import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { BaseToast, ErrorToast, InfoToast } from 'react-native-toast-message';

// Custom toast configurations
export const toastConfig = {
  // Success toast
  success: (props) => (
    <BaseToast
      {...props}
      style={styles.successToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      text1NumberOfLines={2}
      text2NumberOfLines={3}
    />
  ),

  // Error toast
  error: (props) => (
    <ErrorToast
      {...props}
      style={styles.errorToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      text1NumberOfLines={2}
      text2NumberOfLines={3}
    />
  ),

  // Info toast
  info: (props) => (
    <InfoToast
      {...props}
      style={styles.infoToast}
      contentContainerStyle={styles.contentContainer}
      text1Style={styles.text1}
      text2Style={styles.text2}
      text1NumberOfLines={2}
      text2NumberOfLines={3}
    />
  ),

  // Custom warning toast
  warning: ({ text1, text2, ...rest }) => (
    <View style={styles.warningToast}>
      <View style={styles.warningContent}>
        <Text style={styles.warningText1}>{text1}</Text>
        {text2 && <Text style={styles.warningText2}>{text2}</Text>}
      </View>
    </View>
  ),

  // Custom task notification toast
  taskNotification: ({ text1, text2, onPress, ...rest }) => (
    <View style={styles.taskToast}>
      <View style={styles.taskContent}>
        <Text style={styles.taskText1}>{text1}</Text>
        {text2 && <Text style={styles.taskText2}>{text2}</Text>}
      </View>
    </View>
  ),
};

const styles = StyleSheet.create({
  // Success toast styles
  successToast: {
    borderLeftColor: '#10b981',
    borderLeftWidth: 5,
    backgroundColor: '#f0fdf4',
    borderRadius: 8,
    marginHorizontal: 16,
  },

  // Error toast styles
  errorToast: {
    borderLeftColor: '#ef4444',
    borderLeftWidth: 5,
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    marginHorizontal: 16,
  },

  // Info toast styles
  infoToast: {
    borderLeftColor: '#3b82f6',
    borderLeftWidth: 5,
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    marginHorizontal: 16,
  },

  // Warning toast styles
  warningToast: {
    backgroundColor: '#fef3c7',
    borderLeftColor: '#f59e0b',
    borderLeftWidth: 5,
    borderRadius: 8,
    marginHorizontal: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },

  warningContent: {
    flex: 1,
  },

  warningText1: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400e',
    marginBottom: 4,
  },

  warningText2: {
    fontSize: 14,
    color: '#b45309',
    lineHeight: 20,
  },

  // Task notification toast styles
  taskToast: {
    backgroundColor: '#e0f2fe',
    borderLeftColor: '#0891b2',
    borderLeftWidth: 5,
    borderRadius: 8,
    marginHorizontal: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },

  taskContent: {
    flex: 1,
  },

  taskText1: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0e7490',
    marginBottom: 4,
  },

  taskText2: {
    fontSize: 14,
    color: '#155e75',
    lineHeight: 20,
  },

  // Common styles
  contentContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },

  text1: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },

  text2: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
});
