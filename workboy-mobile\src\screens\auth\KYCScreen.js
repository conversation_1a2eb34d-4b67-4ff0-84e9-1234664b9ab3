import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  IconButton,
  ProgressBar,
  Chip,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useForm, Controller } from 'react-hook-form';
import { useAuth } from '@context/AuthContext';
import { colors, spacing, typography, borderRadius } from '@constants/theme';
import { showToast } from '@utils/toast';

const KYCScreen = ({ navigation }) => {
  const { submitKYC, userProfile, loading } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [documents, setDocuments] = useState({
    aadhar_front: null,
    aadhar_back: null,
    pan_card: null,
    profile_photo: null,
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      aadhar_number: '',
      pan_number: '',
      bank_account_number: '',
      bank_ifsc_code: '',
      bank_account_holder_name: '',
      emergency_contact_name: '',
      emergency_contact_phone: '',
      address_line_1: '',
      address_line_2: '',
      city: '',
      state: '',
      postal_code: '',
    },
  });

  const totalSteps = 4;
  const progress = currentStep / totalSteps;

  const pickImage = async (documentType) => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to upload documents.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setDocuments(prev => ({
          ...prev,
          [documentType]: result.assets[0],
        }));
      }
    } catch (error) {
      console.error('Image picker error:', error);
      showToast('Failed to pick image', 'error');
    }
  };

  const takePhoto = async (documentType) => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera permissions to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setDocuments(prev => ({
          ...prev,
          [documentType]: result.assets[0],
        }));
      }
    } catch (error) {
      console.error('Camera error:', error);
      showToast('Failed to take photo', 'error');
    }
  };

  const showImagePicker = (documentType) => {
    Alert.alert(
      'Select Image',
      'Choose how you want to add the document',
      [
        { text: 'Camera', onPress: () => takePhoto(documentType) },
        { text: 'Gallery', onPress: () => pickImage(documentType) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const validateStep = () => {
    const watchedValues = watch();
    
    switch (currentStep) {
      case 1:
        return watchedValues.aadhar_number && documents.aadhar_front && documents.aadhar_back;
      case 2:
        return watchedValues.pan_number && documents.pan_card;
      case 3:
        return watchedValues.bank_account_number && watchedValues.bank_ifsc_code && watchedValues.bank_account_holder_name;
      case 4:
        return watchedValues.emergency_contact_name && watchedValues.emergency_contact_phone && 
               watchedValues.address_line_1 && watchedValues.city && watchedValues.state && watchedValues.postal_code;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (validateStep()) {
      if (currentStep < totalSteps) {
        setCurrentStep(currentStep + 1);
      } else {
        handleSubmitKYC();
      }
    } else {
      showToast('Please complete all required fields', 'warning');
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmitKYC = async () => {
    try {
      const formData = watch();
      
      const kycData = {
        ...formData,
        documents,
      };

      await submitKYC(kycData);
      showToast('KYC submitted successfully! We will review your documents within 24-48 hours.', 'success');
    } catch (error) {
      showToast(error.message || 'Failed to submit KYC', 'error');
    }
  };

  const renderDocumentUpload = (documentType, title, description) => (
    <Card style={styles.documentCard}>
      <Card.Content style={styles.documentContent}>
        <View style={styles.documentHeader}>
          <Text style={styles.documentTitle}>{title}</Text>
          <Text style={styles.documentDescription}>{description}</Text>
        </View>
        
        {documents[documentType] ? (
          <View style={styles.documentUploaded}>
            <Ionicons name="checkmark-circle" size={24} color={colors.success[500]} />
            <Text style={styles.documentUploadedText}>Document uploaded</Text>
            <Button
              mode="text"
              onPress={() => showImagePicker(documentType)}
              labelStyle={styles.changeDocumentText}
            >
              Change
            </Button>
          </View>
        ) : (
          <Button
            mode="outlined"
            onPress={() => showImagePicker(documentType)}
            style={styles.uploadButton}
            icon="camera"
          >
            Upload Document
          </Button>
        )}
      </Card.Content>
    </Card>
  );

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Aadhar Card Verification</Text>
            <Text style={styles.stepDescription}>
              Please provide your Aadhar card details and upload clear photos
            </Text>

            <Controller
              control={control}
              name="aadhar_number"
              rules={{ 
                required: 'Aadhar number is required',
                pattern: {
                  value: /^\d{12}$/,
                  message: 'Aadhar number must be 12 digits'
                }
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="Aadhar Number"
                  mode="outlined"
                  placeholder="Enter 12-digit Aadhar number"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={!!errors.aadhar_number}
                  keyboardType="numeric"
                  maxLength={12}
                  style={styles.input}
                />
              )}
            />
            {errors.aadhar_number && (
              <Text style={styles.errorText}>{errors.aadhar_number.message}</Text>
            )}

            {renderDocumentUpload('aadhar_front', 'Aadhar Card Front', 'Upload the front side of your Aadhar card')}
            {renderDocumentUpload('aadhar_back', 'Aadhar Card Back', 'Upload the back side of your Aadhar card')}
          </View>
        );

      case 2:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>PAN Card Verification</Text>
            <Text style={styles.stepDescription}>
              Please provide your PAN card details and upload a clear photo
            </Text>

            <Controller
              control={control}
              name="pan_number"
              rules={{ 
                required: 'PAN number is required',
                pattern: {
                  value: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
                  message: 'Invalid PAN number format'
                }
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="PAN Number"
                  mode="outlined"
                  placeholder="Enter PAN number (e.g., **********)"
                  value={value}
                  onChangeText={(text) => onChange(text.toUpperCase())}
                  onBlur={onBlur}
                  error={!!errors.pan_number}
                  maxLength={10}
                  style={styles.input}
                />
              )}
            />
            {errors.pan_number && (
              <Text style={styles.errorText}>{errors.pan_number.message}</Text>
            )}

            {renderDocumentUpload('pan_card', 'PAN Card', 'Upload a clear photo of your PAN card')}
          </View>
        );

      case 3:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Bank Account Details</Text>
            <Text style={styles.stepDescription}>
              Please provide your bank account details for payments
            </Text>

            <Controller
              control={control}
              name="bank_account_holder_name"
              rules={{ required: 'Account holder name is required' }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="Account Holder Name"
                  mode="outlined"
                  placeholder="Enter account holder name"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={!!errors.bank_account_holder_name}
                  style={styles.input}
                />
              )}
            />

            <Controller
              control={control}
              name="bank_account_number"
              rules={{ required: 'Account number is required' }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="Account Number"
                  mode="outlined"
                  placeholder="Enter bank account number"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={!!errors.bank_account_number}
                  keyboardType="numeric"
                  style={styles.input}
                />
              )}
            />

            <Controller
              control={control}
              name="bank_ifsc_code"
              rules={{ 
                required: 'IFSC code is required',
                pattern: {
                  value: /^[A-Z]{4}0[A-Z0-9]{6}$/,
                  message: 'Invalid IFSC code format'
                }
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="IFSC Code"
                  mode="outlined"
                  placeholder="Enter IFSC code"
                  value={value}
                  onChangeText={(text) => onChange(text.toUpperCase())}
                  onBlur={onBlur}
                  error={!!errors.bank_ifsc_code}
                  maxLength={11}
                  style={styles.input}
                />
              )}
            />
          </View>
        );

      case 4:
        return (
          <View style={styles.stepContainer}>
            <Text style={styles.stepTitle}>Contact & Address Details</Text>
            <Text style={styles.stepDescription}>
              Please provide your contact and address information
            </Text>

            <Text style={styles.sectionTitle}>Emergency Contact</Text>
            
            <Controller
              control={control}
              name="emergency_contact_name"
              rules={{ required: 'Emergency contact name is required' }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="Emergency Contact Name"
                  mode="outlined"
                  placeholder="Enter emergency contact name"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={!!errors.emergency_contact_name}
                  style={styles.input}
                />
              )}
            />

            <Controller
              control={control}
              name="emergency_contact_phone"
              rules={{ 
                required: 'Emergency contact phone is required',
                pattern: {
                  value: /^[+]?[\d\s\-\(\)]{10,}$/,
                  message: 'Invalid phone number'
                }
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="Emergency Contact Phone"
                  mode="outlined"
                  placeholder="Enter emergency contact phone"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={!!errors.emergency_contact_phone}
                  keyboardType="phone-pad"
                  style={styles.input}
                />
              )}
            />

            <Text style={styles.sectionTitle}>Address</Text>

            <Controller
              control={control}
              name="address_line_1"
              rules={{ required: 'Address is required' }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="Address Line 1"
                  mode="outlined"
                  placeholder="Enter address line 1"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={!!errors.address_line_1}
                  style={styles.input}
                />
              )}
            />

            <Controller
              control={control}
              name="address_line_2"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="Address Line 2 (Optional)"
                  mode="outlined"
                  placeholder="Enter address line 2"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  style={styles.input}
                />
              )}
            />

            <View style={styles.row}>
              <Controller
                control={control}
                name="city"
                rules={{ required: 'City is required' }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label="City"
                    mode="outlined"
                    placeholder="Enter city"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={!!errors.city}
                    style={[styles.input, { flex: 1, marginRight: spacing.md }]}
                  />
                )}
              />

              <Controller
                control={control}
                name="state"
                rules={{ required: 'State is required' }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label="State"
                    mode="outlined"
                    placeholder="Enter state"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={!!errors.state}
                    style={[styles.input, { flex: 1 }]}
                  />
                )}
              />
            </View>

            <Controller
              control={control}
              name="postal_code"
              rules={{ 
                required: 'Postal code is required',
                pattern: {
                  value: /^\d{6}$/,
                  message: 'Postal code must be 6 digits'
                }
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="Postal Code"
                  mode="outlined"
                  placeholder="Enter postal code"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={!!errors.postal_code}
                  keyboardType="numeric"
                  maxLength={6}
                  style={styles.input}
                />
              )}
            />
          </View>
        );

      default:
        return null;
    }
  };

  if (userProfile?.kyc_status === 'pending') {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
        
        <View style={styles.pendingContainer}>
          <Ionicons name="time-outline" size={64} color={colors.warning[500]} />
          <Text style={styles.pendingTitle}>KYC Under Review</Text>
          <Text style={styles.pendingDescription}>
            Your KYC documents are being reviewed. We will notify you once the verification is complete.
            This usually takes 24-48 hours.
          </Text>
          
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          >
            Go Back
          </Button>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          iconColor={colors.gray[600]}
          onPress={() => navigation.goBack()}
        />
        <Text style={styles.headerTitle}>KYC Verification</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Progress */}
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressText}>Step {currentStep} of {totalSteps}</Text>
          <Text style={styles.progressPercentage}>{Math.round(progress * 100)}%</Text>
        </View>
        <ProgressBar progress={progress} color={colors.primary[600]} style={styles.progressBar} />
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {renderStep()}
        </ScrollView>

        {/* Navigation Buttons */}
        <View style={styles.navigationContainer}>
          {currentStep > 1 && (
            <Button
              mode="outlined"
              onPress={handlePrevious}
              style={[styles.navButton, { marginRight: spacing.md }]}
            >
              Previous
            </Button>
          )}
          
          <Button
            mode="contained"
            onPress={handleNext}
            loading={loading}
            disabled={loading || !validateStep()}
            style={[styles.navButton, { flex: 1 }]}
          >
            {currentStep === totalSteps ? 'Submit KYC' : 'Next'}
          </Button>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
  },
  progressContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.gray[50],
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  progressText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
  },
  progressPercentage: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.primary[600],
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.gray[200],
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  stepContainer: {
    padding: spacing.lg,
  },
  stepTitle: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.sm,
  },
  stepDescription: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginTop: spacing.lg,
    marginBottom: spacing.md,
  },
  input: {
    backgroundColor: colors.white,
    marginBottom: spacing.md,
  },
  errorText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.error[600],
    marginTop: -spacing.sm,
    marginBottom: spacing.md,
  },
  row: {
    flexDirection: 'row',
  },
  documentCard: {
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
  },
  documentContent: {
    paddingVertical: spacing.md,
  },
  documentHeader: {
    marginBottom: spacing.md,
  },
  documentTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  documentDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
  },
  documentUploaded: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  documentUploadedText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.success[600],
    flex: 1,
    marginLeft: spacing.sm,
  },
  changeDocumentText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.primary[600],
  },
  uploadButton: {
    borderColor: colors.primary[600],
    borderRadius: borderRadius.lg,
  },
  navigationContainer: {
    flexDirection: 'row',
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  navButton: {
    borderRadius: borderRadius.lg,
  },
  pendingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  pendingTitle: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginTop: spacing.lg,
    marginBottom: spacing.md,
  },
  pendingDescription: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: typography.lineHeight.base,
    marginBottom: spacing.xl,
  },
  backButton: {
    borderColor: colors.gray[300],
    borderRadius: borderRadius.lg,
  },
});

export default KYCScreen;
