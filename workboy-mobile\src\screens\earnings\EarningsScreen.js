import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Chip,
  Divider,
  ActivityIndicator,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '@context/AuthContext';
import { apiService } from '@services/apiService';
import { showToast } from '@utils/toast';

const { width } = Dimensions.get('window');

const EarningsScreen = ({ navigation }) => {
  const [earnings, setEarnings] = useState([]);
  const [stats, setStats] = useState({
    totalEarnings: 0,
    thisWeek: 0,
    thisMonth: 0,
    pendingAmount: 0,
    availableBalance: 0,
    completedTasks: 0,
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    loadEarningsData();
  }, []);

  const loadEarningsData = async () => {
    try {
      setLoading(true);
      const [earningsResponse, statsResponse] = await Promise.all([
        apiService.earnings.getEarnings(),
        apiService.earnings.getEarningsStats(),
      ]);

      setEarnings(earningsResponse.data || []);
      setStats(statsResponse.data || stats);
    } catch (error) {
      console.error('Error loading earnings:', error);
      showToast('error', 'Error', 'Failed to load earnings data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadEarningsData();
    setRefreshing(false);
  };

  const handleWithdraw = () => {
    navigation.navigate('Withdrawal');
  };

  const handleEarningsDetail = (earning) => {
    navigation.navigate('EarningsDetail', { earning });
  };

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#10b981';
      case 'pending':
        return '#f59e0b';
      case 'processing':
        return '#3b82f6';
      default:
        return '#6b7280';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'pending':
        return 'time';
      case 'processing':
        return 'sync';
      default:
        return 'help-circle';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#10b981" />
          <Text style={styles.loadingText}>Loading earnings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Title style={styles.headerTitle}>Earnings</Title>
          <Paragraph style={styles.headerSubtitle}>
            Track your income and manage withdrawals
          </Paragraph>
        </View>

        {/* Balance Card */}
        <Card style={styles.balanceCard}>
          <LinearGradient
            colors={['#10b981', '#059669']}
            style={styles.balanceGradient}
          >
            <View style={styles.balanceContent}>
              <Text style={styles.balanceLabel}>Available Balance</Text>
              <Text style={styles.balanceAmount}>
                {formatCurrency(stats.availableBalance)}
              </Text>
              <Button
                mode="contained"
                onPress={handleWithdraw}
                style={styles.withdrawButton}
                labelStyle={styles.withdrawButtonText}
                disabled={stats.availableBalance <= 0}
              >
                Withdraw
              </Button>
            </View>
          </LinearGradient>
        </Card>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Ionicons name="wallet" size={24} color="#10b981" />
              <Text style={styles.statValue}>{formatCurrency(stats.totalEarnings)}</Text>
              <Text style={styles.statLabel}>Total Earned</Text>
            </Card.Content>
          </Card>

          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Ionicons name="calendar" size={24} color="#3b82f6" />
              <Text style={styles.statValue}>{formatCurrency(stats.thisWeek)}</Text>
              <Text style={styles.statLabel}>This Week</Text>
            </Card.Content>
          </Card>

          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Ionicons name="time" size={24} color="#f59e0b" />
              <Text style={styles.statValue}>{formatCurrency(stats.pendingAmount)}</Text>
              <Text style={styles.statLabel}>Pending</Text>
            </Card.Content>
          </Card>

          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Ionicons name="checkmark-done" size={24} color="#8b5cf6" />
              <Text style={styles.statValue}>{stats.completedTasks}</Text>
              <Text style={styles.statLabel}>Tasks Done</Text>
            </Card.Content>
          </Card>
        </View>

        {/* Recent Earnings */}
        <Card style={styles.earningsCard}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Title style={styles.sectionTitle}>Recent Earnings</Title>
              <TouchableOpacity onPress={() => navigation.navigate('EarningsDetail')}>
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            </View>

            {earnings.length === 0 ? (
              <View style={styles.emptyState}>
                <Ionicons name="wallet-outline" size={48} color="#9ca3af" />
                <Text style={styles.emptyStateText}>No earnings yet</Text>
                <Text style={styles.emptyStateSubtext}>
                  Complete tasks to start earning money
                </Text>
              </View>
            ) : (
              earnings.slice(0, 5).map((earning, index) => (
                <TouchableOpacity
                  key={earning.id || index}
                  style={styles.earningItem}
                  onPress={() => handleEarningsDetail(earning)}
                >
                  <View style={styles.earningLeft}>
                    <View style={styles.earningIcon}>
                      <Ionicons
                        name={getStatusIcon(earning.status)}
                        size={20}
                        color={getStatusColor(earning.status)}
                      />
                    </View>
                    <View style={styles.earningDetails}>
                      <Text style={styles.earningTitle}>
                        {earning.taskTitle || 'Task Completion'}
                      </Text>
                      <Text style={styles.earningDate}>
                        {new Date(earning.date || Date.now()).toLocaleDateString()}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.earningRight}>
                    <Text style={styles.earningAmount}>
                      {formatCurrency(earning.amount)}
                    </Text>
                    <Chip
                      style={[
                        styles.statusChip,
                        { backgroundColor: getStatusColor(earning.status) + '20' },
                      ]}
                      textStyle={[
                        styles.statusChipText,
                        { color: getStatusColor(earning.status) },
                      ]}
                    >
                      {earning.status || 'pending'}
                    </Chip>
                  </View>
                </TouchableOpacity>
              ))
            )}
          </Card.Content>
        </Card>

        {/* Quick Actions */}
        <Card style={styles.actionsCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Quick Actions</Title>
            <View style={styles.actionsGrid}>
              <TouchableOpacity
                style={styles.actionItem}
                onPress={() => navigation.navigate('EarningsDetail')}
              >
                <Ionicons name="list" size={24} color="#10b981" />
                <Text style={styles.actionText}>View History</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionItem}
                onPress={handleWithdraw}
              >
                <Ionicons name="card" size={24} color="#3b82f6" />
                <Text style={styles.actionText}>Withdraw</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionItem}
                onPress={() => navigation.navigate('Settings')}
              >
                <Ionicons name="settings" size={24} color="#8b5cf6" />
                <Text style={styles.actionText}>Settings</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionItem}
                onPress={() => navigation.navigate('Help')}
              >
                <Ionicons name="help-circle" size={24} color="#f59e0b" />
                <Text style={styles.actionText}>Help</Text>
              </TouchableOpacity>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 4,
  },
  balanceCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
  },
  balanceGradient: {
    padding: 24,
  },
  balanceContent: {
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.9,
  },
  balanceAmount: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#ffffff',
    marginVertical: 8,
  },
  withdrawButton: {
    backgroundColor: '#ffffff',
    marginTop: 16,
    paddingHorizontal: 24,
  },
  withdrawButtonText: {
    color: '#10b981',
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statCard: {
    width: (width - 60) / 2,
    marginBottom: 16,
    marginHorizontal: 5,
    borderRadius: 12,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  earningsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  viewAllText: {
    fontSize: 14,
    color: '#10b981',
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6b7280',
    marginTop: 12,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9ca3af',
    marginTop: 4,
    textAlign: 'center',
  },
  earningItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  earningLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  earningIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  earningDetails: {
    flex: 1,
  },
  earningTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  earningDate: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  earningRight: {
    alignItems: 'flex-end',
  },
  earningAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  statusChip: {
    marginTop: 4,
    height: 24,
  },
  statusChipText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  actionsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  actionItem: {
    width: (width - 80) / 2,
    alignItems: 'center',
    paddingVertical: 16,
    marginBottom: 16,
  },
  actionText: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 8,
    fontWeight: '500',
  },
});

export default EarningsScreen;
