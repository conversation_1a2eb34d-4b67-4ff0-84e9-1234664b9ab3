<?php

namespace App\Controllers\Api\V1;

use App\Controllers\Api\BaseApiController;
use App\Models\PaymentModel;
use App\Models\TaskModel;

/**
 * Payment Controller for Work-Boy Booking API
 * 
 * Handles payment processing, Razorpay integration, and transaction management
 */
class PaymentController extends BaseApiController
{
    protected $paymentModel;
    protected $taskModel;

    public function __construct()
    {
        parent::__construct();
        $this->paymentModel = new PaymentModel();
        $this->taskModel = new TaskModel();
    }

    /**
     * Create payment intent
     * POST /api/v1/payments/create
     */
    public function create()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $rules = [
                'task_id'    => 'required|integer',
                'amount'     => 'required|decimal|greater_than[0]',
                'tip_amount' => 'permit_empty|decimal|greater_than_equal_to[0]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Verify task belongs to customer and is completed
            $task = $this->taskModel->where('id', $data['task_id'])
                                  ->where('customer_id', $currentUser['id'])
                                  ->first();

            if (!$task) {
                return $this->notFoundResponse('Task not found');
            }

            if ($task['status'] !== 'completed') {
                return $this->errorResponse('Payment can only be made for completed tasks', 400);
            }

            // Check if payment already exists
            $existingPayment = $this->paymentModel->getByTaskId($data['task_id']);
            if ($existingPayment) {
                return $this->errorResponse('Payment already exists for this task', 400);
            }

            // Create payment record
            $paymentData = [
                'task_id'        => $data['task_id'],
                'customer_id'    => $currentUser['id'],
                'workboy_id'     => $task['workboy_id'],
                'amount'         => $data['amount'],
                'tip_amount'     => $data['tip_amount'] ?? 0,
                'payment_status' => 'pending',
            ];

            $paymentId = $this->paymentModel->insert($paymentData);

            if (!$paymentId) {
                return $this->serverErrorResponse('Failed to create payment');
            }

            // In a real implementation, you would integrate with Razorpay here
            // For now, we'll simulate the payment creation
            $razorpayOrderId = 'order_' . uniqid();
            
            // Update payment with gateway ID
            $this->paymentModel->update($paymentId, [
                'payment_gateway_id' => $razorpayOrderId,
                'payment_method' => 'razorpay',
            ]);

            $payment = $this->paymentModel->find($paymentId);

            $this->logActivity('payment_created', [
                'payment_id' => $paymentId,
                'task_id' => $data['task_id'],
                'amount' => $data['amount']
            ]);

            return $this->apiResponse([
                'payment' => $payment,
                'razorpay_order_id' => $razorpayOrderId,
                'razorpay_key_id' => env('RAZORPAY_KEY_ID'),
            ], 'Payment created successfully', 201);

        } catch (\Exception $e) {
            log_message('error', 'Create payment error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to create payment');
        }
    }

    /**
     * Confirm payment
     * POST /api/v1/payments/confirm
     */
    public function confirm()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $rules = [
                'payment_id'         => 'required|string',
                'payment_gateway_id' => 'required|string',
                'signature'          => 'permit_empty|string',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Find payment by gateway ID
            $payment = $this->paymentModel->where('payment_gateway_id', $data['payment_gateway_id'])
                                        ->where('customer_id', $currentUser['id'])
                                        ->first();

            if (!$payment) {
                return $this->notFoundResponse('Payment not found');
            }

            if ($payment['payment_status'] !== 'pending') {
                return $this->errorResponse('Payment already processed', 400);
            }

            // In a real implementation, you would verify the payment with Razorpay
            // For now, we'll simulate successful payment
            $gatewayResponse = [
                'razorpay_payment_id' => $data['payment_id'],
                'razorpay_order_id' => $data['payment_gateway_id'],
                'razorpay_signature' => $data['signature'] ?? '',
                'status' => 'captured',
                'method' => 'card',
                'confirmed_at' => date('Y-m-d H:i:s'),
            ];

            // Update payment status
            $updated = $this->paymentModel->updatePaymentStatus(
                $payment['id'],
                'completed',
                $gatewayResponse
            );

            if (!$updated) {
                return $this->serverErrorResponse('Failed to confirm payment');
            }

            // Get updated payment with details
            $updatedPayment = $this->paymentModel->getPaymentWithDetails($payment['id']);

            $this->logActivity('payment_confirmed', [
                'payment_id' => $payment['id'],
                'task_id' => $payment['task_id'],
                'amount' => $payment['amount'] + $payment['tip_amount']
            ]);

            return $this->apiResponse($updatedPayment, 'Payment confirmed successfully');

        } catch (\Exception $e) {
            log_message('error', 'Confirm payment error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to confirm payment');
        }
    }

    /**
     * Get payment history
     * GET /api/v1/payments/history
     */
    public function getHistory()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $pagination = $this->getPaginationParams();
            $status = $this->request->getGet('status');

            $builder = $this->paymentModel->select('payments.*, tasks.title as task_title')
                                        ->join('tasks', 'tasks.id = payments.task_id');

            // Filter based on user type
            if ($currentUser['user_type'] === 'customer') {
                $builder->where('payments.customer_id', $currentUser['id']);
            } elseif ($currentUser['user_type'] === 'workboy') {
                $builder->where('payments.workboy_id', $currentUser['id']);
            }
            // Admin can see all payments

            // Apply status filter
            if ($status) {
                $builder->where('payments.payment_status', $status);
            }

            // Get total count for pagination
            $total = $builder->countAllResults(false);

            // Get payments
            $payments = $builder->orderBy('payments.created_at', 'DESC')
                              ->limit($pagination['limit'], $pagination['offset'])
                              ->get()
                              ->getResultArray();

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($payments, 'Payment history retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Get payment history error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve payment history');
        }
    }

    /**
     * Handle Razorpay webhook
     * POST /api/v1/payments/webhook
     */
    public function webhook()
    {
        try {
            // Verify webhook signature (in production)
            $webhookSecret = env('RAZORPAY_WEBHOOK_SECRET');
            $signature = $this->request->getHeader('X-Razorpay-Signature');
            
            // Get raw POST data
            $payload = file_get_contents('php://input');
            
            // In production, verify the signature:
            // $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);
            // if (!hash_equals($expectedSignature, $signature)) {
            //     return $this->unauthorizedResponse('Invalid signature');
            // }

            $data = json_decode($payload, true);

            if (!$data || !isset($data['event'])) {
                return $this->errorResponse('Invalid webhook data', 400);
            }

            $event = $data['event'];
            $paymentData = $data['payload']['payment']['entity'] ?? null;

            if (!$paymentData) {
                return $this->errorResponse('Invalid payment data', 400);
            }

            // Find payment by gateway ID
            $payment = $this->paymentModel->where('payment_gateway_id', $paymentData['order_id'])
                                        ->first();

            if (!$payment) {
                log_message('warning', 'Webhook received for unknown payment: ' . $paymentData['order_id']);
                return $this->apiResponse(null, 'Payment not found');
            }

            // Handle different webhook events
            switch ($event) {
                case 'payment.captured':
                    $this->paymentModel->updatePaymentStatus(
                        $payment['id'],
                        'completed',
                        $paymentData
                    );
                    break;

                case 'payment.failed':
                    $this->paymentModel->updatePaymentStatus(
                        $payment['id'],
                        'failed',
                        $paymentData
                    );
                    break;

                default:
                    log_message('info', 'Unhandled webhook event: ' . $event);
                    break;
            }

            return $this->apiResponse(null, 'Webhook processed successfully');

        } catch (\Exception $e) {
            log_message('error', 'Webhook processing error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to process webhook');
        }
    }

    /**
     * Get payment statistics (Admin only)
     * GET /api/v1/payments/stats
     */
    public function getStats()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');

            $filters = [];
            if ($dateFrom) $filters['date_from'] = $dateFrom;
            if ($dateTo) $filters['date_to'] = $dateTo;

            // Get payment statistics
            $stats = $this->paymentModel->getPaymentStats($filters);

            // Get daily revenue for the last 30 days
            $dailyRevenue = $this->paymentModel->getDailyRevenue(30);

            // Get payment method distribution
            $paymentMethods = $this->paymentModel->getPaymentMethodStats();

            $statsData = [
                'summary' => $stats,
                'daily_revenue' => $dailyRevenue,
                'payment_methods' => $paymentMethods,
            ];

            return $this->apiResponse($statsData, 'Payment statistics retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get payment stats error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve payment statistics');
        }
    }
}
