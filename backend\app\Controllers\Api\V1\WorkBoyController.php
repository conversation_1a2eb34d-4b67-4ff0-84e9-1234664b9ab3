<?php

namespace App\Controllers\Api\V1;

use App\Controllers\Api\BaseApiController;
use App\Models\WorkboyProfileModel;
use App\Models\TaskModel;
use App\Models\PaymentModel;
use App\Models\ReviewModel;

/**
 * Work-Boy Controller for Work-Boy Booking API
 * 
 * Handles Work-Boy specific operations including dashboard, KYC, and task management
 */
class WorkBoyController extends BaseApiController
{
    protected $workboyProfileModel;
    protected $taskModel;
    protected $paymentModel;
    protected $reviewModel;

    public function __construct()
    {
        parent::__construct();
        $this->workboyProfileModel = new WorkboyProfileModel();
        $this->taskModel = new TaskModel();
        $this->paymentModel = new PaymentModel();
        $this->reviewModel = new ReviewModel();
    }

    /**
     * Get Work-Boy dashboard
     * GET /api/v1/workboy/dashboard
     */
    public function dashboard()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'workboy') {
                return $this->forbiddenResponse('Work-Boy access required');
            }

            $workboyId = $currentUser['id'];

            // Get Work-Boy profile with stats
            $profile = $this->workboyProfileModel->getWorkBoyWithStats($workboyId);

            // Get available tasks
            $availableTasks = $this->taskModel->getAvailableTasks(5);

            // Get upcoming assigned tasks
            $upcomingTasks = $this->taskModel->getUpcomingTasks($workboyId, 'workboy', 3);

            // Get earnings summary
            $earningsToday = $this->paymentModel->getWorkBoyEarnings($workboyId, 'today');
            $earningsWeek = $this->paymentModel->getWorkBoyEarnings($workboyId, 'week');
            $earningsMonth = $this->paymentModel->getWorkBoyEarnings($workboyId, 'month');
            $earningsTotal = $this->paymentModel->getWorkBoyEarnings($workboyId, 'all');

            // Get recent reviews
            $recentReviews = $this->reviewModel->getByWorkBoyId($workboyId, 3);

            // Get task statistics
            $taskStats = $this->taskModel->getTaskStats(['workboy_id' => $workboyId]);

            $dashboardData = [
                'profile' => $profile,
                'available_tasks' => $availableTasks,
                'upcoming_tasks' => $upcomingTasks,
                'earnings_summary' => [
                    'today' => $earningsToday['total_earnings'] ?? 0,
                    'this_week' => $earningsWeek['total_earnings'] ?? 0,
                    'this_month' => $earningsMonth['total_earnings'] ?? 0,
                    'total' => $earningsTotal['total_earnings'] ?? 0,
                ],
                'performance_stats' => [
                    'rating' => $profile['rating'] ?? 0,
                    'total_tasks' => $profile['total_tasks_completed'] ?? 0,
                    'completion_rate' => $taskStats['completed_tasks'] > 0 ? 
                        round(($taskStats['completed_tasks'] / $taskStats['total_tasks']) * 100, 2) : 0,
                    'total_reviews' => $profile['total_reviews'] ?? 0,
                ],
                'recent_reviews' => $recentReviews,
            ];

            return $this->apiResponse($dashboardData, 'Dashboard data retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Work-Boy dashboard error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve dashboard data');
        }
    }

    /**
     * Update Work-Boy profile
     * PUT /api/v1/workboy/profile
     */
    public function updateProfile()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'workboy') {
                return $this->forbiddenResponse('Work-Boy access required');
            }

            $rules = [
                'skills'              => 'permit_empty|valid_json',
                'hourly_rate'         => 'permit_empty|decimal|greater_than[0]',
                'availability_status' => 'permit_empty|in_list[available,busy,offline]',
                'bank_details'        => 'permit_empty|valid_json',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            $updateData = [];
            $allowedFields = ['skills', 'hourly_rate', 'availability_status', 'bank_details'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    if (in_array($field, ['skills', 'bank_details']) && is_array($data[$field])) {
                        $updateData[$field] = json_encode($data[$field]);
                    } else {
                        $updateData[$field] = $data[$field];
                    }
                }
            }

            if (empty($updateData)) {
                return $this->errorResponse('No valid fields to update', 400);
            }

            $updated = $this->workboyProfileModel->where('user_id', $currentUser['id'])
                                                ->set($updateData)
                                                ->update();

            if (!$updated) {
                return $this->serverErrorResponse('Failed to update profile');
            }

            // Get updated profile
            $profile = $this->workboyProfileModel->getByUserId($currentUser['id']);

            $this->logActivity('workboy_profile_updated', [
                'updated_fields' => array_keys($updateData)
            ]);

            return $this->apiResponse($profile, 'Profile updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Update Work-Boy profile error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to update profile');
        }
    }

    /**
     * Submit KYC documents
     * POST /api/v1/workboy/kyc
     */
    public function submitKYC()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'workboy') {
                return $this->forbiddenResponse('Work-Boy access required');
            }

            $rules = [
                'documents' => 'required|valid_json',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);
            $documents = $data['documents'];

            // Validate required documents
            $requiredDocs = ['id_proof', 'address_proof', 'photo'];
            foreach ($requiredDocs as $doc) {
                if (!isset($documents[$doc]) || empty($documents[$doc])) {
                    return $this->errorResponse("Missing required document: {$doc}", 400);
                }
            }

            $updated = $this->workboyProfileModel->submitKYC($currentUser['id'], $documents);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to submit KYC documents');
            }

            $this->logActivity('kyc_submitted', ['user_id' => $currentUser['id']]);

            return $this->apiResponse(null, 'KYC documents submitted successfully. Awaiting admin approval.');

        } catch (\Exception $e) {
            log_message('error', 'Submit KYC error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to submit KYC documents');
        }
    }

    /**
     * Get available tasks for Work-Boy
     * GET /api/v1/workboy/available-tasks
     */
    public function getAvailableTasks()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'workboy') {
                return $this->forbiddenResponse('Work-Boy access required');
            }

            // Check if Work-Boy is approved and available
            $profile = $this->workboyProfileModel->getByUserId($currentUser['id']);
            
            if (!$profile || $profile['kyc_status'] !== 'approved') {
                return $this->errorResponse('KYC approval required to view tasks', 403);
            }

            if ($profile['availability_status'] !== 'available') {
                return $this->errorResponse('You must be available to view tasks', 400);
            }

            $pagination = $this->getPaginationParams();
            $categoryId = $this->request->getGet('category_id');
            $latitude = $this->request->getGet('location_lat');
            $longitude = $this->request->getGet('location_lng');
            $radius = $this->request->getGet('radius') ?? 10;

            $builder = $this->taskModel->select('tasks.*, 
                                               task_categories.name as category_name, 
                                               task_categories.icon as category_icon,
                                               addresses.*, 
                                               users.first_name as customer_first_name, 
                                               users.last_name as customer_last_name')
                                     ->join('task_categories', 'task_categories.id = tasks.category_id', 'left')
                                     ->join('addresses', 'addresses.id = tasks.address_id')
                                     ->join('users', 'users.id = tasks.customer_id')
                                     ->where('tasks.status', 'pending')
                                     ->where('tasks.scheduled_date >=', date('Y-m-d'));

            // Filter by category if specified
            if ($categoryId) {
                $builder->where('tasks.category_id', $categoryId);
            }

            // Filter by location if coordinates provided
            if ($latitude && $longitude) {
                // Add distance calculation and filter
                $builder->select('*, (6371 * acos(cos(radians(?)) * cos(radians(addresses.latitude)) * 
                                 cos(radians(addresses.longitude) - radians(?)) + sin(radians(?)) * 
                                 sin(radians(addresses.latitude)))) AS distance', false)
                       ->having('distance <', $radius)
                       ->orderBy('distance', 'ASC');
                
                // Bind parameters for distance calculation
                $builder->setBinds([$latitude, $longitude, $latitude]);
            } else {
                $builder->orderBy('tasks.priority', 'DESC')
                       ->orderBy('tasks.created_at', 'ASC');
            }

            // Get total count for pagination
            $total = $builder->countAllResults(false);

            // Get tasks
            $tasks = $builder->limit($pagination['limit'], $pagination['offset'])
                           ->get()
                           ->getResultArray();

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($tasks, 'Available tasks retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Get available tasks error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve available tasks');
        }
    }

    /**
     * Accept a task
     * POST /api/v1/workboy/tasks/{id}/accept
     */
    public function acceptTask($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'workboy') {
                return $this->forbiddenResponse('Work-Boy access required');
            }

            // Check if Work-Boy is approved and available
            $profile = $this->workboyProfileModel->getByUserId($currentUser['id']);
            
            if (!$profile || $profile['kyc_status'] !== 'approved') {
                return $this->errorResponse('KYC approval required', 403);
            }

            if ($profile['availability_status'] !== 'available') {
                return $this->errorResponse('You must be available to accept tasks', 400);
            }

            $task = $this->taskModel->find($id);

            if (!$task) {
                return $this->notFoundResponse('Task not found');
            }

            if ($task['status'] !== 'pending') {
                return $this->errorResponse('Task is no longer available', 400);
            }

            // Assign task to Work-Boy
            $assigned = $this->taskModel->assignTask($id, $currentUser['id']);

            if (!$assigned) {
                return $this->serverErrorResponse('Failed to accept task');
            }

            // Update Work-Boy availability to busy
            $this->workboyProfileModel->updateAvailability($currentUser['id'], 'busy');

            // Get updated task with details
            $updatedTask = $this->taskModel->getTaskWithDetails($id);

            $this->logActivity('task_accepted', ['task_id' => $id]);

            return $this->apiResponse($updatedTask, 'Task accepted successfully');

        } catch (\Exception $e) {
            log_message('error', 'Accept task error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to accept task');
        }
    }

    /**
     * Reject a task
     * POST /api/v1/workboy/tasks/{id}/reject
     */
    public function rejectTask($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'workboy') {
                return $this->forbiddenResponse('Work-Boy access required');
            }

            $task = $this->taskModel->find($id);

            if (!$task) {
                return $this->notFoundResponse('Task not found');
            }

            if ($task['status'] !== 'pending') {
                return $this->errorResponse('Task is no longer available', 400);
            }

            // For now, just log the rejection
            // In a more advanced system, you might track rejections
            $this->logActivity('task_rejected', ['task_id' => $id]);

            return $this->apiResponse(null, 'Task rejected');

        } catch (\Exception $e) {
            log_message('error', 'Reject task error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to reject task');
        }
    }

    /**
     * Get Work-Boy earnings
     * GET /api/v1/workboy/earnings
     */
    public function getEarnings()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'workboy') {
                return $this->forbiddenResponse('Work-Boy access required');
            }

            $period = $this->request->getGet('period') ?? 'all';
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');

            $workboyId = $currentUser['id'];

            // Get earnings summary
            $earnings = $this->paymentModel->getWorkBoyEarnings($workboyId, $period);

            // Get daily earnings for the last 30 days
            $dailyEarnings = $this->paymentModel->select('DATE(created_at) as date, 
                                                         SUM(amount + tip_amount) as earnings,
                                                         COUNT(*) as tasks_completed')
                                               ->where('workboy_id', $workboyId)
                                               ->where('payment_status', 'completed')
                                               ->where('created_at >=', date('Y-m-d', strtotime('-30 days')))
                                               ->groupBy('DATE(created_at)')
                                               ->orderBy('date', 'ASC')
                                               ->findAll();

            // Get recent payments
            $recentPayments = $this->paymentModel->select('payments.*, tasks.title as task_title')
                                                ->join('tasks', 'tasks.id = payments.task_id')
                                                ->where('payments.workboy_id', $workboyId)
                                                ->where('payments.payment_status', 'completed')
                                                ->orderBy('payments.created_at', 'DESC')
                                                ->limit(10)
                                                ->findAll();

            $earningsData = [
                'summary' => $earnings,
                'daily_earnings' => $dailyEarnings,
                'recent_payments' => $recentPayments,
            ];

            return $this->apiResponse($earningsData, 'Earnings data retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get earnings error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve earnings data');
        }
    }
}
