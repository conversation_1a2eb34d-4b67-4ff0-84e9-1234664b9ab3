import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { Card, Button, List } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@constants/theme';

const PaymentDetailScreen = ({ route, navigation }) => {
  const { paymentId } = route.params || {};

  // Mock payment method data
  const paymentMethod = {
    id: paymentId || '1',
    type: 'card',
    last4: '4242',
    brand: 'Visa',
    expiryMonth: '12',
    expiryYear: '2025',
    isDefault: true,
    holderName: '<PERSON> Doe',
  };

  const handleSetDefault = () => {
    Alert.alert(
      'Set as Default',
      'Set this payment method as your default?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Set Default', onPress: () => {
          // Handle set as default
          Alert.alert('Success', 'Payment method set as default');
        }},
      ]
    );
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Payment Method',
      'Are you sure you want to delete this payment method?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => {
          // Handle delete
          navigation.goBack();
        }},
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Payment Method Card */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <Ionicons name="card-outline" size={32} color={colors.primary[600]} />
              <View style={styles.cardInfo}>
                <Text style={styles.cardTitle}>
                  {paymentMethod.brand} •••• {paymentMethod.last4}
                </Text>
                <Text style={styles.cardSubtitle}>
                  Expires {paymentMethod.expiryMonth}/{paymentMethod.expiryYear}
                </Text>
              </View>
              {paymentMethod.isDefault && (
                <Text style={styles.defaultBadge}>Default</Text>
              )}
            </View>
          </Card.Content>
        </Card>

        {/* Payment Method Details */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Details</Text>
            <List.Item
              title="Card Holder"
              description={paymentMethod.holderName}
              left={(props) => (
                <List.Icon
                  {...props}
                  icon={({ size, color }) => (
                    <Ionicons name="person-outline" size={size} color={color} />
                  )}
                />
              )}
            />
            <List.Item
              title="Card Number"
              description={`•••• •••• •••• ${paymentMethod.last4}`}
              left={(props) => (
                <List.Icon
                  {...props}
                  icon={({ size, color }) => (
                    <Ionicons name="card-outline" size={size} color={color} />
                  )}
                />
              )}
            />
            <List.Item
              title="Expiry Date"
              description={`${paymentMethod.expiryMonth}/${paymentMethod.expiryYear}`}
              left={(props) => (
                <List.Icon
                  {...props}
                  icon={({ size, color }) => (
                    <Ionicons name="calendar-outline" size={size} color={color} />
                  )}
                />
              )}
            />
          </Card.Content>
        </Card>

        {/* Actions */}
        <View style={styles.actions}>
          {!paymentMethod.isDefault && (
            <Button
              mode="contained"
              onPress={handleSetDefault}
              style={styles.actionButton}
              icon={({ size, color }) => (
                <Ionicons name="checkmark-circle-outline" size={size} color={color} />
              )}
            >
              Set as Default
            </Button>
          )}

          <Button
            mode="outlined"
            onPress={() => {
              Alert.alert('Edit Payment Method', 'Edit functionality would be implemented here.');
            }}
            style={styles.actionButton}
            icon={({ size, color }) => (
              <Ionicons name="create-outline" size={size} color={color} />
            )}
          >
            Edit
          </Button>

          <Button
            mode="outlined"
            onPress={handleDelete}
            style={[styles.actionButton, { borderColor: colors.error[500] }]}
            labelStyle={{ color: colors.error[500] }}
            icon={({ size }) => (
              <Ionicons name="trash-outline" size={size} color={colors.error[500]} />
            )}
          >
            Delete
          </Button>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  card: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  cardTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  cardSubtitle: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  defaultBadge: {
    fontSize: typography.fontSize.sm,
    color: colors.primary[600],
    fontFamily: typography.fontFamily.medium,
    backgroundColor: colors.primary[50],
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.md,
  },
  actions: {
    marginTop: spacing.md,
  },
  actionButton: {
    marginBottom: spacing.md,
  },
});

export default PaymentDetailScreen;
