import React from 'react'
import { clsx } from 'clsx'

export const Card = ({
  children,
  className = '',
  padding = 'default',
  shadow = 'default',
  hover = false,
  gradient = false,
  ...props
}) => {
  const paddingClasses = {
    none: '',
    xs: 'p-3',
    sm: 'p-4',
    default: 'p-6',
    lg: 'p-8',
    xl: 'p-10'
  }

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    default: 'shadow-card',
    md: 'shadow-medium',
    lg: 'shadow-large',
    elevated: 'shadow-elevated'
  }

  return (
    <div
      className={clsx(
        'bg-white rounded-xl border border-gray-200/60 backdrop-blur-sm transition-all duration-300',
        paddingClasses[padding],
        shadowClasses[shadow],
        hover && 'hover:shadow-card-hover hover:-translate-y-1 cursor-pointer',
        gradient && 'bg-gradient-to-br from-white to-gray-50/50',
        'animate-fade-in-up',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export const CardHeader = ({ children, className = '', divider = true, ...props }) => (
  <div className={clsx(
    'pb-5 mb-6',
    divider && 'border-b border-gray-200/60',
    className
  )} {...props}>
    {children}
  </div>
)

export const CardTitle = ({ children, className = '', size = 'default', ...props }) => {
  const sizeClasses = {
    sm: 'text-base font-semibold',
    default: 'text-lg font-semibold',
    lg: 'text-xl font-bold',
    xl: 'text-2xl font-bold'
  }

  return (
    <h3 className={clsx(
      'text-gray-900 tracking-tight',
      sizeClasses[size],
      className
    )} {...props}>
      {children}
    </h3>
  )
}

export const CardDescription = ({ children, className = '', ...props }) => (
  <p className={clsx('text-sm text-gray-600 mt-1', className)} {...props}>
    {children}
  </p>
)

export const CardContent = ({ children, className = '', ...props }) => (
  <div className={clsx('space-y-4', className)} {...props}>
    {children}
  </div>
)

export const CardFooter = ({ children, className = '', divider = true, ...props }) => (
  <div className={clsx(
    'pt-5 mt-6',
    divider && 'border-t border-gray-200/60',
    className
  )} {...props}>
    {children}
  </div>
)

export default Card
