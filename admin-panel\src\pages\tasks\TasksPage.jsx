import { useState, useEffect } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { <PERSON> } from 'react-router-dom'
import {
  ClipboardDocumentListIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  MapPinIcon,
  CalendarDaysIcon,
  CurrencyDollarIcon,
  UserIcon,
  TagIcon,
} from '@heroicons/react/24/outline'
import PremiumCard, { PremiumCardHeader, PremiumCardTitle, PremiumCardContent } from '../../components/ui/PremiumCard'
import PremiumButton from '../../components/ui/PremiumButton'
import PremiumInput from '../../components/ui/PremiumInput'

const TasksPage = () => {
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterCategory, setFilterCategory] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const tasksPerPage = 10

  // Mock data
  useEffect(() => {
    const mockTasks = [
      {
        id: 1,
        title: 'Fix Kitchen Sink Leak',
        description: 'Kitchen sink has a persistent leak that needs immediate attention',
        category: 'Plumbing',
        status: 'in_progress',
        priority: 'high',
        customer: 'John Doe',
        workBoy: 'Rajesh Kumar',
        location: 'Mumbai, Maharashtra',
        createdDate: '2024-03-15',
        dueDate: '2024-03-16',
        budget: 2500,
        estimatedHours: 3,
        completedHours: 1.5
      },
      {
        id: 2,
        title: 'Electrical Wiring Installation',
        description: 'Install new electrical wiring for home renovation project',
        category: 'Electrical',
        status: 'pending',
        priority: 'medium',
        customer: 'Jane Smith',
        workBoy: null,
        location: 'Delhi, Delhi',
        createdDate: '2024-03-14',
        dueDate: '2024-03-18',
        budget: 8500,
        estimatedHours: 8,
        completedHours: 0
      },
      {
        id: 3,
        title: 'Deep House Cleaning',
        description: 'Complete deep cleaning service for 3BHK apartment',
        category: 'Cleaning',
        status: 'completed',
        priority: 'low',
        customer: 'Mike Johnson',
        workBoy: 'Suresh Patel',
        location: 'Bangalore, Karnataka',
        createdDate: '2024-03-12',
        dueDate: '2024-03-13',
        budget: 3500,
        estimatedHours: 6,
        completedHours: 6
      },
      {
        id: 4,
        title: 'Wooden Furniture Repair',
        description: 'Repair damaged wooden dining table and chairs',
        category: 'Carpentry',
        status: 'in_progress',
        priority: 'medium',
        customer: 'Sarah Wilson',
        workBoy: 'Vikram Singh',
        location: 'Chennai, Tamil Nadu',
        createdDate: '2024-03-13',
        dueDate: '2024-03-17',
        budget: 4200,
        estimatedHours: 5,
        completedHours: 2
      },
      {
        id: 5,
        title: 'Wall Painting Service',
        description: 'Paint living room and bedroom walls with premium paint',
        category: 'Painting',
        status: 'cancelled',
        priority: 'low',
        customer: 'David Brown',
        workBoy: 'Manoj Gupta',
        location: 'Pune, Maharashtra',
        createdDate: '2024-03-10',
        dueDate: '2024-03-15',
        budget: 6800,
        estimatedHours: 12,
        completedHours: 0
      }
    ]

    setTimeout(() => {
      setTasks(mockTasks)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.customer.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || task.status === filterStatus
    const matchesCategory = filterCategory === 'all' || task.category === filterCategory
    return matchesSearch && matchesStatus && matchesCategory
  })

  const totalPages = Math.ceil(filteredTasks.length / tasksPerPage)
  const startIndex = (currentPage - 1) * tasksPerPage
  const paginatedTasks = filteredTasks.slice(startIndex, startIndex + tasksPerPage)

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { bg: 'bg-warning-100', text: 'text-warning-800', icon: ClockIcon },
      in_progress: { bg: 'bg-primary-100', text: 'text-primary-800', icon: ClockIcon },
      completed: { bg: 'bg-success-100', text: 'text-success-800', icon: CheckCircleIcon },
      cancelled: { bg: 'bg-danger-100', text: 'text-danger-800', icon: XCircleIcon }
    }

    const config = statusConfig[status] || statusConfig.pending
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-bold ${config.bg} ${config.text}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </span>
    )
  }

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      low: { bg: 'bg-gray-100', text: 'text-gray-800' },
      medium: { bg: 'bg-warning-100', text: 'text-warning-800' },
      high: { bg: 'bg-danger-100', text: 'text-danger-800' }
    }

    const config = priorityConfig[priority] || priorityConfig.medium

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-bold ${config.bg} ${config.text}`}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    )
  }

  const getCategoryIcon = (category) => {
    const categoryIcons = {
      'Plumbing': '🔧',
      'Electrical': '⚡',
      'Cleaning': '🧹',
      'Carpentry': '🔨',
      'Painting': '🎨'
    }

    return categoryIcons[category] || '🔧'
  }

  if (loading) {
    return (
      <div className="space-y-6 animate-fade-in-up">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 animate-fade-in-up">
      <Helmet>
        <title>Tasks Management - WorkBoy Admin</title>
      </Helmet>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-black bg-gradient-to-r from-gray-900 via-primary-800 to-brand-700 bg-clip-text text-transparent">
            Tasks Management
          </h1>
          <p className="text-gray-600 mt-2 font-medium">Monitor and manage all service tasks</p>
        </div>
        <div className="flex space-x-3">
          <PremiumButton variant="outline" icon={ArrowDownTrayIcon}>
            Export
          </PremiumButton>
          <PremiumButton variant="primary" icon={PlusIcon}>
            Create Task
          </PremiumButton>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 shadow-lg shadow-primary-500/25">
              <ClipboardDocumentListIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Total Tasks</p>
              <p className="text-2xl font-black text-gray-900">{tasks.length}</p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-warning-500 to-warning-600 shadow-lg shadow-warning-500/25">
              <ClockIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">In Progress</p>
              <p className="text-2xl font-black text-gray-900">
                {tasks.filter(t => t.status === 'in_progress').length}
              </p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-success-500 to-success-600 shadow-lg shadow-success-500/25">
              <CheckCircleIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Completed</p>
              <p className="text-2xl font-black text-gray-900">
                {tasks.filter(t => t.status === 'completed').length}
              </p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-accent-500 to-accent-600 shadow-lg shadow-accent-500/25">
              <CurrencyDollarIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Total Value</p>
              <p className="text-2xl font-black text-gray-900">
                ₹{tasks.reduce((sum, t) => sum + t.budget, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </PremiumCard>
      </div>

      {/* Filters and Search */}
      <PremiumCard gradient>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1 max-w-md">
            <PremiumInput
              placeholder="Search tasks by title, customer, or description..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={MagnifyingGlassIcon}
              variant="premium"
            />
          </div>

          <div className="flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-xl text-sm font-medium bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-300"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>

            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-xl text-sm font-medium bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-300"
            >
              <option value="all">All Categories</option>
              <option value="Plumbing">Plumbing</option>
              <option value="Electrical">Electrical</option>
              <option value="Cleaning">Cleaning</option>
              <option value="Carpentry">Carpentry</option>
              <option value="Painting">Painting</option>
            </select>

            <PremiumButton variant="outline" icon={FunnelIcon} size="sm">
              More Filters
            </PremiumButton>
          </div>
        </div>
      </PremiumCard>

      {/* Tasks Table */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={ClipboardDocumentListIcon}>
            Tasks List ({filteredTasks.length} tasks)
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Task
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Customer & Work-Boy
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Category & Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Budget
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedTasks.map((task) => (
                  <tr key={task.id} className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-2xl mr-3">{getCategoryIcon(task.category)}</div>
                        <div>
                          <div className="text-sm font-bold text-gray-900">{task.title}</div>
                          <div className="text-sm text-gray-500 max-w-xs truncate">{task.description}</div>
                          <div className="text-xs text-gray-400 flex items-center mt-1">
                            <MapPinIcon className="h-3 w-3 mr-1" />
                            {task.location}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <div className="text-sm text-gray-900 flex items-center">
                          <UserIcon className="h-3 w-3 mr-2 text-gray-400" />
                          {task.customer}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          {task.workBoy ? (
                            <>
                              <UserIcon className="h-3 w-3 mr-2 text-gray-400" />
                              {task.workBoy}
                            </>
                          ) : (
                            <span className="text-warning-600 font-medium">Unassigned</span>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <TagIcon className="h-3 w-3 mr-2 text-gray-400" />
                          <span className="text-sm font-medium text-gray-900">{task.category}</span>
                        </div>
                        <div>{getPriorityBadge(task.priority)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="flex items-center space-x-2 mb-1">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-primary-500 to-brand-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${(task.completedHours / task.estimatedHours) * 100}%` }}
                            />
                          </div>
                          <span className="text-xs font-medium">
                            {Math.round((task.completedHours / task.estimatedHours) * 100)}%
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {task.completedHours}h / {task.estimatedHours}h
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-bold text-gray-900">
                        ₹{task.budget.toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 flex items-center">
                        <CalendarDaysIcon className="h-3 w-3 mr-2 text-gray-400" />
                        {new Date(task.dueDate).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(task.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/tasks/${task.id}`}
                          className="text-primary-600 hover:text-primary-900 p-1 rounded-lg hover:bg-primary-50 transition-colors duration-200"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Link>
                        <button className="text-warning-600 hover:text-warning-900 p-1 rounded-lg hover:bg-warning-50 transition-colors duration-200">
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button className="text-danger-600 hover:text-danger-900 p-1 rounded-lg hover:bg-danger-50 transition-colors duration-200">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </PremiumCardContent>
      </PremiumCard>
    </div>
  )
}

export default TasksPage
