import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { useLoading } from '../../contexts/LoadingContext'
import Layout from '../../components/layout/Layout'
import { taskService } from '../../services/taskService'
import { addressService } from '../../services/addressService'

const CreateTaskPage = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  const { setLoading } = useLoading()
  
  const [categories, setCategories] = useState([])
  const [addresses, setAddresses] = useState([])
  const [formData, setFormData] = useState({
    category_id: '',
    title: '',
    description: '',
    address_id: '',
    scheduled_date: '',
    scheduled_time: '',
    estimated_duration: 60,
    special_instructions: ''
  })
  const [errors, setErrors] = useState({})

  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    setLoading(true)
    try {
      const [categoriesResponse, addressesResponse] = await Promise.all([
        taskService.getCategories(),
        addressService.getAddresses()
      ])
      
      setCategories(categoriesResponse.data || [])
      setAddresses(addressesResponse.data || [])
    } catch (error) {
      console.error('Error loading initial data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.category_id) newErrors.category_id = 'Please select a category'
    if (!formData.title.trim()) newErrors.title = 'Title is required'
    if (!formData.description.trim()) newErrors.description = 'Description is required'
    if (!formData.address_id) newErrors.address_id = 'Please select an address'
    if (!formData.scheduled_date) newErrors.scheduled_date = 'Scheduled date is required'
    if (!formData.scheduled_time) newErrors.scheduled_time = 'Scheduled time is required'
    
    // Validate date is not in the past
    const selectedDateTime = new Date(`${formData.scheduled_date}T${formData.scheduled_time}`)
    if (selectedDateTime < new Date()) {
      newErrors.scheduled_date = 'Scheduled date and time cannot be in the past'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setLoading(true)
    try {
      const taskData = {
        ...formData,
        customer_id: user.id,
        status: 'pending'
      }
      
      await taskService.createTask(taskData)
      navigate('/tasks', { 
        state: { message: 'Task created successfully!' }
      })
    } catch (error) {
      console.error('Error creating task:', error)
      setErrors({ submit: 'Failed to create task. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const selectedCategory = categories.find(cat => cat.id === formData.category_id)

  return (
    <Layout>
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-soft p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Create New Task</h1>
            <p className="text-gray-600 mt-1">Book a service by filling out the details below</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Category Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Category *
              </label>
              <select
                name="category_id"
                value={formData.category_id}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.category_id ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name} - ${category.base_price}/hr
                  </option>
                ))}
              </select>
              {errors.category_id && (
                <p className="text-red-500 text-sm mt-1">{errors.category_id}</p>
              )}
              {selectedCategory && (
                <p className="text-gray-600 text-sm mt-1">{selectedCategory.description}</p>
              )}
            </div>

            {/* Task Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Task Title *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="e.g., Deep clean 3-bedroom apartment"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.title ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.title && (
                <p className="text-red-500 text-sm mt-1">{errors.title}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                placeholder="Provide detailed description of the task..."
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">{errors.description}</p>
              )}
            </div>

            {/* Address Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Address *
              </label>
              <select
                name="address_id"
                value={formData.address_id}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.address_id ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select an address</option>
                {addresses.map(address => (
                  <option key={address.id} value={address.id}>
                    {address.street_address}, {address.city}
                  </option>
                ))}
              </select>
              {errors.address_id && (
                <p className="text-red-500 text-sm mt-1">{errors.address_id}</p>
              )}
              <button
                type="button"
                onClick={() => navigate('/addresses')}
                className="text-primary-600 text-sm mt-1 hover:text-primary-700"
              >
                + Add new address
              </button>
            </div>

            {/* Scheduling */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date *
                </label>
                <input
                  type="date"
                  name="scheduled_date"
                  value={formData.scheduled_date}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.scheduled_date ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.scheduled_date && (
                  <p className="text-red-500 text-sm mt-1">{errors.scheduled_date}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Time *
                </label>
                <input
                  type="time"
                  name="scheduled_time"
                  value={formData.scheduled_time}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    errors.scheduled_time ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.scheduled_time && (
                  <p className="text-red-500 text-sm mt-1">{errors.scheduled_time}</p>
                )}
              </div>
            </div>

            {/* Estimated Duration */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estimated Duration (minutes)
              </label>
              <select
                name="estimated_duration"
                value={formData.estimated_duration}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value={30}>30 minutes</option>
                <option value={60}>1 hour</option>
                <option value={90}>1.5 hours</option>
                <option value={120}>2 hours</option>
                <option value={180}>3 hours</option>
                <option value={240}>4 hours</option>
                <option value={480}>8 hours (full day)</option>
              </select>
            </div>

            {/* Special Instructions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Special Instructions
              </label>
              <textarea
                name="special_instructions"
                value={formData.special_instructions}
                onChange={handleInputChange}
                rows={3}
                placeholder="Any special requirements or instructions..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Submit Error */}
            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-600 text-sm">{errors.submit}</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4 pt-4">
              <button
                type="button"
                onClick={() => navigate('/tasks')}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
              >
                Create Task
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  )
}

export default CreateTaskPage
