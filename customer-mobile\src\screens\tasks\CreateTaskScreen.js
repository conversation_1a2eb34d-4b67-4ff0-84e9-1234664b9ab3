import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Chip,
  IconButton,
  Menu,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useForm, Controller } from 'react-hook-form';
import { apiService, endpoints } from '@services/apiService';
import { colors, spacing, typography, borderRadius } from '@constants/theme';
import { showToast } from '@utils/toast';
import { format } from 'date-fns';

const CreateTaskScreen = ({ navigation, route }) => {
  const { categoryId, categoryName } = route.params || {};
  
  const [categories, setCategories] = useState([]);
  const [addresses, setAddresses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [categoryMenuVisible, setCategoryMenuVisible] = useState(false);
  const [addressMenuVisible, setAddressMenuVisible] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    defaultValues: {
      title: '',
      description: '',
      category_id: categoryId || '',
      address_id: '',
      scheduled_date: new Date(),
      scheduled_time: '09:00',
      budget_min: '',
      budget_max: '',
      urgency: 'normal',
      notes: '',
    },
  });

  const watchedValues = watch();

  useEffect(() => {
    fetchCategories();
    fetchAddresses();
  }, []);

  useEffect(() => {
    if (categoryId) {
      setValue('category_id', categoryId);
    }
  }, [categoryId]);

  const fetchCategories = async () => {
    try {
      const response = await apiService.get(endpoints.tasks.categories);
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const fetchAddresses = async () => {
    try {
      const response = await apiService.get(endpoints.customer.addresses);
      if (response.success) {
        setAddresses(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch addresses:', error);
    }
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      
      const taskData = {
        ...data,
        scheduled_date: format(data.scheduled_date, 'yyyy-MM-dd'),
        budget_min: data.budget_min ? parseFloat(data.budget_min) : null,
        budget_max: data.budget_max ? parseFloat(data.budget_max) : null,
      };

      const response = await apiService.post(endpoints.tasks.create, taskData);
      
      if (response.success) {
        showToast('Task created successfully!', 'success');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Failed to create task:', error);
      showToast('Failed to create task. Please try again.', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setValue('scheduled_date', selectedDate);
    }
  };

  const handleTimeChange = (event, selectedTime) => {
    setShowTimePicker(false);
    if (selectedTime) {
      const timeString = format(selectedTime, 'HH:mm');
      setValue('scheduled_time', timeString);
    }
  };

  const selectedCategory = categories.find(cat => cat.id === watchedValues.category_id);
  const selectedAddress = addresses.find(addr => addr.id === watchedValues.address_id);

  const urgencyOptions = [
    { value: 'low', label: 'Low', color: colors.success[500] },
    { value: 'normal', label: 'Normal', color: colors.primary[500] },
    { value: 'high', label: 'High', color: colors.warning[500] },
    { value: 'urgent', label: 'Urgent', color: colors.error[500] },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          iconColor={colors.gray[600]}
          onPress={() => navigation.goBack()}
        />
        <Text style={styles.headerTitle}>Create Task</Text>
        <View style={{ width: 40 }} />
      </View>

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.form}>
            {/* Task Title */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Task Title *</Text>
              <Controller
                control={control}
                name="title"
                rules={{ required: 'Task title is required' }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    mode="outlined"
                    placeholder="What do you need help with?"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={!!errors.title}
                    style={styles.input}
                    outlineColor={colors.gray[300]}
                    activeOutlineColor={colors.primary[600]}
                  />
                )}
              />
              {errors.title && (
                <Text style={styles.errorText}>{errors.title.message}</Text>
              )}
            </View>

            {/* Category Selection */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Category *</Text>
              <Menu
                visible={categoryMenuVisible}
                onDismiss={() => setCategoryMenuVisible(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setCategoryMenuVisible(true)}
                    style={styles.menuButton}
                    contentStyle={styles.menuButtonContent}
                    labelStyle={styles.menuButtonLabel}
                    icon="chevron-down"
                  >
                    {selectedCategory ? selectedCategory.name : 'Select Category'}
                  </Button>
                }
              >
                {categories.map((category) => (
                  <Menu.Item
                    key={category.id}
                    onPress={() => {
                      setValue('category_id', category.id);
                      setCategoryMenuVisible(false);
                    }}
                    title={category.name}
                  />
                ))}
              </Menu>
              {errors.category_id && (
                <Text style={styles.errorText}>Category is required</Text>
              )}
            </View>

            {/* Description */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Description *</Text>
              <Controller
                control={control}
                name="description"
                rules={{ required: 'Description is required' }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    mode="outlined"
                    placeholder="Describe your task in detail..."
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={!!errors.description}
                    multiline
                    numberOfLines={4}
                    style={styles.textArea}
                    outlineColor={colors.gray[300]}
                    activeOutlineColor={colors.primary[600]}
                  />
                )}
              />
              {errors.description && (
                <Text style={styles.errorText}>{errors.description.message}</Text>
              )}
            </View>

            {/* Address Selection */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Address *</Text>
              <Menu
                visible={addressMenuVisible}
                onDismiss={() => setAddressMenuVisible(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setAddressMenuVisible(true)}
                    style={styles.menuButton}
                    contentStyle={styles.menuButtonContent}
                    labelStyle={styles.menuButtonLabel}
                    icon="chevron-down"
                  >
                    {selectedAddress ? selectedAddress.label || 'Selected Address' : 'Select Address'}
                  </Button>
                }
              >
                {addresses.map((address) => (
                  <Menu.Item
                    key={address.id}
                    onPress={() => {
                      setValue('address_id', address.id);
                      setAddressMenuVisible(false);
                    }}
                    title={address.label || `${address.address_line_1}, ${address.city}`}
                  />
                ))}
                <Divider />
                <Menu.Item
                  onPress={() => {
                    setAddressMenuVisible(false);
                    navigation.navigate('AddAddress');
                  }}
                  title="Add New Address"
                  leadingIcon="plus"
                />
              </Menu>
              {errors.address_id && (
                <Text style={styles.errorText}>Address is required</Text>
              )}
            </View>

            {/* Date and Time */}
            <View style={styles.row}>
              <View style={[styles.inputGroup, { flex: 1, marginRight: spacing.md }]}>
                <Text style={styles.label}>Date *</Text>
                <Button
                  mode="outlined"
                  onPress={() => setShowDatePicker(true)}
                  style={styles.dateTimeButton}
                  contentStyle={styles.dateTimeButtonContent}
                  labelStyle={styles.dateTimeButtonLabel}
                  icon="calendar-outline"
                >
                  {format(watchedValues.scheduled_date, 'MMM dd, yyyy')}
                </Button>
              </View>

              <View style={[styles.inputGroup, { flex: 1 }]}>
                <Text style={styles.label}>Time *</Text>
                <Button
                  mode="outlined"
                  onPress={() => setShowTimePicker(true)}
                  style={styles.dateTimeButton}
                  contentStyle={styles.dateTimeButtonContent}
                  labelStyle={styles.dateTimeButtonLabel}
                  icon="time-outline"
                >
                  {watchedValues.scheduled_time}
                </Button>
              </View>
            </View>

            {/* Budget Range */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Budget Range (Optional)</Text>
              <View style={styles.row}>
                <Controller
                  control={control}
                  name="budget_min"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      mode="outlined"
                      placeholder="Min ₹"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      keyboardType="numeric"
                      style={[styles.input, { flex: 1, marginRight: spacing.md }]}
                      outlineColor={colors.gray[300]}
                      activeOutlineColor={colors.primary[600]}
                    />
                  )}
                />
                <Controller
                  control={control}
                  name="budget_max"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      mode="outlined"
                      placeholder="Max ₹"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      keyboardType="numeric"
                      style={[styles.input, { flex: 1 }]}
                      outlineColor={colors.gray[300]}
                      activeOutlineColor={colors.primary[600]}
                    />
                  )}
                />
              </View>
            </View>

            {/* Urgency */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Urgency</Text>
              <View style={styles.urgencyContainer}>
                {urgencyOptions.map((option) => (
                  <Chip
                    key={option.value}
                    mode={watchedValues.urgency === option.value ? 'flat' : 'outlined'}
                    selected={watchedValues.urgency === option.value}
                    onPress={() => setValue('urgency', option.value)}
                    style={[
                      styles.urgencyChip,
                      watchedValues.urgency === option.value && {
                        backgroundColor: `${option.color}20`,
                        borderColor: option.color,
                      }
                    ]}
                    textStyle={[
                      styles.urgencyChipText,
                      watchedValues.urgency === option.value && { color: option.color }
                    ]}
                  >
                    {option.label}
                  </Chip>
                ))}
              </View>
            </View>

            {/* Additional Notes */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Additional Notes</Text>
              <Controller
                control={control}
                name="notes"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    mode="outlined"
                    placeholder="Any special instructions or requirements..."
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    multiline
                    numberOfLines={3}
                    style={styles.textArea}
                    outlineColor={colors.gray[300]}
                    activeOutlineColor={colors.primary[600]}
                  />
                )}
              />
            </View>
          </View>
        </ScrollView>

        {/* Submit Button */}
        <View style={styles.footer}>
          <Button
            mode="contained"
            onPress={handleSubmit(onSubmit)}
            loading={loading}
            disabled={loading}
            style={styles.submitButton}
            contentStyle={styles.submitButtonContent}
            labelStyle={styles.submitButtonLabel}
          >
            Create Task
          </Button>
        </View>
      </KeyboardAvoidingView>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={watchedValues.scheduled_date}
          mode="date"
          display="default"
          minimumDate={new Date()}
          onChange={handleDateChange}
        />
      )}

      {/* Time Picker */}
      {showTimePicker && (
        <DateTimePicker
          value={new Date(`2000-01-01T${watchedValues.scheduled_time}:00`)}
          mode="time"
          display="default"
          onChange={handleTimeChange}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  form: {
    padding: spacing.lg,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  label: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[700],
    marginBottom: spacing.sm,
  },
  input: {
    backgroundColor: colors.white,
  },
  textArea: {
    backgroundColor: colors.white,
    minHeight: 100,
  },
  errorText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.error[600],
    marginTop: spacing.xs,
  },
  menuButton: {
    borderColor: colors.gray[300],
    borderRadius: borderRadius.md,
    justifyContent: 'flex-start',
  },
  menuButtonContent: {
    justifyContent: 'flex-start',
    paddingVertical: spacing.sm,
  },
  menuButtonLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[700],
  },
  row: {
    flexDirection: 'row',
  },
  dateTimeButton: {
    borderColor: colors.gray[300],
    borderRadius: borderRadius.md,
    justifyContent: 'flex-start',
  },
  dateTimeButtonContent: {
    justifyContent: 'flex-start',
    paddingVertical: spacing.sm,
  },
  dateTimeButtonLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[700],
  },
  urgencyContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  urgencyChip: {
    backgroundColor: colors.gray[100],
    borderColor: colors.gray[300],
  },
  urgencyChipText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
  },
  footer: {
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  submitButton: {
    backgroundColor: colors.primary[600],
    borderRadius: borderRadius.lg,
  },
  submitButtonContent: {
    paddingVertical: spacing.sm,
  },
  submitButtonLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.white,
  },
});

export default CreateTaskScreen;
