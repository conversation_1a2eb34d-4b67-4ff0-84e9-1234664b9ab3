import AsyncStorage from '@react-native-async-storage/async-storage'
import { authService } from './authService'
import { showToast } from '@utils/toast'

class WebSocketService {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 1000
    this.listeners = new Map()
    this.isConnected = false
    this.userId = null
    this.heartbeatInterval = null
  }

  async connect() {
    const token = await authService.getToken()
    if (!token) {
      console.error('No auth token available for WebSocket connection')
      return
    }

    const wsUrl = process.env.EXPO_PUBLIC_WS_URL || 'ws://localhost:8080'
    
    try {
      this.ws = new WebSocket(wsUrl)
      this.setupEventListeners()
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      this.scheduleReconnect()
    }
  }

  setupEventListeners() {
    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.isConnected = true
      this.reconnectAttempts = 0
      
      // Authenticate immediately after connection
      this.authenticate()
      
      // Start heartbeat
      this.startHeartbeat()
      
      this.emit('connected')
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.handleMessage(data)
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason)
      this.isConnected = false
      this.stopHeartbeat()
      this.emit('disconnected')
      
      // Attempt to reconnect unless it was a clean close
      if (event.code !== 1000) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error)
      this.emit('error', error)
    }
  }

  handleMessage(data) {
    console.log('WebSocket message received:', data)
    
    switch (data.type) {
      case 'authenticated':
        this.userId = data.user_id
        this.emit('authenticated', data)
        break
        
      case 'task_update':
        this.handleTaskUpdate(data)
        break
        
      case 'location_update':
        this.emit('locationUpdate', data)
        break
        
      case 'notification':
        this.handleNotification(data)
        break
        
      case 'workboy_status':
        this.emit('workboyStatus', data)
        break
        
      case 'new_task_available':
        this.handleNewTaskAvailable(data)
        break
        
      case 'task_assigned':
        this.handleTaskAssigned(data)
        break
        
      case 'error':
        console.error('WebSocket server error:', data.message)
        this.emit('error', data)
        break
        
      case 'pong':
        // Handle ping/pong for connection health
        break
        
      default:
        console.warn('Unknown WebSocket message type:', data.type)
    }
  }

  handleTaskUpdate(data) {
    this.emit('taskUpdate', data)
    
    // Show toast notification for important updates
    const statusMessages = {
      'assigned': 'Task has been assigned to a Work-Boy',
      'started': 'Work-Boy has started your task',
      'in_progress': 'Your task is in progress',
      'completed': 'Your task has been completed!',
      'cancelled': 'Your task has been cancelled',
    }
    
    const message = statusMessages[data.status]
    if (message) {
      showToast(message, 'info')
    }
  }

  handleNotification(data) {
    this.emit('notification', data)
    
    // Show toast notification
    showToast(data.title || 'New notification', 'info')
  }

  handleNewTaskAvailable(data) {
    this.emit('newTaskAvailable', data)
    showToast('New task available in your area!', 'success')
  }

  handleTaskAssigned(data) {
    this.emit('taskAssigned', data)
    showToast('You have been assigned a new task!', 'success')
  }

  async authenticate() {
    const token = await authService.getToken()
    if (token && this.isConnected) {
      this.send({
        type: 'authenticate',
        token: token
      })
    }
  }

  joinRoom(roomId) {
    if (this.isConnected) {
      this.send({
        type: 'join_room',
        room_id: roomId
      })
    }
  }

  trackTask(taskId) {
    this.joinRoom(`task_${taskId}`)
  }

  trackWorkBoy(workboyId) {
    this.joinRoom(`workboy_${workboyId}`)
  }

  updateTaskStatus(taskId, status, data = {}) {
    if (this.isConnected) {
      this.send({
        type: 'task_update',
        task_id: taskId,
        status: status,
        ...data
      })
    }
  }

  sendLocationUpdate(latitude, longitude) {
    if (this.isConnected) {
      this.send({
        type: 'location_update',
        latitude: latitude,
        longitude: longitude
      })
    }
  }

  acceptTask(taskId) {
    if (this.isConnected) {
      this.send({
        type: 'task_action',
        action: 'accept',
        task_id: taskId
      })
    }
  }

  rejectTask(taskId, reason = '') {
    if (this.isConnected) {
      this.send({
        type: 'task_action',
        action: 'reject',
        task_id: taskId,
        reason: reason
      })
    }
  }

  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    } else {
      console.warn('WebSocket not connected, cannot send message:', data)
    }
  }

  scheduleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect()
      }, delay)
    } else {
      console.error('Max reconnection attempts reached')
      this.emit('maxReconnectAttemptsReached')
      showToast('Connection lost. Please check your internet connection.', 'error')
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
    this.isConnected = false
    this.userId = null
    this.stopHeartbeat()
  }

  // Event listener management
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Error in WebSocket event callback:', error)
        }
      })
    }
  }

  // Health check
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' })
      }
    }, 30000) // Send ping every 30 seconds
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  // Utility methods
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      userId: this.userId,
      reconnectAttempts: this.reconnectAttempts
    }
  }

  // Background task handling for location updates
  startLocationTracking() {
    if (this.isConnected) {
      this.send({
        type: 'start_location_tracking'
      })
    }
  }

  stopLocationTracking() {
    if (this.isConnected) {
      this.send({
        type: 'stop_location_tracking'
      })
    }
  }
}

// Create singleton instance
export const websocketService = new WebSocketService()

export default websocketService
