<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class TaskCategoriesSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'name'        => 'Grocery Shopping',
                'description' => 'Buy groceries and household items from supermarkets, local stores, or online platforms',
                'icon'        => '🛒',
                'base_price'  => 50.00,
                'is_active'   => true,
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
            [
                'name'        => 'Delivery',
                'description' => 'Pick up and deliver items, documents, packages, or food from one location to another',
                'icon'        => '📦',
                'base_price'  => 30.00,
                'is_active'   => true,
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
            [
                'name'        => 'Cleaning',
                'description' => 'House cleaning, office cleaning, and general maintenance tasks',
                'icon'        => '🧹',
                'base_price'  => 100.00,
                'is_active'   => true,
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
            [
                'name'        => 'Pet Care',
                'description' => 'Pet walking, feeding, grooming, and general pet care services',
                'icon'        => '🐶',
                'base_price'  => 40.00,
                'is_active'   => true,
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
            [
                'name'        => 'Custom Task',
                'description' => 'Any other personal task or errand not covered by standard categories',
                'icon'        => '✍️',
                'base_price'  => 25.00,
                'is_active'   => true,
                'created_at'  => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert data
        $this->db->table('task_categories')->insertBatch($data);
    }
}
