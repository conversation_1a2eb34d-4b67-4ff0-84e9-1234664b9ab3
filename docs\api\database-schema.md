# Work-Boy Booking Database Schema

## Overview
This document outlines the database schema for the Work-Boy Booking platform using MySQL.

## Tables

### 1. Users
Primary table for all platform users (Customers, Work-Boys, Admins)

```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    firebase_uid VARCHAR(128) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    first_name VA<PERSON>HAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    profile_image VARCHAR(500),
    user_type ENUM('customer', 'workboy', 'admin') NOT NULL,
    status ENUM('active', 'inactive', 'suspended', 'pending_verification') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_firebase_uid (firebase_uid),
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status)
);
```

### 2. Customer Profiles
Extended information for customers

```sql
CREATE TABLE customer_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    preferred_payment_method ENUM('card', 'wallet', 'upi') DEFAULT 'card',
    default_address_id INT,
    notification_preferences JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id)
);
```

### 3. Work-Boy Profiles
Extended information for Work-Boys

```sql
CREATE TABLE workboy_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    kyc_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    kyc_documents JSON,
    skills JSON,
    hourly_rate DECIMAL(8,2),
    availability_status ENUM('available', 'busy', 'offline') DEFAULT 'offline',
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_tasks_completed INT DEFAULT 0,
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    bank_details JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_kyc_status (kyc_status),
    INDEX idx_availability_status (availability_status),
    INDEX idx_rating (rating)
);
```

### 4. Addresses
User addresses for task locations

```sql
CREATE TABLE addresses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    label VARCHAR(50),
    address_line_1 VARCHAR(255) NOT NULL,
    address_line_2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) DEFAULT 'India',
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_location (latitude, longitude)
);
```

### 5. Task Categories
Predefined task categories

```sql
CREATE TABLE task_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(255),
    base_price DECIMAL(8,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_is_active (is_active)
);
```

### 6. Tasks
Main tasks table

```sql
CREATE TABLE tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    workboy_id INT,
    category_id INT,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    task_images JSON,
    address_id INT NOT NULL,
    scheduled_date DATE NOT NULL,
    scheduled_time TIME NOT NULL,
    estimated_duration INT, -- in minutes
    status ENUM('pending', 'assigned', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    total_amount DECIMAL(8,2),
    tip_amount DECIMAL(8,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (workboy_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES task_categories(id),
    FOREIGN KEY (address_id) REFERENCES addresses(id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_workboy_id (workboy_id),
    INDEX idx_status (status),
    INDEX idx_scheduled_date (scheduled_date),
    INDEX idx_created_at (created_at)
);
```

### 7. Task Status History
Track task status changes

```sql
CREATE TABLE task_status_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id INT NOT NULL,
    status ENUM('pending', 'assigned', 'in_progress', 'completed', 'cancelled') NOT NULL,
    notes TEXT,
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_task_id (task_id),
    INDEX idx_status (status)
);
```

### 8. Payments
Payment transactions

```sql
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id INT NOT NULL,
    customer_id INT NOT NULL,
    workboy_id INT,
    amount DECIMAL(8,2) NOT NULL,
    tip_amount DECIMAL(8,2) DEFAULT 0.00,
    payment_method VARCHAR(50),
    payment_gateway_id VARCHAR(255),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    gateway_response JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (workboy_id) REFERENCES users(id),
    INDEX idx_task_id (task_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
);
```

### 9. Reviews and Ratings
Customer reviews for completed tasks

```sql
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id INT NOT NULL,
    customer_id INT NOT NULL,
    workboy_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (workboy_id) REFERENCES users(id),
    UNIQUE KEY unique_task_review (task_id),
    INDEX idx_workboy_id (workboy_id),
    INDEX idx_rating (rating)
);
```

### 10. Notifications
System notifications

```sql
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('task_update', 'payment', 'system', 'promotion') NOT NULL,
    related_id INT, -- task_id, payment_id, etc.
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_type (type)
);
```

## Initial Data

### Task Categories
```sql
INSERT INTO task_categories (name, description, icon, base_price) VALUES
('Grocery Shopping', 'Buy groceries and household items', '🛒', 50.00),
('Delivery', 'Pick up and deliver items', '📦', 30.00),
('Cleaning', 'House cleaning and maintenance', '🧹', 100.00),
('Pet Care', 'Pet walking, feeding, and care', '🐶', 40.00),
('Custom Task', 'Any other personal task', '✍️', 25.00);
```

## Indexes and Performance

Key indexes have been added for:
- User lookups (firebase_uid, email, user_type)
- Task queries (customer_id, workboy_id, status, date)
- Location-based searches (latitude, longitude)
- Payment tracking (payment_status, created_at)
- Rating and review queries (workboy_id, rating)

## Security Considerations

1. All sensitive data (bank details, KYC documents) stored as JSON
2. Foreign key constraints maintain data integrity
3. Proper indexing for performance
4. User status tracking for account management
5. Payment status tracking for financial integrity
