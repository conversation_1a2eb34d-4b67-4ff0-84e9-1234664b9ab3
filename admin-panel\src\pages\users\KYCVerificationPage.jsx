import React from 'react'
import { Helmet } from 'react-helmet-async'
import { ShieldCheckIcon } from '@heroicons/react/24/outline'
import { Card, CardHeader, CardTitle, CardContent } from '@components/ui/Card'

const KYCVerificationPage = () => {
  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Helmet>
        <title>KYC Verification - WorkBoy Admin</title>
      </Helmet>
      
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
            <ShieldCheckIcon className="mr-3 h-8 w-8 text-primary-600" />
            KYC Verification
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            Review and approve WorkBoy identity verification documents
          </p>
        </div>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Pending Verifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No pending verifications</h3>
              <p className="mt-1 text-sm text-gray-500">
                All KYC documents have been reviewed.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default KYCVerificationPage
