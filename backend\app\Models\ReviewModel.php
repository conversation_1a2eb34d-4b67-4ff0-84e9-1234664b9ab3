<?php

namespace App\Models;

use CodeIgniter\Model;

class ReviewModel extends Model
{
    protected $table            = 'reviews';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'task_id',
        'customer_id',
        'workboy_id',
        'rating',
        'review_text'
    ];

    // Dates
    protected $useTimestamps = false; // Only created_at, no updated_at
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';

    // Validation
    protected $validationRules = [
        'task_id'     => 'required|integer|is_unique[reviews.task_id,id,{id}]',
        'customer_id' => 'required|integer',
        'workboy_id'  => 'required|integer',
        'rating'      => 'required|integer|greater_than[0]|less_than[6]',
        'review_text' => 'permit_empty',
    ];

    protected $validationMessages = [
        'task_id' => [
            'required'  => 'Task ID is required',
            'is_unique' => 'Review already exists for this task',
        ],
        'customer_id' => [
            'required' => 'Customer ID is required',
        ],
        'workboy_id' => [
            'required' => 'Work-Boy ID is required',
        ],
        'rating' => [
            'required'     => 'Rating is required',
            'greater_than' => 'Rating must be between 1 and 5',
            'less_than'    => 'Rating must be between 1 and 5',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $afterInsert    = ['updateWorkBoyRating'];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Set created_at timestamp
     */
    protected function setCreatedAt(array $data)
    {
        $data['data']['created_at'] = date('Y-m-d H:i:s');
        return $data;
    }

    /**
     * Update Work-Boy rating after review submission
     */
    protected function updateWorkBoyRating(array $data)
    {
        if (isset($data['data']['workboy_id']) && isset($data['data']['rating'])) {
            $workboyProfileModel = new WorkboyProfileModel();
            $workboyProfileModel->updateRating($data['data']['workboy_id'], $data['data']['rating']);
        }

        return $data;
    }

    /**
     * Get reviews by Work-Boy ID
     */
    public function getByWorkBoyId(int $workboyId, int $limit = 20, int $offset = 0)
    {
        return $this->select('reviews.*, tasks.title as task_title,
                             customers.first_name as customer_first_name, customers.last_name as customer_last_name,
                             customers.profile_image as customer_profile_image')
                   ->join('tasks', 'tasks.id = reviews.task_id')
                   ->join('users customers', 'customers.id = reviews.customer_id')
                   ->where('reviews.workboy_id', $workboyId)
                   ->orderBy('reviews.created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get reviews by customer ID
     */
    public function getByCustomerId(int $customerId, int $limit = 20, int $offset = 0)
    {
        return $this->select('reviews.*, tasks.title as task_title,
                             workboys.first_name as workboy_first_name, workboys.last_name as workboy_last_name,
                             workboys.profile_image as workboy_profile_image')
                   ->join('tasks', 'tasks.id = reviews.task_id')
                   ->join('users workboys', 'workboys.id = reviews.workboy_id')
                   ->where('reviews.customer_id', $customerId)
                   ->orderBy('reviews.created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get review by task ID
     */
    public function getByTaskId(int $taskId)
    {
        return $this->where('task_id', $taskId)->first();
    }

    /**
     * Get Work-Boy rating statistics
     */
    public function getWorkBoyRatingStats(int $workboyId)
    {
        return $this->select('
                        COUNT(*) as total_reviews,
                        AVG(rating) as average_rating,
                        SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star_count,
                        SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star_count,
                        SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star_count,
                        SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star_count,
                        SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star_count
                    ')
                   ->where('workboy_id', $workboyId)
                   ->get()
                   ->getRowArray();
    }

    /**
     * Get recent reviews
     */
    public function getRecentReviews(int $limit = 10)
    {
        return $this->select('reviews.*, tasks.title as task_title,
                             customers.first_name as customer_first_name, customers.last_name as customer_last_name,
                             workboys.first_name as workboy_first_name, workboys.last_name as workboy_last_name')
                   ->join('tasks', 'tasks.id = reviews.task_id')
                   ->join('users customers', 'customers.id = reviews.customer_id')
                   ->join('users workboys', 'workboys.id = reviews.workboy_id')
                   ->orderBy('reviews.created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get top rated Work-Boys
     */
    public function getTopRatedWorkBoys(int $limit = 10, int $minReviews = 5)
    {
        return $this->select('workboy_id,
                             workboys.first_name, workboys.last_name, workboys.profile_image,
                             COUNT(*) as total_reviews,
                             AVG(rating) as average_rating')
                   ->join('users workboys', 'workboys.id = reviews.workboy_id')
                   ->groupBy('workboy_id')
                   ->having('total_reviews >=', $minReviews)
                   ->orderBy('average_rating', 'DESC')
                   ->orderBy('total_reviews', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get reviews with rating filter
     */
    public function getReviewsByRating(int $rating, int $limit = 20, int $offset = 0)
    {
        return $this->select('reviews.*, tasks.title as task_title,
                             customers.first_name as customer_first_name, customers.last_name as customer_last_name,
                             workboys.first_name as workboy_first_name, workboys.last_name as workboy_last_name')
                   ->join('tasks', 'tasks.id = reviews.task_id')
                   ->join('users customers', 'customers.id = reviews.customer_id')
                   ->join('users workboys', 'workboys.id = reviews.workboy_id')
                   ->where('reviews.rating', $rating)
                   ->orderBy('reviews.created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Search reviews
     */
    public function searchReviews(string $query, int $limit = 20, int $offset = 0)
    {
        return $this->select('reviews.*, tasks.title as task_title,
                             customers.first_name as customer_first_name, customers.last_name as customer_last_name,
                             workboys.first_name as workboy_first_name, workboys.last_name as workboy_last_name')
                   ->join('tasks', 'tasks.id = reviews.task_id')
                   ->join('users customers', 'customers.id = reviews.customer_id')
                   ->join('users workboys', 'workboys.id = reviews.workboy_id')
                   ->like('reviews.review_text', $query)
                   ->orderBy('reviews.created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get rating distribution
     */
    public function getRatingDistribution(int $workboyId = null)
    {
        $builder = $this->builder();
        
        if ($workboyId) {
            $builder->where('workboy_id', $workboyId);
        }

        return $builder->select('rating, COUNT(*) as count')
                      ->groupBy('rating')
                      ->orderBy('rating', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get monthly review trends
     */
    public function getMonthlyReviewTrends(int $months = 12)
    {
        $startDate = date('Y-m-d', strtotime("-{$months} months"));
        
        return $this->select('YEAR(created_at) as year, MONTH(created_at) as month,
                             COUNT(*) as review_count,
                             AVG(rating) as average_rating')
                   ->where('created_at >=', $startDate)
                   ->groupBy('YEAR(created_at), MONTH(created_at)')
                   ->orderBy('year', 'ASC')
                   ->orderBy('month', 'ASC')
                   ->findAll();
    }

    /**
     * Get reviews pending response (for future feature)
     */
    public function getReviewsPendingResponse(int $workboyId, int $limit = 20)
    {
        // This could be extended to include Work-Boy responses to reviews
        return $this->select('reviews.*, tasks.title as task_title,
                             customers.first_name as customer_first_name, customers.last_name as customer_last_name')
                   ->join('tasks', 'tasks.id = reviews.task_id')
                   ->join('users customers', 'customers.id = reviews.customer_id')
                   ->where('reviews.workboy_id', $workboyId)
                   ->where('reviews.rating <', 4) // Focus on lower ratings
                   ->orderBy('reviews.created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Check if customer can review task
     */
    public function canReviewTask(int $taskId, int $customerId): array
    {
        // Check if task exists and belongs to customer
        $taskModel = new TaskModel();
        $task = $taskModel->where('id', $taskId)
                         ->where('customer_id', $customerId)
                         ->first();

        if (!$task) {
            return [
                'can_review' => false,
                'reason' => 'Task not found or access denied'
            ];
        }

        // Check if task is completed
        if ($task['status'] !== 'completed') {
            return [
                'can_review' => false,
                'reason' => 'Task must be completed before reviewing'
            ];
        }

        // Check if review already exists
        $existingReview = $this->getByTaskId($taskId);
        if ($existingReview) {
            return [
                'can_review' => false,
                'reason' => 'Review already submitted for this task'
            ];
        }

        return [
            'can_review' => true,
            'reason' => null
        ];
    }
}
