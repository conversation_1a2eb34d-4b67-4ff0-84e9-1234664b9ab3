import React from 'react'
import { Helmet } from 'react-helmet-async'
import { UserGroupIcon, PlusIcon } from '@heroicons/react/24/outline'
import { Card, CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import { Button } from '@components/ui/Button'

const AdminUsersPage = () => {
  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Helmet>
        <title>Admin Users - WorkBoy Admin</title>
      </Helmet>
      
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
            <UserGroupIcon className="mr-3 h-8 w-8 text-primary-600" />
            Admin Users
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage administrator accounts and permissions
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Button className="inline-flex items-center">
            <PlusIcon className="mr-2 h-4 w-4" />
            Add Admin
          </Button>
        </div>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Administrator Management</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No admin users found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by adding a new administrator.
              </p>
              <div className="mt-6">
                <Button>
                  <PlusIcon className="mr-2 h-4 w-4" />
                  Add Admin
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default AdminUsersPage
