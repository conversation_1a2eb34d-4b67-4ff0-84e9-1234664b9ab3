import Cookies from 'js-cookie'

const TOKEN_KEY = 'admin_token'
const USER_KEY = 'admin_user'

export const authService = {
  // Token management
  setToken: (token) => {
    Cookies.set(TOKEN_KEY, token, { 
      expires: 7, // 7 days
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    })
  },

  getToken: () => {
    return Cookies.get(TOKEN_KEY)
  },

  removeToken: () => {
    Cookies.remove(TOKEN_KEY)
    localStorage.removeItem(USER_KEY)
  },

  // User data management
  setUser: (user) => {
    localStorage.setItem(USER_KEY, JSON.stringify(user))
  },

  getUser: () => {
    const user = localStorage.getItem(USER_KEY)
    return user ? JSON.parse(user) : null
  },

  removeUser: () => {
    localStorage.removeItem(USER_KEY)
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return !!authService.getToken()
  },

  // Clear all auth data
  clearAuth: () => {
    authService.removeToken()
    authService.removeUser()
  },
}

export default authService
