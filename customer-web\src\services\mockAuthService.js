// Mock authentication service for development when Firebase is not configured
class MockAuthService {
  constructor() {
    this.currentUser = null
    this.listeners = []
  }

  // Mock user object
  createMockUser(email, uid = null) {
    return {
      uid: uid || `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      email: email,
      displayName: email.split('@')[0],
      emailVerified: true,
      photoURL: null,
      phoneNumber: null,
      delete: async () => {
        console.log('Mock: User deleted')
        this.currentUser = null
        this.notifyListeners(null)
      }
    }
  }

  // Mock createUserWithEmailAndPassword
  async createUserWithEmailAndPassword(email, password) {
    console.log('🔧 Mock: Creating user with email and password')
    const user = this.createMockUser(email)
    this.currentUser = user
    this.notifyListeners(user)
    return { user }
  }

  // Mock signInWithEmailAndPassword
  async signInWithEmailAndPassword(email, password) {
    console.log('🔧 Mock: Signing in with email and password')
    const user = this.createMockUser(email)
    this.currentUser = user
    this.notifyListeners(user)
    return { user }
  }

  // Mock signInWithPopup
  async signInWithPopup(provider) {
    console.log('🔧 Mock: Signing in with popup')
    const user = this.createMockUser('<EMAIL>')
    user.displayName = 'Mock User'
    this.currentUser = user
    this.notifyListeners(user)
    return { user }
  }

  // Mock signOut
  async signOut() {
    console.log('🔧 Mock: Signing out')
    this.currentUser = null
    this.notifyListeners(null)
  }

  // Mock updateProfile
  async updateProfile(user, profile) {
    console.log('🔧 Mock: Updating profile', profile)
    if (this.currentUser) {
      Object.assign(this.currentUser, profile)
    }
  }

  // Mock sendPasswordResetEmail
  async sendPasswordResetEmail(email) {
    console.log('🔧 Mock: Sending password reset email to', email)
  }

  // Mock onAuthStateChanged
  onAuthStateChanged(callback) {
    this.listeners.push(callback)
    // Immediately call with current user
    callback(this.currentUser)
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback)
    }
  }

  // Notify all listeners of auth state change
  notifyListeners(user) {
    this.listeners.forEach(listener => {
      try {
        listener(user)
      } catch (error) {
        console.error('Error in auth state listener:', error)
      }
    })
  }

  // Get current user
  get user() {
    return this.currentUser
  }
}

// Mock Google Auth Provider
export class MockGoogleAuthProvider {
  constructor() {
    this.providerId = 'google.com'
  }
}

// Create singleton instance
const mockAuthService = new MockAuthService()

export default mockAuthService
