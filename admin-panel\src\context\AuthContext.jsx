import React, { createContext, useContext, useEffect, useState } from 'react'
import { apiService } from '@services/apiService'
import { authService } from '@services/authService'
import toast from 'react-hot-toast'

const AuthContext = createContext({})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    checkAuthState()
  }, [])

  const checkAuthState = async () => {
    try {
      setLoading(true)
      const token = authService.getToken()

      if (token && token.startsWith('mock-admin-token')) {
        // For mock authentication, restore admin user
        const mockAdminUser = {
          id: 1,
          email: '<EMAIL>',
          first_name: 'Admin',
          last_name: 'User',
          user_type: 'admin',
          role: 'super_admin',
          permissions: ['all']
        }

        setUser(mockAdminUser)
        setIsAuthenticated(true)
      } else if (token) {
        // TODO: Verify token with backend when ready
        // For now, clear invalid tokens
        authService.removeToken()
        setUser(null)
        setIsAuthenticated(false)
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      authService.removeToken()
      setUser(null)
      setIsAuthenticated(false)
    } finally {
      setLoading(false)
    }
  }

  const login = async (email, password) => {
    try {
      setLoading(true)

      // For now, use a simple admin login check
      // TODO: Integrate with Firebase or create proper admin authentication
      if (email === '<EMAIL>' && password === 'admin123') {
        const mockAdminUser = {
          id: 1,
          email: '<EMAIL>',
          first_name: 'Admin',
          last_name: 'User',
          user_type: 'admin',
          role: 'super_admin',
          permissions: ['all']
        }

        // Generate a mock token
        const mockToken = 'mock-admin-token-' + Date.now()

        // Store token
        authService.setToken(mockToken)

        // Set user data
        setUser(mockAdminUser)
        setIsAuthenticated(true)

        toast.success('Welcome back, Admin!')
        return { success: true }
      } else {
        throw new Error('Invalid credentials')
      }
    } catch (error) {
      console.error('Login error:', error)
      const message = error.message || 'Login failed'
      toast.error(message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      // For now, just clear local state
      // TODO: Call actual logout endpoint when backend is ready
      authService.removeToken()
      setUser(null)
      setIsAuthenticated(false)
      toast.success('Logged out successfully')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const forgotPassword = async (email) => {
    try {
      const response = await apiService.post('/admin/forgot-password', {
        email,
      })

      if (response.success) {
        toast.success('Password reset instructions sent to your email')
        return { success: true }
      } else {
        throw new Error(response.message || 'Failed to send reset email')
      }
    } catch (error) {
      console.error('Forgot password error:', error)
      const message = error.response?.data?.message || error.message || 'Failed to send reset email'
      toast.error(message)
      throw error
    }
  }

  const updateProfile = async (profileData) => {
    try {
      setLoading(true)
      const response = await apiService.put('/admin/profile', profileData)

      if (response.success) {
        setUser(response.data)
        toast.success('Profile updated successfully')
        return { success: true }
      } else {
        throw new Error(response.message || 'Failed to update profile')
      }
    } catch (error) {
      console.error('Update profile error:', error)
      const message = error.response?.data?.message || error.message || 'Failed to update profile'
      toast.error(message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const changePassword = async (currentPassword, newPassword) => {
    try {
      setLoading(true)
      const response = await apiService.put('/admin/change-password', {
        current_password: currentPassword,
        new_password: newPassword,
      })

      if (response.success) {
        toast.success('Password changed successfully')
        return { success: true }
      } else {
        throw new Error(response.message || 'Failed to change password')
      }
    } catch (error) {
      console.error('Change password error:', error)
      const message = error.response?.data?.message || error.message || 'Failed to change password'
      toast.error(message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const refreshUser = async () => {
    try {
      const response = await apiService.get('/admin/profile')
      if (response.success) {
        setUser(response.data)
      }
    } catch (error) {
      console.error('Refresh user error:', error)
    }
  }

  const value = {
    // State
    user,
    loading,
    isAuthenticated,
    
    // Methods
    login,
    logout,
    forgotPassword,
    updateProfile,
    changePassword,
    refreshUser,
    
    // Computed values
    isAdmin: user?.role === 'admin',
    isSuperAdmin: user?.role === 'super_admin',
    canManageUsers: user?.permissions?.includes('manage_users') || user?.role === 'super_admin',
    canManageFinancials: user?.permissions?.includes('manage_financials') || user?.role === 'super_admin',
    canManageSettings: user?.permissions?.includes('manage_settings') || user?.role === 'super_admin',
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
