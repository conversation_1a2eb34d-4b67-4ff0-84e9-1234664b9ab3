import Toast from 'react-native-toast-message';

export const showToast = (message, type = 'info', options = {}) => {
  Toast.show({
    type,
    text1: message,
    position: 'top',
    visibilityTime: 4000,
    autoHide: true,
    topOffset: 60,
    ...options,
  });
};

export const showSuccessToast = (message, options = {}) => {
  showToast(message, 'success', options);
};

export const showErrorToast = (message, options = {}) => {
  showToast(message, 'error', options);
};

export const showInfoToast = (message, options = {}) => {
  showToast(message, 'info', options);
};

export const showWarningToast = (message, options = {}) => {
  showToast(message, 'warning', options);
};
