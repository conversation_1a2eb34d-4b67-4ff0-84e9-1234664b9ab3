import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, borderRadius } from '@constants/theme';

const BaseToast = ({ text1, type, iconName, backgroundColor, textColor }) => (
  <View style={[styles.container, { backgroundColor }]}>
    <View style={styles.iconContainer}>
      <Ionicons name={iconName} size={24} color={textColor} />
    </View>
    <Text style={[styles.text, { color: textColor }]} numberOfLines={2}>
      {text1}
    </Text>
  </View>
);

export const toastConfig = {
  success: (props) => (
    <BaseToast
      {...props}
      iconName="checkmark-circle"
      backgroundColor={colors.success[500]}
      textColor={colors.white}
    />
  ),
  error: (props) => (
    <BaseToast
      {...props}
      iconName="close-circle"
      backgroundColor={colors.error[500]}
      textColor={colors.white}
    />
  ),
  info: (props) => (
    <BaseToast
      {...props}
      iconName="information-circle"
      backgroundColor={colors.primary[500]}
      textColor={colors.white}
    />
  ),
  warning: (props) => (
    <BaseToast
      {...props}
      iconName="warning"
      backgroundColor={colors.warning[500]}
      textColor={colors.white}
    />
  ),
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginHorizontal: spacing.lg,
    borderRadius: borderRadius.lg,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconContainer: {
    marginRight: spacing.md,
  },
  text: {
    flex: 1,
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    lineHeight: typography.lineHeight.base,
  },
});
