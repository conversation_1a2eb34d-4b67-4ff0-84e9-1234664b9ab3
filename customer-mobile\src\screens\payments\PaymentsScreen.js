import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Card, List, Button, FAB } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@constants/theme';

const PaymentsScreen = ({ navigation }) => {
  // Mock payment methods data
  const paymentMethods = [
    {
      id: '1',
      type: 'card',
      last4: '4242',
      brand: 'Visa',
      isDefault: true,
    },
    {
      id: '2',
      type: 'card',
      last4: '5555',
      brand: 'Mastercard',
      isDefault: false,
    },
  ];

  const getCardIcon = (brand) => {
    switch (brand.toLowerCase()) {
      case 'visa':
        return 'card-outline';
      case 'mastercard':
        return 'card-outline';
      default:
        return 'card-outline';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>Payment Methods</Text>
        
        {paymentMethods.length > 0 ? (
          <Card style={styles.card}>
            <Card.Content style={styles.cardContent}>
              {paymentMethods.map((method, index) => (
                <React.Fragment key={method.id}>
                  <List.Item
                    title={`${method.brand} •••• ${method.last4}`}
                    description={method.isDefault ? 'Default payment method' : ''}
                    left={(props) => (
                      <List.Icon
                        {...props}
                        icon={({ size, color }) => (
                          <Ionicons name={getCardIcon(method.brand)} size={size} color={color} />
                        )}
                      />
                    )}
                    right={(props) => (
                      <View style={styles.cardActions}>
                        {method.isDefault && (
                          <Text style={styles.defaultBadge}>Default</Text>
                        )}
                        <List.Icon {...props} icon="chevron-forward" />
                      </View>
                    )}
                    onPress={() => navigation.navigate('PaymentDetail', { paymentId: method.id })}
                    style={styles.listItem}
                  />
                  {index < paymentMethods.length - 1 && (
                    <View style={styles.divider} />
                  )}
                </React.Fragment>
              ))}
            </Card.Content>
          </Card>
        ) : (
          <Card style={styles.emptyCard}>
            <Card.Content style={styles.emptyContent}>
              <Ionicons name="card-outline" size={64} color={colors.gray[400]} />
              <Text style={styles.emptyTitle}>No Payment Methods</Text>
              <Text style={styles.emptyDescription}>
                Add a payment method to book services
              </Text>
              <Button
                mode="contained"
                onPress={() => {
                  // Handle add payment method
                }}
                style={styles.addButton}
              >
                Add Payment Method
              </Button>
            </Card.Content>
          </Card>
        )}
      </ScrollView>

      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => {
          // Handle add payment method
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: spacing.md,
    paddingBottom: 100,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.lg,
  },
  card: {
    elevation: 2,
  },
  cardContent: {
    paddingVertical: spacing.xs,
  },
  listItem: {
    paddingVertical: spacing.sm,
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  defaultBadge: {
    fontSize: typography.fontSize.sm,
    color: colors.primary[600],
    fontFamily: typography.fontFamily.medium,
    marginRight: spacing.sm,
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginLeft: spacing.xl,
  },
  emptyCard: {
    elevation: 2,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyDescription: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  addButton: {
    marginTop: spacing.md,
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: colors.primary[600],
  },
});

export default PaymentsScreen;
