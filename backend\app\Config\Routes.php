<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 * Work-Boy Booking API Routes
 */

// Default route
$routes->get('/', 'Home::index');

// API Routes with versioning
$routes->group('api/v1', ['namespace' => 'App\Controllers\Api\V1'], function ($routes) {

    // Public routes (no authentication required)
    $routes->post('auth/register', 'AuthController::register');
    $routes->post('auth/login', 'AuthController::login');
    $routes->post('auth/verify-token', 'AuthController::verifyToken');
    $routes->get('tasks/categories', 'TaskController::getCategories');

    // Protected routes (authentication required)
    $routes->group('', ['filter' => 'auth'], function ($routes) {

        // Authentication routes
        $routes->get('auth/profile', 'AuthController::getProfile');
        $routes->put('auth/profile', 'AuthController::updateProfile');
        $routes->post('auth/logout', 'AuthController::logout');

        // User routes
        $routes->get('users/(:num)', 'UserController::show/$1');
        $routes->put('users/(:num)', 'UserController::update/$1');

        // Customer routes
        $routes->group('customer', function ($routes) {
            $routes->get('dashboard', 'CustomerController::dashboard');
            $routes->get('addresses', 'CustomerController::getAddresses');
            $routes->post('addresses', 'CustomerController::createAddress');
            $routes->put('addresses/(:num)', 'CustomerController::updateAddress/$1');
            $routes->delete('addresses/(:num)', 'CustomerController::deleteAddress/$1');
        });

        // Work-Boy routes
        $routes->group('workboy', function ($routes) {
            $routes->get('dashboard', 'WorkBoyController::dashboard');
            $routes->put('profile', 'WorkBoyController::updateProfile');
            $routes->post('kyc', 'WorkBoyController::submitKYC');
            $routes->get('available-tasks', 'WorkBoyController::getAvailableTasks');
            $routes->post('tasks/(:num)/accept', 'WorkBoyController::acceptTask/$1');
            $routes->post('tasks/(:num)/reject', 'WorkBoyController::rejectTask/$1');
            $routes->get('earnings', 'WorkBoyController::getEarnings');
        });

        // Task routes
        $routes->group('tasks', function ($routes) {
            $routes->get('', 'TaskController::index');
            $routes->post('', 'TaskController::create');
            $routes->get('(:num)', 'TaskController::show/$1');
            $routes->put('(:num)', 'TaskController::update/$1');
            $routes->delete('(:num)', 'TaskController::delete/$1');
            $routes->post('(:num)/assign', 'TaskController::assignTask/$1');
            $routes->post('(:num)/status', 'TaskController::updateStatus/$1');
        });

        // Payment routes
        $routes->group('payments', function ($routes) {
            $routes->post('create', 'PaymentController::create');
            $routes->post('confirm', 'PaymentController::confirm');
            $routes->get('history', 'PaymentController::getHistory');
            $routes->post('webhook', 'PaymentController::webhook');
        });

        // Review routes
        $routes->group('reviews', function ($routes) {
            $routes->post('', 'ReviewController::create');
            $routes->get('workboy/(:num)', 'ReviewController::getWorkBoyReviews/$1');
        });

        // Notification routes
        $routes->group('notifications', function ($routes) {
            $routes->get('', 'NotificationController::index');
            $routes->put('(:num)/read', 'NotificationController::markAsRead/$1');
            $routes->put('read-all', 'NotificationController::markAllAsRead');
        });

        // Admin routes (admin role required)
        $routes->group('admin', ['filter' => 'admin'], function ($routes) {
            $routes->get('dashboard', 'AdminController::dashboard');
            $routes->get('users', 'AdminController::getUsers');
            $routes->put('users/(:num)/status', 'AdminController::updateUserStatus/$1');
            $routes->get('tasks', 'AdminController::getTasks');
            $routes->get('payments', 'AdminController::getPayments');
            $routes->get('analytics', 'AdminController::getAnalytics');
            $routes->post('workboy/(:num)/approve-kyc', 'AdminController::approveKYC/$1');
            $routes->post('workboy/(:num)/reject-kyc', 'AdminController::rejectKYC/$1');
        });
    });
});

// WebSocket routes for real-time features
$routes->get('ws/task-tracking/(:num)', 'WebSocketController::taskTracking/$1');
$routes->get('ws/location-tracking/(:num)', 'WebSocketController::locationTracking/$1');
