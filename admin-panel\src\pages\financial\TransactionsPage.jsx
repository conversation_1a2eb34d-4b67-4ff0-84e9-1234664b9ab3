import React from 'react'
import { Helmet } from 'react-helmet-async'
import { BanknotesIcon } from '@heroicons/react/24/outline'
import { Card, CardHeader, CardTitle, CardContent } from '@components/ui/Card'

const TransactionsPage = () => {
  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Helmet>
        <title>Transactions - WorkBoy Admin</title>
      </Helmet>
      
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
            <BanknotesIcon className="mr-3 h-8 w-8 text-primary-600" />
            Transactions
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            View and manage all financial transactions
          </p>
        </div>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Transaction History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <BanknotesIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Transaction data will appear here when available.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default TransactionsPage
