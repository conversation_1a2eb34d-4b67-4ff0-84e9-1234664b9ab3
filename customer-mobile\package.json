{"name": "workboy-customer-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-community/datetimepicker": "^8.4.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "axios": "^1.6.2", "date-fns": "^2.30.0", "expo": "~50.0.0", "expo-async-storage": "@react-native-async-storage/async-storage", "expo-auth-session": "^6.2.1", "expo-blur": "~12.9.2", "expo-camera": "~14.1.3", "expo-constants": "~15.4.5", "expo-device": "~5.9.3", "expo-font": "~11.10.2", "expo-haptics": "~12.8.1", "expo-image-picker": "~14.7.1", "expo-linear-gradient": "~12.7.2", "expo-linking": "~6.2.2", "expo-location": "~16.5.5", "expo-notifications": "~0.27.6", "expo-permissions": "~14.4.0", "expo-router": "~3.4.7", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "expo-system-ui": "~2.9.3", "expo-web-browser": "~12.8.2", "firebase": "^11.10.0", "lottie-react-native": "6.5.1", "react": "18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-native": "0.73.0", "react-native-animatable": "^1.4.0", "react-native-collapsible": "^1.6.1", "react-native-document-picker": "^9.1.1", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.14.0", "react-native-image-viewing": "^0.2.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-maps": "1.10.0", "react-native-modal": "^13.0.1", "react-native-paper": "^5.12.3", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-share": "^10.0.2", "react-native-skeleton-placeholder": "^5.2.4", "react-native-super-grid": "^5.0.0", "react-native-svg": "14.1.0", "react-native-toast-message": "^2.2.0", "react-native-vector-icons": "^10.0.3", "react-native-web": "^0.19.13"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "babel-plugin-module-resolver": "^5.0.2", "typescript": "^5.1.3"}, "private": true}