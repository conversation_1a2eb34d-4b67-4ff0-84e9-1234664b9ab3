import React, { useState } from 'react'
import {
  Bars3Icon,
  BellIcon,
  MagnifyingGlassIcon,
  ChevronDownIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  SunIcon,
  MoonIcon,
  CommandLineIcon,
  GlobeAltIcon,
  QuestionMarkCircleIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline'
import { Menu, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { useAuth } from '@context/AuthContext'
import { clsx } from 'clsx'

export const StructuredHeader = ({ onMenuClick }) => {
  const { user, logout } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [notifications] = useState([
    { id: 1, title: 'New task assigned', message: 'Task #1234 has been assigned to <PERSON>', time: '2 min ago', unread: true },
    { id: 2, title: 'Payment received', message: '₹2,500 payment received from customer', time: '5 min ago', unread: true },
    { id: 3, title: 'KYC verification pending', message: '3 new KYC documents need review', time: '10 min ago', unread: false },
  ])

  const unreadCount = notifications.filter(n => n.unread).length

  return (
    <header className="bg-white/80 backdrop-blur-xl shadow-sm border-b border-gray-200/60 sticky top-0 z-40">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-18 justify-between items-center">
          {/* Mobile menu button */}
          <button
            type="button"
            className="lg:hidden p-3 rounded-xl text-gray-400 hover:text-primary-600 hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-300 hover:scale-110"
            onClick={onMenuClick}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Search */}
          <div className="flex-1 max-w-2xl mx-8">
            <div className="relative group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 group-focus-within:text-primary-500 transition-colors duration-300" aria-hidden="true" />
              </div>
              <input
                id="search"
                name="search"
                type="search"
                className="block w-full pl-12 pr-12 py-3.5 border border-gray-200 rounded-2xl leading-5 bg-gray-50/50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-primary-500/20 focus:border-primary-300 focus:bg-white text-sm transition-all duration-300 hover:bg-white hover:border-gray-300"
                placeholder="Search users, tasks, payments... (⌘K)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                <div className="hidden sm:flex items-center space-x-1">
                  <kbd className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-100 border border-gray-200 rounded-md">⌘</kbd>
                  <kbd className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-100 border border-gray-200 rounded-md">K</kbd>
                </div>
              </div>
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-3">
            {/* Quick Actions */}
            <div className="hidden lg:flex items-center space-x-2">
              <button className="p-2.5 rounded-xl text-gray-400 hover:text-primary-600 hover:bg-primary-50 transition-all duration-300 hover:scale-110">
                <CommandLineIcon className="h-5 w-5" />
              </button>
              <button className="p-2.5 rounded-xl text-gray-400 hover:text-warning-600 hover:bg-warning-50 transition-all duration-300 hover:scale-110">
                <SunIcon className="h-5 w-5" />
              </button>
              <button className="p-2.5 rounded-xl text-gray-400 hover:text-brand-600 hover:bg-brand-50 transition-all duration-300 hover:scale-110">
                <QuestionMarkCircleIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Notifications */}
            <Menu as="div" className="relative">
              <Menu.Button className="relative p-3 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500/20 transition-all duration-300 hover:scale-110">
                <span className="sr-only">View notifications</span>
                <BellIcon className="h-6 w-6" aria-hidden="true" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-gradient-to-r from-danger-500 to-danger-600 text-xs font-bold text-white animate-pulse">
                    {unreadCount}
                  </span>
                )}
              </Menu.Button>

              <Transition
                as={Fragment}
                enter="transition ease-out duration-200"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-150"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 z-10 mt-3 w-80 origin-top-right rounded-2xl bg-white/95 backdrop-blur-xl py-2 shadow-xl ring-1 ring-black/5 focus:outline-none">
                  <div className="px-4 py-3 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-bold text-gray-900">Notifications</h3>
                      <span className="text-xs text-primary-600 font-medium">{unreadCount} new</span>
                    </div>
                  </div>

                  <div className="max-h-80 overflow-y-auto">
                    {notifications.map((notification) => (
                      <Menu.Item key={notification.id}>
                        {({ active }) => (
                          <div className={clsx(
                            'px-4 py-3 transition-colors duration-200',
                            active ? 'bg-primary-50' : '',
                            notification.unread ? 'bg-primary-50/30' : ''
                          )}>
                            <div className="flex items-start space-x-3">
                              <div className={clsx(
                                'flex-shrink-0 w-2 h-2 rounded-full mt-2',
                                notification.unread ? 'bg-primary-500' : 'bg-gray-300'
                              )}></div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                  {notification.title}
                                </p>
                                <p className="text-sm text-gray-500 mt-1">
                                  {notification.message}
                                </p>
                                <p className="text-xs text-gray-400 mt-1">
                                  {notification.time}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </Menu.Item>
                    ))}
                  </div>

                  <div className="px-4 py-2 border-t border-gray-100">
                    <button className="w-full text-center text-sm text-primary-600 hover:text-primary-700 font-medium">
                      View all notifications
                    </button>
                  </div>
                </Menu.Items>
              </Transition>
            </Menu>

            {/* User menu */}
            <Menu as="div" className="relative">
              <Menu.Button className="flex items-center space-x-3 p-2 rounded-xl hover:bg-gradient-to-r hover:from-primary-50 hover:to-brand-50/30 transition-all duration-300 group">
                <div className="relative">
                  <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary-600 via-primary-500 to-brand-600 flex items-center justify-center shadow-lg shadow-primary-500/25 group-hover:shadow-primary-500/40 transition-all duration-300">
                    <span className="text-sm font-bold text-white">
                      {user?.name?.charAt(0) || 'A'}
                    </span>
                  </div>
                  <div className="absolute -bottom-1 -right-1 h-3 w-3 bg-gradient-to-br from-success-400 to-success-500 rounded-full border-2 border-white animate-pulse"></div>
                </div>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-bold text-gray-900 group-hover:text-primary-700 transition-colors duration-300">
                    {user?.name || 'Admin User'}
                  </div>
                  <div className="text-xs text-gray-500 font-medium">
                    {user?.role || 'Administrator'}
                  </div>
                </div>
                <ChevronDownIcon className="h-4 w-4 text-gray-400 group-hover:text-primary-600 transition-all duration-300 group-hover:rotate-180" />
              </Menu.Button>

              <Transition
                as={Fragment}
                enter="transition ease-out duration-200"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-150"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 z-10 mt-3 w-64 origin-top-right rounded-2xl bg-white/95 backdrop-blur-xl py-2 shadow-xl ring-1 ring-black/5 focus:outline-none">
                  <div className="px-4 py-4 border-b border-gray-100/60">
                    <div className="flex items-center space-x-3">
                      <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-primary-600 via-primary-500 to-brand-600 flex items-center justify-center shadow-lg shadow-primary-500/25">
                        <span className="text-white font-bold text-lg">
                          {user?.name?.charAt(0) || 'A'}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-bold text-gray-900">{user?.name || 'Admin User'}</p>
                        <p className="text-xs text-gray-500 truncate">{user?.email || '<EMAIL>'}</p>
                        <div className="flex items-center mt-1">
                          <div className="h-2 w-2 bg-success-400 rounded-full mr-2 animate-pulse"></div>
                          <span className="text-xs text-success-600 font-medium">Online</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="py-2">
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          className={clsx(
                            'group flex w-full items-center px-4 py-3 text-sm font-medium transition-all duration-200',
                            active ? 'bg-gradient-to-r from-primary-50 to-brand-50/30 text-primary-700' : 'text-gray-700 hover:text-primary-600'
                          )}
                        >
                          <div className={clsx(
                            'p-2 rounded-lg mr-3 transition-all duration-200',
                            active ? 'bg-primary-100 text-primary-600' : 'bg-gray-100 text-gray-500 group-hover:bg-primary-100 group-hover:text-primary-600'
                          )}>
                            <UserCircleIcon className="h-4 w-4" />
                          </div>
                          <div>
                            <div>Your Profile</div>
                            <div className="text-xs text-gray-500">Manage your account</div>
                          </div>
                        </button>
                      )}
                    </Menu.Item>

                    <Menu.Item>
                      {({ active }) => (
                        <button
                          className={clsx(
                            'group flex w-full items-center px-4 py-3 text-sm font-medium transition-all duration-200',
                            active ? 'bg-gradient-to-r from-primary-50 to-brand-50/30 text-primary-700' : 'text-gray-700 hover:text-primary-600'
                          )}
                        >
                          <div className={clsx(
                            'p-2 rounded-lg mr-3 transition-all duration-200',
                            active ? 'bg-primary-100 text-primary-600' : 'bg-gray-100 text-gray-500 group-hover:bg-primary-100 group-hover:text-primary-600'
                          )}>
                            <Cog6ToothIcon className="h-4 w-4" />
                          </div>
                          <div>
                            <div>Settings</div>
                            <div className="text-xs text-gray-500">Preferences & config</div>
                          </div>
                        </button>
                      )}
                    </Menu.Item>

                    <Menu.Item>
                      {({ active }) => (
                        <button
                          className={clsx(
                            'group flex w-full items-center px-4 py-3 text-sm font-medium transition-all duration-200',
                            active ? 'bg-gradient-to-r from-primary-50 to-brand-50/30 text-primary-700' : 'text-gray-700 hover:text-primary-600'
                          )}
                        >
                          <div className={clsx(
                            'p-2 rounded-lg mr-3 transition-all duration-200',
                            active ? 'bg-primary-100 text-primary-600' : 'bg-gray-100 text-gray-500 group-hover:bg-primary-100 group-hover:text-primary-600'
                          )}>
                            <QuestionMarkCircleIcon className="h-4 w-4" />
                          </div>
                          <div>
                            <div>Help & Support</div>
                            <div className="text-xs text-gray-500">Get assistance</div>
                          </div>
                        </button>
                      )}
                    </Menu.Item>
                  </div>

                  <div className="border-t border-gray-100/60 pt-2">
                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={logout}
                          className={clsx(
                            'group flex w-full items-center px-4 py-3 text-sm font-medium transition-all duration-200',
                            active ? 'bg-gradient-to-r from-danger-50 to-danger-100/50 text-danger-700' : 'text-gray-700 hover:text-danger-600'
                          )}
                        >
                          <div className={clsx(
                            'p-2 rounded-lg mr-3 transition-all duration-200',
                            active ? 'bg-danger-100 text-danger-600' : 'bg-gray-100 text-gray-500 group-hover:bg-danger-100 group-hover:text-danger-600'
                          )}>
                            <ArrowRightOnRectangleIcon className="h-4 w-4" />
                          </div>
                          <div>
                            <div>Sign out</div>
                            <div className="text-xs text-gray-500">End your session</div>
                          </div>
                        </button>
                      )}
                    </Menu.Item>
                  </div>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>
    </header>
  )
}
