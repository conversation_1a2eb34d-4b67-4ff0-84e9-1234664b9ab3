<?php

namespace App\Services;

/**
 * Razorpay Service for Work-Boy Booking API
 * 
 * Handles Razorpay payment gateway integration
 */
class RazorpayService
{
    private $keyId;
    private $keySecret;
    private $webhookSecret;

    public function __construct()
    {
        $this->keyId = env('RAZORPAY_KEY_ID');
        $this->keySecret = env('RAZORPAY_KEY_SECRET');
        $this->webhookSecret = env('RAZORPAY_WEBHOOK_SECRET');
    }

    /**
     * Create Razorpay order
     */
    public function createOrder(float $amount, string $currency = 'INR', array $metadata = [])
    {
        try {
            // In a real implementation, you would use the Razorpay PHP SDK
            // For now, we'll simulate the order creation
            
            $orderData = [
                'id' => 'order_' . uniqid(),
                'amount' => $amount * 100, // Razorpay expects amount in paise
                'currency' => $currency,
                'status' => 'created',
                'created_at' => time(),
                'notes' => $metadata,
            ];

            return [
                'success' => true,
                'order' => $orderData,
            ];

        } catch (\Exception $e) {
            log_message('error', 'Razorpay create order error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify payment signature
     */
    public function verifyPaymentSignature(string $paymentId, string $orderId, string $signature): bool
    {
        try {
            // In a real implementation, you would verify the signature using Razorpay SDK
            // $expectedSignature = hash_hmac('sha256', $orderId . '|' . $paymentId, $this->keySecret);
            // return hash_equals($expectedSignature, $signature);
            
            // For demo purposes, we'll return true
            return true;

        } catch (\Exception $e) {
            log_message('error', 'Razorpay signature verification error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Capture payment
     */
    public function capturePayment(string $paymentId, float $amount)
    {
        try {
            // In a real implementation, you would capture the payment using Razorpay SDK
            
            $captureData = [
                'id' => $paymentId,
                'amount' => $amount * 100,
                'status' => 'captured',
                'captured_at' => time(),
            ];

            return [
                'success' => true,
                'payment' => $captureData,
            ];

        } catch (\Exception $e) {
            log_message('error', 'Razorpay capture payment error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Refund payment
     */
    public function refundPayment(string $paymentId, float $amount = null, string $reason = null)
    {
        try {
            // In a real implementation, you would process refund using Razorpay SDK
            
            $refundData = [
                'id' => 'rfnd_' . uniqid(),
                'payment_id' => $paymentId,
                'amount' => $amount ? $amount * 100 : null,
                'status' => 'processed',
                'created_at' => time(),
                'notes' => [
                    'reason' => $reason ?? 'Refund requested by customer',
                ],
            ];

            return [
                'success' => true,
                'refund' => $refundData,
            ];

        } catch (\Exception $e) {
            log_message('error', 'Razorpay refund payment error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        try {
            $expectedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);
            return hash_equals($expectedSignature, $signature);

        } catch (\Exception $e) {
            log_message('error', 'Razorpay webhook verification error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment details
     */
    public function getPaymentDetails(string $paymentId)
    {
        try {
            // In a real implementation, you would fetch payment details using Razorpay SDK
            
            $paymentData = [
                'id' => $paymentId,
                'status' => 'captured',
                'amount' => 50000, // Example amount in paise
                'currency' => 'INR',
                'method' => 'card',
                'created_at' => time(),
            ];

            return [
                'success' => true,
                'payment' => $paymentData,
            ];

        } catch (\Exception $e) {
            log_message('error', 'Razorpay get payment details error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create customer
     */
    public function createCustomer(array $customerData)
    {
        try {
            // In a real implementation, you would create customer using Razorpay SDK
            
            $customer = [
                'id' => 'cust_' . uniqid(),
                'name' => $customerData['name'],
                'email' => $customerData['email'],
                'contact' => $customerData['phone'] ?? null,
                'created_at' => time(),
            ];

            return [
                'success' => true,
                'customer' => $customer,
            ];

        } catch (\Exception $e) {
            log_message('error', 'Razorpay create customer error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create subscription (for future recurring payments)
     */
    public function createSubscription(string $planId, string $customerId, array $options = [])
    {
        try {
            // In a real implementation, you would create subscription using Razorpay SDK
            
            $subscription = [
                'id' => 'sub_' . uniqid(),
                'plan_id' => $planId,
                'customer_id' => $customerId,
                'status' => 'created',
                'created_at' => time(),
            ];

            return [
                'success' => true,
                'subscription' => $subscription,
            ];

        } catch (\Exception $e) {
            log_message('error', 'Razorpay create subscription error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get settlement details
     */
    public function getSettlements(array $filters = [])
    {
        try {
            // In a real implementation, you would fetch settlements using Razorpay SDK
            
            $settlements = [
                [
                    'id' => 'setl_' . uniqid(),
                    'amount' => 95000, // Amount in paise after fees
                    'fees' => 5000,
                    'tax' => 900,
                    'status' => 'processed',
                    'created_at' => time() - 86400,
                ],
            ];

            return [
                'success' => true,
                'settlements' => $settlements,
            ];

        } catch (\Exception $e) {
            log_message('error', 'Razorpay get settlements error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate payment amount
     */
    public function validateAmount(float $amount): bool
    {
        // Minimum amount is ₹1 (100 paise)
        // Maximum amount is ₹15,00,000 (15000000 paise)
        return $amount >= 1 && $amount <= 1500000;
    }

    /**
     * Format amount for display
     */
    public function formatAmount(int $amountInPaise): string
    {
        $amount = $amountInPaise / 100;
        return '₹' . number_format($amount, 2);
    }

    /**
     * Convert amount to paise
     */
    public function convertToPaise(float $amount): int
    {
        return (int) round($amount * 100);
    }

    /**
     * Convert amount from paise
     */
    public function convertFromPaise(int $amountInPaise): float
    {
        return $amountInPaise / 100;
    }

    /**
     * Get supported payment methods
     */
    public function getSupportedPaymentMethods(): array
    {
        return [
            'card' => [
                'name' => 'Credit/Debit Card',
                'types' => ['visa', 'mastercard', 'amex', 'rupay'],
            ],
            'netbanking' => [
                'name' => 'Net Banking',
                'banks' => ['hdfc', 'icici', 'sbi', 'axis', 'kotak'],
            ],
            'wallet' => [
                'name' => 'Digital Wallets',
                'providers' => ['paytm', 'phonepe', 'googlepay', 'amazonpay'],
            ],
            'upi' => [
                'name' => 'UPI',
                'providers' => ['googlepay', 'phonepe', 'paytm', 'bhim'],
            ],
        ];
    }

    /**
     * Calculate platform fee
     */
    public function calculatePlatformFee(float $amount): array
    {
        // Example fee structure: 2% + ₹2
        $percentageFee = $amount * 0.02;
        $fixedFee = 2.00;
        $totalFee = $percentageFee + $fixedFee;
        
        // GST on fee (18%)
        $gst = $totalFee * 0.18;
        $totalWithGst = $totalFee + $gst;

        return [
            'base_amount' => $amount,
            'percentage_fee' => $percentageFee,
            'fixed_fee' => $fixedFee,
            'total_fee' => $totalFee,
            'gst' => $gst,
            'total_fee_with_gst' => $totalWithGst,
            'net_amount' => $amount - $totalWithGst,
        ];
    }
}
