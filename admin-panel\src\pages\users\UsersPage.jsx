import { useState, useEffect } from 'react'
import { Helm<PERSON> } from 'react-helmet-async'
import { <PERSON> } from 'react-router-dom'
import {
  UsersIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  UserCircleIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarDaysIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline'
import PremiumCard, { PremiumCardHeader, PremiumCardTitle, PremiumCardContent } from '../../components/ui/PremiumCard'
import PremiumButton from '../../components/ui/PremiumButton'
import PremiumInput from '../../components/ui/PremiumInput'

const UsersPage = () => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const usersPerPage = 10

  // Mock data
  useEffect(() => {
    const mockUsers = [
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        location: 'Mumbai, Maharashtra',
        joinDate: '2024-01-15',
        status: 'active',
        totalTasks: 45,
        completedTasks: 42,
        rating: 4.8,
        avatar: null
      },
      {
        id: 2,
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        location: 'Delhi, Delhi',
        joinDate: '2024-02-20',
        status: 'active',
        totalTasks: 32,
        completedTasks: 30,
        rating: 4.6,
        avatar: null
      },
      {
        id: 3,
        name: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        location: 'Bangalore, Karnataka',
        joinDate: '2024-01-10',
        status: 'inactive',
        totalTasks: 28,
        completedTasks: 25,
        rating: 4.4,
        avatar: null
      },
      {
        id: 4,
        name: 'Sarah Wilson',
        email: '<EMAIL>',
        phone: '+91 9876543213',
        location: 'Chennai, Tamil Nadu',
        joinDate: '2024-03-05',
        status: 'active',
        totalTasks: 18,
        completedTasks: 16,
        rating: 4.9,
        avatar: null
      },
      {
        id: 5,
        name: 'David Brown',
        email: '<EMAIL>',
        phone: '+91 9876543214',
        location: 'Pune, Maharashtra',
        joinDate: '2024-02-28',
        status: 'suspended',
        totalTasks: 12,
        completedTasks: 8,
        rating: 3.8,
        avatar: null
      }
    ]

    setTimeout(() => {
      setUsers(mockUsers)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.phone.includes(searchQuery)
    const matchesFilter = filterStatus === 'all' || user.status === filterStatus
    return matchesSearch && matchesFilter
  })

  const totalPages = Math.ceil(filteredUsers.length / usersPerPage)
  const startIndex = (currentPage - 1) * usersPerPage
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + usersPerPage)

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { bg: 'bg-success-100', text: 'text-success-800', icon: CheckCircleIcon },
      inactive: { bg: 'bg-gray-100', text: 'text-gray-800', icon: XCircleIcon },
      suspended: { bg: 'bg-danger-100', text: 'text-danger-800', icon: XCircleIcon }
    }

    const config = statusConfig[status] || statusConfig.inactive
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-bold ${config.bg} ${config.text}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    )
  }

  const getRatingStars = (rating) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(<span key={i} className="text-warning-400">★</span>)
    }

    if (hasHalfStar) {
      stars.push(<span key="half" className="text-warning-400">☆</span>)
    }

    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<span key={`empty-${i}`} className="text-gray-300">☆</span>)
    }

    return <div className="flex items-center">{stars}</div>
  }

  if (loading) {
    return (
      <div className="space-y-6 animate-fade-in-up">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 animate-fade-in-up">
      <Helmet>
        <title>Users Management - WorkBoy Admin</title>
      </Helmet>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-black bg-gradient-to-r from-gray-900 via-primary-800 to-brand-700 bg-clip-text text-transparent">
            Users Management
          </h1>
          <p className="text-gray-600 mt-2 font-medium">Manage customer accounts and profiles</p>
        </div>
        <div className="flex space-x-3">
          <PremiumButton variant="outline" icon={ArrowDownTrayIcon}>
            Export
          </PremiumButton>
          <PremiumButton variant="primary" icon={PlusIcon}>
            Add User
          </PremiumButton>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 shadow-lg shadow-primary-500/25">
              <UsersIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Total Users</p>
              <p className="text-2xl font-black text-gray-900">{users.length}</p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-success-500 to-success-600 shadow-lg shadow-success-500/25">
              <CheckCircleIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Active Users</p>
              <p className="text-2xl font-black text-gray-900">
                {users.filter(u => u.status === 'active').length}
              </p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-warning-500 to-warning-600 shadow-lg shadow-warning-500/25">
              <XCircleIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Inactive</p>
              <p className="text-2xl font-black text-gray-900">
                {users.filter(u => u.status === 'inactive').length}
              </p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-danger-500 to-danger-600 shadow-lg shadow-danger-500/25">
              <XCircleIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Suspended</p>
              <p className="text-2xl font-black text-gray-900">
                {users.filter(u => u.status === 'suspended').length}
              </p>
            </div>
          </div>
        </PremiumCard>
      </div>

      {/* Filters and Search */}
      <PremiumCard gradient>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1 max-w-md">
            <PremiumInput
              placeholder="Search users by name, email, or phone..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={MagnifyingGlassIcon}
              variant="premium"
            />
          </div>

          <div className="flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-xl text-sm font-medium bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-300"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>

            <PremiumButton variant="outline" icon={FunnelIcon} size="sm">
              More Filters
            </PremiumButton>
          </div>
        </div>
      </PremiumCard>

      {/* Users Table */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={UsersIcon}>
            Users List ({filteredUsers.length} users)
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Tasks
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Rating
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 flex items-center justify-center shadow-lg shadow-primary-500/25">
                          <span className="text-white font-bold text-sm">
                            {user.name.charAt(0)}
                          </span>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-bold text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <CalendarDaysIcon className="h-3 w-3 mr-1" />
                            Joined {new Date(user.joinDate).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <div className="text-sm text-gray-900 flex items-center">
                          <EnvelopeIcon className="h-3 w-3 mr-2 text-gray-400" />
                          {user.email}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <PhoneIcon className="h-3 w-3 mr-2 text-gray-400" />
                          {user.phone}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 flex items-center">
                        <MapPinIcon className="h-3 w-3 mr-2 text-gray-400" />
                        {user.location}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="font-bold">{user.completedTasks}/{user.totalTasks}</div>
                        <div className="text-xs text-gray-500">
                          {Math.round((user.completedTasks / user.totalTasks) * 100)}% completion
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {getRatingStars(user.rating)}
                        <span className="text-sm font-bold text-gray-900">{user.rating}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(user.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/users/${user.id}`}
                          className="text-primary-600 hover:text-primary-900 p-1 rounded-lg hover:bg-primary-50 transition-colors duration-200"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Link>
                        <button className="text-warning-600 hover:text-warning-900 p-1 rounded-lg hover:bg-warning-50 transition-colors duration-200">
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button className="text-danger-600 hover:text-danger-900 p-1 rounded-lg hover:bg-danger-50 transition-colors duration-200">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
              <div className="text-sm text-gray-700">
                Showing {startIndex + 1} to {Math.min(startIndex + usersPerPage, filteredUsers.length)} of {filteredUsers.length} users
              </div>
              <div className="flex items-center space-x-2">
                <PremiumButton
                  variant="outline"
                  size="sm"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(currentPage - 1)}
                >
                  Previous
                </PremiumButton>

                {[...Array(totalPages)].map((_, i) => (
                  <button
                    key={i + 1}
                    onClick={() => setCurrentPage(i + 1)}
                    className={`px-3 py-1 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      currentPage === i + 1
                        ? 'bg-primary-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {i + 1}
                  </button>
                ))}

                <PremiumButton
                  variant="outline"
                  size="sm"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(currentPage + 1)}
                >
                  Next
                </PremiumButton>
              </div>
            </div>
          )}
        </PremiumCardContent>
      </PremiumCard>
    </div>
  )
}

export default UsersPage
