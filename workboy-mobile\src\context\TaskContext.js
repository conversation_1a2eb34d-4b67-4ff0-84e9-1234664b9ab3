import React, { createContext, useContext, useEffect, useState } from 'react';
import { apiService, endpoints } from '@services/apiService';
import { showToast } from '@utils/toast';

const TaskContext = createContext({});

export const useTask = () => {
  const context = useContext(TaskContext);
  if (!context) {
    throw new Error('useTask must be used within a TaskProvider');
  }
  return context;
};

export const TaskProvider = ({ children }) => {
  const [tasks, setTasks] = useState([]);
  const [availableTasks, setAvailableTasks] = useState([]);
  const [currentTask, setCurrentTask] = useState(null);
  const [taskHistory, setTaskHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchTasks();
    fetchAvailableTasks();
    fetchCurrentTask();
  }, []);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/workboy/tasks');
      
      if (response.success) {
        setTasks(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableTasks = async () => {
    try {
      const response = await apiService.get('/workboy/tasks/available');
      
      if (response.success) {
        setAvailableTasks(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch available tasks:', error);
    }
  };

  const fetchCurrentTask = async () => {
    try {
      const response = await apiService.get('/workboy/tasks/current');
      
      if (response.success) {
        setCurrentTask(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch current task:', error);
    }
  };

  const fetchTaskHistory = async () => {
    try {
      const response = await apiService.get('/workboy/tasks/history');
      
      if (response.success) {
        setTaskHistory(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch task history:', error);
    }
  };

  const acceptTask = async (taskId) => {
    try {
      const response = await apiService.post(`/workboy/tasks/${taskId}/accept`);
      
      if (response.success) {
        // Update local state
        setAvailableTasks(prev => prev.filter(task => task.id !== taskId));
        setCurrentTask(response.data);
        
        showToast('Task accepted successfully!', 'success');
        return { success: true, data: response.data };
      } else {
        throw new Error(response.message || 'Failed to accept task');
      }
    } catch (error) {
      console.error('Accept task error:', error);
      showToast(error.message || 'Failed to accept task', 'error');
      throw error;
    }
  };

  const rejectTask = async (taskId, reason = '') => {
    try {
      const response = await apiService.post(`/workboy/tasks/${taskId}/reject`, {
        reason,
      });
      
      if (response.success) {
        // Update local state
        setAvailableTasks(prev => prev.filter(task => task.id !== taskId));
        
        showToast('Task rejected', 'info');
        return { success: true };
      } else {
        throw new Error(response.message || 'Failed to reject task');
      }
    } catch (error) {
      console.error('Reject task error:', error);
      showToast(error.message || 'Failed to reject task', 'error');
      throw error;
    }
  };

  const updateTaskStatus = async (taskId, status, data = {}) => {
    try {
      const response = await apiService.put(`/workboy/tasks/${taskId}/status`, {
        status,
        ...data,
      });
      
      if (response.success) {
        // Update local state
        if (currentTask && currentTask.id === taskId) {
          setCurrentTask(response.data);
        }
        
        setTasks(prev => 
          prev.map(task => 
            task.id === taskId ? response.data : task
          )
        );
        
        const statusMessages = {
          'started': 'Task started',
          'in_progress': 'Task in progress',
          'completed': 'Task completed successfully!',
          'cancelled': 'Task cancelled',
        };
        
        showToast(statusMessages[status] || 'Task status updated', 'success');
        return { success: true, data: response.data };
      } else {
        throw new Error(response.message || 'Failed to update task status');
      }
    } catch (error) {
      console.error('Update task status error:', error);
      showToast(error.message || 'Failed to update task status', 'error');
      throw error;
    }
  };

  const startTask = async (taskId) => {
    return updateTaskStatus(taskId, 'started');
  };

  const completeTask = async (taskId, completionData) => {
    try {
      const response = await apiService.post(`/workboy/tasks/${taskId}/complete`, completionData);
      
      if (response.success) {
        // Update local state
        setCurrentTask(null);
        setTasks(prev => 
          prev.map(task => 
            task.id === taskId ? response.data : task
          )
        );
        
        showToast('Task completed successfully!', 'success');
        return { success: true, data: response.data };
      } else {
        throw new Error(response.message || 'Failed to complete task');
      }
    } catch (error) {
      console.error('Complete task error:', error);
      showToast(error.message || 'Failed to complete task', 'error');
      throw error;
    }
  };

  const cancelTask = async (taskId, reason = '') => {
    try {
      const response = await apiService.post(`/workboy/tasks/${taskId}/cancel`, {
        reason,
      });
      
      if (response.success) {
        // Update local state
        if (currentTask && currentTask.id === taskId) {
          setCurrentTask(null);
        }
        
        setTasks(prev => 
          prev.map(task => 
            task.id === taskId ? response.data : task
          )
        );
        
        showToast('Task cancelled', 'info');
        return { success: true, data: response.data };
      } else {
        throw new Error(response.message || 'Failed to cancel task');
      }
    } catch (error) {
      console.error('Cancel task error:', error);
      showToast(error.message || 'Failed to cancel task', 'error');
      throw error;
    }
  };

  const uploadTaskImages = async (taskId, images) => {
    try {
      const response = await apiService.uploadFiles(`/workboy/tasks/${taskId}/images`, images);
      
      if (response.success) {
        showToast('Images uploaded successfully', 'success');
        return { success: true, data: response.data };
      } else {
        throw new Error(response.message || 'Failed to upload images');
      }
    } catch (error) {
      console.error('Upload images error:', error);
      showToast(error.message || 'Failed to upload images', 'error');
      throw error;
    }
  };

  const refreshTasks = async () => {
    try {
      setRefreshing(true);
      await Promise.all([
        fetchTasks(),
        fetchAvailableTasks(),
        fetchCurrentTask(),
      ]);
    } catch (error) {
      console.error('Refresh tasks error:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getTaskById = (taskId) => {
    return tasks.find(task => task.id === taskId) || 
           availableTasks.find(task => task.id === taskId) ||
           (currentTask && currentTask.id === taskId ? currentTask : null);
  };

  const getTasksByStatus = (status) => {
    return tasks.filter(task => task.status === status);
  };

  const value = {
    // State
    tasks,
    availableTasks,
    currentTask,
    taskHistory,
    loading,
    refreshing,
    
    // Methods
    fetchTasks,
    fetchAvailableTasks,
    fetchCurrentTask,
    fetchTaskHistory,
    acceptTask,
    rejectTask,
    updateTaskStatus,
    startTask,
    completeTask,
    cancelTask,
    uploadTaskImages,
    refreshTasks,
    
    // Utility methods
    getTaskById,
    getTasksByStatus,
    
    // Computed values
    hasTasks: tasks.length > 0,
    hasAvailableTasks: availableTasks.length > 0,
    hasCurrentTask: !!currentTask,
    pendingTasks: getTasksByStatus('pending'),
    inProgressTasks: getTasksByStatus('in_progress'),
    completedTasks: getTasksByStatus('completed'),
  };

  return (
    <TaskContext.Provider value={value}>
      {children}
    </TaskContext.Provider>
  );
};
