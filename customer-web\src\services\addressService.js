import api from './api'

export const addressService = {
  // Get all addresses for the current user
  getAddresses: async () => {
    return api.get('/addresses')
  },

  // Get a specific address by ID
  getAddress: async (id) => {
    return api.get(`/addresses/${id}`)
  },

  // Create a new address
  createAddress: async (addressData) => {
    return api.post('/addresses', addressData)
  },

  // Update an existing address
  updateAddress: async (id, addressData) => {
    return api.put(`/addresses/${id}`, addressData)
  },

  // Delete an address
  deleteAddress: async (id) => {
    return api.delete(`/addresses/${id}`)
  },

  // Set an address as default
  setDefaultAddress: async (id) => {
    return api.put(`/addresses/${id}/set-default`)
  },

  // Validate an address
  validateAddress: async (addressData) => {
    return api.post('/addresses/validate', addressData)
  },

  // Get address suggestions based on input
  getAddressSuggestions: async (query) => {
    return api.get(`/addresses/suggestions?q=${encodeURIComponent(query)}`)
  }
}

export default addressService
