<?php

namespace App\Controllers\Api\V1;

use App\Controllers\Api\BaseApiController;
use App\Models\NotificationModel;

/**
 * Notification Controller for Work-Boy Booking API
 * 
 * Handles user notifications and push notification management
 */
class NotificationController extends BaseApiController
{
    protected $notificationModel;

    public function __construct()
    {
        parent::__construct();
        $this->notificationModel = new NotificationModel();
    }

    /**
     * Get user notifications
     * GET /api/v1/notifications
     */
    public function index()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $pagination = $this->getPaginationParams();
            $type = $this->request->getGet('type');
            $isRead = $this->request->getGet('is_read');
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');

            $filters = [];
            if ($type) $filters['type'] = $type;
            if ($isRead !== null) $filters['is_read'] = (int) $isRead;
            if ($dateFrom) $filters['date_from'] = $dateFrom;
            if ($dateTo) $filters['date_to'] = $dateTo;

            // Get notifications
            $notifications = $this->notificationModel->getFilteredNotifications(
                $currentUser['id'],
                $filters,
                $pagination['limit'],
                $pagination['offset']
            );

            // Get total count for pagination
            $total = $this->notificationModel->where('user_id', $currentUser['id'])->countAllResults();

            // Get unread count
            $unreadCount = $this->notificationModel->getUnreadCount($currentUser['id']);

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);
            $meta['unread_count'] = $unreadCount;

            return $this->apiResponse($notifications, 'Notifications retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Get notifications error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve notifications');
        }
    }

    /**
     * Mark notification as read
     * PUT /api/v1/notifications/{id}/read
     */
    public function markAsRead($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $notification = $this->notificationModel->where('id', $id)
                                                  ->where('user_id', $currentUser['id'])
                                                  ->first();

            if (!$notification) {
                return $this->notFoundResponse('Notification not found');
            }

            if ($notification['is_read']) {
                return $this->apiResponse(null, 'Notification already marked as read');
            }

            $updated = $this->notificationModel->markAsRead($id, $currentUser['id']);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to mark notification as read');
            }

            $this->logActivity('notification_read', ['notification_id' => $id]);

            return $this->apiResponse(null, 'Notification marked as read');

        } catch (\Exception $e) {
            log_message('error', 'Mark notification as read error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to mark notification as read');
        }
    }

    /**
     * Mark all notifications as read
     * PUT /api/v1/notifications/read-all
     */
    public function markAllAsRead()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $updated = $this->notificationModel->markAllAsRead($currentUser['id']);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to mark all notifications as read');
            }

            $this->logActivity('all_notifications_read', ['user_id' => $currentUser['id']]);

            return $this->apiResponse(null, 'All notifications marked as read');

        } catch (\Exception $e) {
            log_message('error', 'Mark all notifications as read error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to mark all notifications as read');
        }
    }

    /**
     * Delete notification
     * DELETE /api/v1/notifications/{id}
     */
    public function delete($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $notification = $this->notificationModel->where('id', $id)
                                                  ->where('user_id', $currentUser['id'])
                                                  ->first();

            if (!$notification) {
                return $this->notFoundResponse('Notification not found');
            }

            $deleted = $this->notificationModel->deleteNotification($id, $currentUser['id']);

            if (!$deleted) {
                return $this->serverErrorResponse('Failed to delete notification');
            }

            $this->logActivity('notification_deleted', ['notification_id' => $id]);

            return $this->apiResponse(null, 'Notification deleted successfully');

        } catch (\Exception $e) {
            log_message('error', 'Delete notification error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to delete notification');
        }
    }

    /**
     * Get notification statistics
     * GET /api/v1/notifications/stats
     */
    public function getStats()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $stats = $this->notificationModel->getNotificationStats($currentUser['id']);

            return $this->apiResponse($stats, 'Notification statistics retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get notification stats error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve notification statistics');
        }
    }

    /**
     * Search notifications
     * GET /api/v1/notifications/search
     */
    public function search()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $query = $this->request->getGet('q');
            
            if (!$query) {
                return $this->errorResponse('Search query is required', 400);
            }

            $pagination = $this->getPaginationParams();

            $notifications = $this->notificationModel->searchNotifications(
                $currentUser['id'],
                $query,
                $pagination['limit'],
                $pagination['offset']
            );

            // Get approximate total count
            $total = count($notifications);

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($notifications, 'Search results retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Search notifications error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to search notifications');
        }
    }

    /**
     * Bulk mark notifications as read
     * PUT /api/v1/notifications/bulk-read
     */
    public function bulkMarkAsRead()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $rules = [
                'notification_ids' => 'required|valid_json',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);
            $notificationIds = $data['notification_ids'];

            if (!is_array($notificationIds) || empty($notificationIds)) {
                return $this->errorResponse('Invalid notification IDs', 400);
            }

            $updated = $this->notificationModel->bulkMarkAsRead($notificationIds, $currentUser['id']);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to mark notifications as read');
            }

            $this->logActivity('bulk_notifications_read', [
                'notification_ids' => $notificationIds,
                'count' => count($notificationIds)
            ]);

            return $this->apiResponse(null, 'Notifications marked as read successfully');

        } catch (\Exception $e) {
            log_message('error', 'Bulk mark as read error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to mark notifications as read');
        }
    }

    /**
     * Get notification preferences
     * GET /api/v1/notifications/preferences
     */
    public function getPreferences()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $preferences = $this->notificationModel->getUserNotificationPreferences($currentUser['id']);

            return $this->apiResponse($preferences, 'Notification preferences retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get notification preferences error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve notification preferences');
        }
    }

    /**
     * Update notification preferences
     * PUT /api/v1/notifications/preferences
     */
    public function updatePreferences()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $rules = [
                'email_notifications' => 'permit_empty|in_list[0,1]',
                'push_notifications'  => 'permit_empty|in_list[0,1]',
                'sms_notifications'   => 'permit_empty|in_list[0,1]',
                'task_updates'        => 'permit_empty|in_list[0,1]',
                'promotional'         => 'permit_empty|in_list[0,1]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Get current preferences
            $currentPreferences = $this->notificationModel->getUserNotificationPreferences($currentUser['id']);

            // Update preferences
            $updatedPreferences = array_merge($currentPreferences, $data);

            // Update in customer profile
            $customerProfileModel = new \App\Models\CustomerProfileModel();
            $updated = $customerProfileModel->updateNotificationPreferences($currentUser['id'], $updatedPreferences);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to update notification preferences');
            }

            $this->logActivity('notification_preferences_updated', [
                'updated_fields' => array_keys($data)
            ]);

            return $this->apiResponse($updatedPreferences, 'Notification preferences updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Update notification preferences error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to update notification preferences');
        }
    }

    /**
     * Send test notification (Admin only)
     * POST /api/v1/notifications/test
     */
    public function sendTestNotification()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $rules = [
                'user_id' => 'required|integer',
                'title'   => 'required|max_length[255]',
                'message' => 'required',
                'type'    => 'permit_empty|in_list[task_update,payment,system,promotion]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            $notificationId = $this->notificationModel->insert([
                'user_id' => $data['user_id'],
                'title' => $data['title'],
                'message' => $data['message'],
                'type' => $data['type'] ?? 'system',
                'is_read' => 0,
            ]);

            if (!$notificationId) {
                return $this->serverErrorResponse('Failed to send test notification');
            }

            $this->logActivity('test_notification_sent', [
                'notification_id' => $notificationId,
                'target_user_id' => $data['user_id']
            ]);

            return $this->apiResponse(['notification_id' => $notificationId], 'Test notification sent successfully');

        } catch (\Exception $e) {
            log_message('error', 'Send test notification error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to send test notification');
        }
    }
}
