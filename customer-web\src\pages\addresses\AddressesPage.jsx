import React, { useState, useEffect } from 'react'
import { 
  Plus, 
  MapPin, 
  Edit, 
  Trash2, 
  Home, 
  Building, 
  Star,
  MoreVertical
} from 'lucide-react'
import DashboardLayout from '../../components/layout/DashboardLayout'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import AddressModal from '../../components/addresses/AddressModal'
import { apiHelpers, endpoints } from '../../config/api'
import toast from 'react-hot-toast'

const AddressesPage = () => {
  const [addresses, setAddresses] = useState([])
  const [loading, setLoading] = useState(true)
  const [modalOpen, setModalOpen] = useState(false)
  const [editingAddress, setEditingAddress] = useState(null)
  const [deletingId, setDeletingId] = useState(null)

  useEffect(() => {
    fetchAddresses()
  }, [])

  const fetchAddresses = async () => {
    try {
      setLoading(true)
      const response = await apiHelpers.get(endpoints.customer.addresses)
      
      if (response.success) {
        setAddresses(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch addresses:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddAddress = () => {
    setEditingAddress(null)
    setModalOpen(true)
  }

  const handleEditAddress = (address) => {
    setEditingAddress(address)
    setModalOpen(true)
  }

  const handleDeleteAddress = async (addressId) => {
    if (!window.confirm('Are you sure you want to delete this address?')) {
      return
    }

    try {
      setDeletingId(addressId)
      await apiHelpers.delete(endpoints.customer.deleteAddress(addressId))
      
      setAddresses(addresses.filter(addr => addr.id !== addressId))
      toast.success('Address deleted successfully')
    } catch (error) {
      console.error('Failed to delete address:', error)
      toast.error('Failed to delete address')
    } finally {
      setDeletingId(null)
    }
  }

  const handleAddressSubmit = async (addressData) => {
    try {
      if (editingAddress) {
        // Update existing address
        const response = await apiHelpers.put(
          endpoints.customer.updateAddress(editingAddress.id),
          addressData
        )
        
        if (response.success) {
          setAddresses(addresses.map(addr => 
            addr.id === editingAddress.id ? response.data : addr
          ))
          toast.success('Address updated successfully')
        }
      } else {
        // Create new address
        const response = await apiHelpers.post(
          endpoints.customer.createAddress,
          addressData
        )
        
        if (response.success) {
          setAddresses([...addresses, response.data])
          toast.success('Address added successfully')
        }
      }
      
      setModalOpen(false)
      setEditingAddress(null)
    } catch (error) {
      console.error('Failed to save address:', error)
      toast.error('Failed to save address')
    }
  }

  const getAddressIcon = (label) => {
    switch (label?.toLowerCase()) {
      case 'home':
        return Home
      case 'office':
      case 'work':
        return Building
      default:
        return MapPin
    }
  }

  const formatAddress = (address) => {
    const parts = [
      address.address_line_1,
      address.address_line_2,
      address.city,
      address.state,
      address.postal_code
    ].filter(Boolean)
    
    return parts.join(', ')
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Addresses</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your saved addresses for quick booking
            </p>
          </div>
          <button
            onClick={handleAddAddress}
            className="mt-4 sm:mt-0 btn btn-primary btn-md"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Address
          </button>
        </div>

        {/* Addresses List */}
        <div className="card">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : addresses.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {addresses.map((address) => {
                const AddressIcon = getAddressIcon(address.label)
                return (
                  <div key={address.id} className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                          <AddressIcon className="w-6 h-6 text-primary-600" />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="text-lg font-medium text-gray-900">
                              {address.label || 'Address'}
                            </h3>
                            {address.is_default && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                <Star className="w-3 h-3 mr-1" />
                                Default
                              </span>
                            )}
                          </div>
                          
                          <p className="text-gray-600 mt-1">
                            {formatAddress(address)}
                          </p>
                          
                          {address.latitude && address.longitude && (
                            <div className="flex items-center mt-2 text-sm text-gray-500">
                              <MapPin className="w-4 h-4 mr-1" />
                              Location saved
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEditAddress(address)}
                          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                          title="Edit address"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        
                        <button
                          onClick={() => handleDeleteAddress(address.id)}
                          disabled={deletingId === address.id}
                          className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100 disabled:opacity-50"
                          title="Delete address"
                        >
                          {deletingId === address.id ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No addresses saved
              </h3>
              <p className="text-gray-500 mb-6">
                Add your first address to make booking services easier and faster.
              </p>
              <button
                onClick={handleAddAddress}
                className="btn btn-primary btn-md"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Address
              </button>
            </div>
          )}
        </div>

        {/* Tips Card */}
        <div className="card p-6 bg-blue-50 border-blue-200">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <MapPin className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-blue-900 mb-1">
                Pro Tips for Better Service
              </h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Add detailed landmarks to help Work-Boys find your location easily</li>
                <li>• Set a default address for quick booking</li>
                <li>• Include apartment/floor numbers for accurate delivery</li>
                <li>• Save multiple addresses like home, office, and relatives' places</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Address Modal */}
      <AddressModal
        isOpen={modalOpen}
        onClose={() => {
          setModalOpen(false)
          setEditingAddress(null)
        }}
        onSubmit={handleAddressSubmit}
        address={editingAddress}
      />
    </DashboardLayout>
  )
}

export default AddressesPage
