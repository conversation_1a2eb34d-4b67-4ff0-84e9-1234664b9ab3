<?php

namespace Tests\Integration;

use CodeIgniter\Test\CIUnitTestCase;
use CodeIgniter\Test\DatabaseTestTrait;
use CodeIgniter\Test\FeatureTestTrait;

class ApiIntegrationTest extends CIUnitTestCase
{
    use DatabaseTestTrait;
    use FeatureTestTrait;

    protected $migrate = true;
    protected $migrateOnce = false;
    protected $refresh = true;
    protected $namespace = null;

    protected $customerToken;
    protected $workboyToken;
    protected $adminToken;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpTestData();
    }

    protected function setUpTestData()
    {
        // Create test users
        $this->createTestCustomer();
        $this->createTestWorkBoy();
        $this->createTestAdmin();
    }

    protected function createTestCustomer()
    {
        $result = $this->post('/api/v1/auth/register', [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'first_name' => 'Test',
            'last_name' => 'Customer',
            'phone' => '+**********',
            'user_type' => 'customer'
        ]);

        $this->customerToken = $result->getJSON()->data->token;
    }

    protected function createTestWorkBoy()
    {
        $result = $this->post('/api/v1/auth/register', [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'first_name' => 'Test',
            'last_name' => 'WorkBoy',
            'phone' => '+1234567891',
            'user_type' => 'workboy',
            'skills' => ['cleaning', 'plumbing'],
            'experience_years' => 2
        ]);

        $this->workboyToken = $result->getJSON()->data->token;
    }

    protected function createTestAdmin()
    {
        $result = $this->post('/api/v1/admin/login', [
            'email' => '<EMAIL>',
            'password' => 'admin123'
        ]);

        $this->adminToken = $result->getJSON()->data->token;
    }

    public function testCustomerWorkflow()
    {
        // Test customer registration and login
        $this->assertNotEmpty($this->customerToken);

        // Test customer profile
        $result = $this->withHeaders(['Authorization' => 'Bearer ' . $this->customerToken])
                       ->get('/api/v1/auth/profile');
        
        $result->assertStatus(200);
        $this->assertEquals('customer', $result->getJSON()->data->user_type);

        // Test address creation
        $addressResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->customerToken])
                              ->post('/api/v1/customer/addresses', [
                                  'label' => 'Home',
                                  'address_line_1' => '123 Test Street',
                                  'city' => 'Test City',
                                  'state' => 'Test State',
                                  'postal_code' => '12345',
                                  'latitude' => 40.7128,
                                  'longitude' => -74.0060
                              ]);

        $addressResult->assertStatus(201);
        $addressId = $addressResult->getJSON()->data->id;

        // Test task creation
        $taskResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->customerToken])
                           ->post('/api/v1/tasks', [
                               'title' => 'Test Cleaning Task',
                               'description' => 'Clean my apartment',
                               'category_id' => 1,
                               'address_id' => $addressId,
                               'scheduled_date' => date('Y-m-d', strtotime('+1 day')),
                               'scheduled_time' => '10:00',
                               'budget_min' => 500,
                               'budget_max' => 1000,
                               'urgency' => 'normal'
                           ]);

        $taskResult->assertStatus(201);
        $taskId = $taskResult->getJSON()->data->id;

        // Test task listing
        $tasksResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->customerToken])
                            ->get('/api/v1/customer/tasks');

        $tasksResult->assertStatus(200);
        $this->assertGreaterThan(0, count($tasksResult->getJSON()->data));

        return $taskId;
    }

    public function testWorkBoyWorkflow()
    {
        // Test work-boy registration and login
        $this->assertNotEmpty($this->workboyToken);

        // Test work-boy profile
        $result = $this->withHeaders(['Authorization' => 'Bearer ' . $this->workboyToken])
                       ->get('/api/v1/auth/profile');
        
        $result->assertStatus(200);
        $this->assertEquals('workboy', $result->getJSON()->data->user_type);

        // Test KYC submission
        $kycResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->workboyToken])
                          ->post('/api/v1/workboy/kyc', [
                              'aadhar_number' => '************',
                              'pan_number' => '**********',
                              'bank_account_number' => '**********',
                              'bank_ifsc_code' => 'HDFC0001234',
                              'bank_account_holder_name' => 'Test WorkBoy',
                              'emergency_contact_name' => 'Emergency Contact',
                              'emergency_contact_phone' => '+**********',
                              'address_line_1' => '456 WorkBoy Street',
                              'city' => 'WorkBoy City',
                              'state' => 'WorkBoy State',
                              'postal_code' => '54321'
                          ]);

        $kycResult->assertStatus(201);

        // Test availability update
        $availabilityResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->workboyToken])
                                   ->put('/api/v1/workboy/availability', [
                                       'is_available' => true
                                   ]);

        $availabilityResult->assertStatus(200);

        // Test available tasks
        $tasksResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->workboyToken])
                            ->get('/api/v1/workboy/tasks/available');

        $tasksResult->assertStatus(200);
    }

    public function testTaskLifecycle()
    {
        // Create a task as customer
        $taskId = $this->testCustomerWorkflow();

        // Accept task as work-boy
        $acceptResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->workboyToken])
                             ->post("/api/v1/workboy/tasks/{$taskId}/accept");

        $acceptResult->assertStatus(200);

        // Start task
        $startResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->workboyToken])
                            ->put("/api/v1/workboy/tasks/{$taskId}/status", [
                                'status' => 'started'
                            ]);

        $startResult->assertStatus(200);

        // Complete task
        $completeResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->workboyToken])
                               ->post("/api/v1/workboy/tasks/{$taskId}/complete", [
                                   'completion_notes' => 'Task completed successfully',
                                   'completion_images' => []
                               ]);

        $completeResult->assertStatus(200);

        // Verify task status
        $taskResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->customerToken])
                           ->get("/api/v1/tasks/{$taskId}");

        $taskResult->assertStatus(200);
        $this->assertEquals('completed', $taskResult->getJSON()->data->status);
    }

    public function testAdminWorkflow()
    {
        // Test admin login
        $this->assertNotEmpty($this->adminToken);

        // Test dashboard data
        $dashboardResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->adminToken])
                                ->get('/api/v1/admin/dashboard');

        $dashboardResult->assertStatus(200);
        $this->assertObjectHasAttribute('stats', $dashboardResult->getJSON()->data);

        // Test user management
        $usersResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->adminToken])
                            ->get('/api/v1/admin/users');

        $usersResult->assertStatus(200);

        // Test work-boy management
        $workboysResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->adminToken])
                               ->get('/api/v1/admin/workboys');

        $workboysResult->assertStatus(200);

        // Test task management
        $tasksResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->adminToken])
                            ->get('/api/v1/admin/tasks');

        $tasksResult->assertStatus(200);
    }

    public function testPaymentWorkflow()
    {
        // Create and complete a task
        $taskId = $this->testCustomerWorkflow();
        
        // Accept and complete task as work-boy
        $this->withHeaders(['Authorization' => 'Bearer ' . $this->workboyToken])
             ->post("/api/v1/workboy/tasks/{$taskId}/accept");
        
        $this->withHeaders(['Authorization' => 'Bearer ' . $this->workboyToken])
             ->post("/api/v1/workboy/tasks/{$taskId}/complete", [
                 'completion_notes' => 'Task completed'
             ]);

        // Process payment as customer
        $paymentResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->customerToken])
                              ->post("/api/v1/payments/process", [
                                  'task_id' => $taskId,
                                  'amount' => 750,
                                  'payment_method' => 'razorpay',
                                  'razorpay_payment_id' => 'pay_test123',
                                  'razorpay_order_id' => 'order_test123',
                                  'razorpay_signature' => 'signature_test123'
                              ]);

        $paymentResult->assertStatus(200);

        // Verify payment
        $paymentId = $paymentResult->getJSON()->data->id;
        $paymentDetailResult = $this->withHeaders(['Authorization' => 'Bearer ' . $this->customerToken])
                                    ->get("/api/v1/payments/{$paymentId}");

        $paymentDetailResult->assertStatus(200);
        $this->assertEquals('completed', $paymentDetailResult->getJSON()->data->status);
    }

    public function testErrorHandling()
    {
        // Test unauthorized access
        $result = $this->get('/api/v1/customer/tasks');
        $result->assertStatus(401);

        // Test invalid token
        $result = $this->withHeaders(['Authorization' => 'Bearer invalid_token'])
                       ->get('/api/v1/customer/tasks');
        $result->assertStatus(401);

        // Test non-existent resource
        $result = $this->withHeaders(['Authorization' => 'Bearer ' . $this->customerToken])
                       ->get('/api/v1/tasks/99999');
        $result->assertStatus(404);

        // Test validation errors
        $result = $this->withHeaders(['Authorization' => 'Bearer ' . $this->customerToken])
                       ->post('/api/v1/tasks', [
                           'title' => '', // Empty title should fail validation
                       ]);
        $result->assertStatus(422);
    }

    public function testRateLimiting()
    {
        // Test rate limiting by making multiple requests
        for ($i = 0; $i < 100; $i++) {
            $result = $this->post('/api/v1/auth/login', [
                'email' => '<EMAIL>',
                'password' => 'wrongpassword'
            ]);
        }

        // Should eventually get rate limited
        $result->assertStatus(429);
    }

    public function testCrossOriginRequests()
    {
        // Test CORS headers
        $result = $this->call('OPTIONS', '/api/v1/auth/login', [], [], [], [
            'HTTP_ORIGIN' => 'http://localhost:3000',
            'HTTP_ACCESS_CONTROL_REQUEST_METHOD' => 'POST',
            'HTTP_ACCESS_CONTROL_REQUEST_HEADERS' => 'Content-Type, Authorization'
        ]);

        $result->assertStatus(200);
        $result->assertHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
        $result->assertHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    }
}
