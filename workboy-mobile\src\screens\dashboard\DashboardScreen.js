import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  StatusBar,
  Switch,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  IconButton,
  Avatar,
  Chip,
  FAB,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '@context/AuthContext';
import { useTask } from '@context/TaskContext';
import { apiService } from '@services/apiService';
import { colors, spacing, typography, borderRadius } from '@constants/theme';
import { showToast } from '@utils/toast';
import { format } from 'date-fns';

const { width: screenWidth } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const { userProfile, updateAvailability, isAvailable } = useAuth();
  const { currentTask, availableTasks, refreshTasks } = useTask();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await apiService.get('/workboy/dashboard');
      if (response.success) {
        setDashboardData(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      showToast('Failed to load dashboard data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await Promise.all([
      fetchDashboardData(),
      refreshTasks(),
    ]);
    setRefreshing(false);
  }, []);

  const handleAvailabilityToggle = async (value) => {
    try {
      await updateAvailability(value);
      showToast(
        value ? 'You are now available for tasks' : 'You are now offline',
        value ? 'success' : 'info'
      );
    } catch (error) {
      showToast('Failed to update availability', 'error');
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return colors.warning[500];
      case 'assigned':
      case 'in_progress':
        return colors.primary[500];
      case 'completed':
        return colors.success[500];
      case 'cancelled':
        return colors.error[500];
      default:
        return colors.gray[500];
    }
  };

  const stats = dashboardData?.stats || {};
  const earnings = dashboardData?.earnings || {};
  const recentTasks = dashboardData?.recent_tasks || [];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary[600]} />
      
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <LinearGradient
          colors={[colors.primary[600], colors.primary[700]]}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerLeft}>
              <Avatar.Text
                size={48}
                label={userProfile?.first_name?.charAt(0) || 'W'}
                style={styles.avatar}
                labelStyle={styles.avatarLabel}
              />
              <View style={styles.headerText}>
                <Text style={styles.greeting}>{getGreeting()}</Text>
                <Text style={styles.userName}>
                  {userProfile?.first_name || 'Work-Boy'}
                </Text>
              </View>
            </View>
            
            <View style={styles.headerRight}>
              <IconButton
                icon="notifications-outline"
                size={24}
                iconColor={colors.white}
                onPress={() => navigation.navigate('Notifications')}
                style={styles.notificationButton}
              />
            </View>
          </View>

          {/* Availability Toggle */}
          <Card style={styles.availabilityCard}>
            <Card.Content style={styles.availabilityContent}>
              <View style={styles.availabilityInfo}>
                <View style={[
                  styles.availabilityIndicator,
                  { backgroundColor: isAvailable ? colors.success[500] : colors.gray[400] }
                ]} />
                <View style={styles.availabilityText}>
                  <Text style={styles.availabilityTitle}>
                    {isAvailable ? 'Available for Tasks' : 'Currently Offline'}
                  </Text>
                  <Text style={styles.availabilitySubtitle}>
                    {isAvailable 
                      ? 'You will receive new task notifications' 
                      : 'Turn on to start receiving tasks'
                    }
                  </Text>
                </View>
              </View>
              
              <Switch
                value={isAvailable}
                onValueChange={handleAvailabilityToggle}
                trackColor={{ false: colors.gray[300], true: colors.success[200] }}
                thumbColor={isAvailable ? colors.success[500] : colors.gray[500]}
              />
            </Card.Content>
          </Card>
        </LinearGradient>

        {/* Current Task */}
        {currentTask && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Current Task</Text>
            <Card style={styles.currentTaskCard}>
              <Card.Content style={styles.currentTaskContent}>
                <View style={styles.currentTaskHeader}>
                  <Text style={styles.currentTaskTitle} numberOfLines={2}>
                    {currentTask.title}
                  </Text>
                  <Chip
                    mode="flat"
                    style={[
                      styles.statusChip,
                      { backgroundColor: `${getStatusColor(currentTask.status)}20` }
                    ]}
                    textStyle={[
                      styles.statusChipText,
                      { color: getStatusColor(currentTask.status) }
                    ]}
                  >
                    {currentTask.status.replace('_', ' ')}
                  </Chip>
                </View>
                
                <Text style={styles.currentTaskCustomer}>
                  Customer: {currentTask.customer_name}
                </Text>
                
                <View style={styles.currentTaskMeta}>
                  <View style={styles.currentTaskMetaItem}>
                    <Ionicons name="location-outline" size={16} color={colors.gray[500]} />
                    <Text style={styles.currentTaskMetaText}>{currentTask.city}</Text>
                  </View>
                  <View style={styles.currentTaskMetaItem}>
                    <Ionicons name="cash-outline" size={16} color={colors.gray[500]} />
                    <Text style={styles.currentTaskMetaText}>₹{currentTask.amount}</Text>
                  </View>
                </View>

                <View style={styles.currentTaskActions}>
                  <Button
                    mode="outlined"
                    onPress={() => navigation.navigate('TaskDetail', { taskId: currentTask.id })}
                    style={styles.currentTaskButton}
                    labelStyle={styles.currentTaskButtonLabel}
                  >
                    View Details
                  </Button>
                  
                  {currentTask.status === 'assigned' && (
                    <Button
                      mode="contained"
                      onPress={() => navigation.navigate('TaskNavigation', { taskId: currentTask.id })}
                      style={[styles.currentTaskButton, { marginLeft: spacing.md }]}
                      labelStyle={styles.currentTaskButtonLabel}
                    >
                      Start Task
                    </Button>
                  )}
                </View>
              </Card.Content>
            </Card>
          </View>
        )}

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            <Card style={[styles.statCard, { backgroundColor: colors.primary[50] }]}>
              <Card.Content style={styles.statCardContent}>
                <View style={[styles.statIcon, { backgroundColor: colors.primary[100] }]}>
                  <Ionicons name="checkmark-circle-outline" size={24} color={colors.primary[600]} />
                </View>
                <Text style={styles.statNumber}>{stats.completed_tasks || 0}</Text>
                <Text style={styles.statLabel}>Completed</Text>
              </Card.Content>
            </Card>

            <Card style={[styles.statCard, { backgroundColor: colors.success[50] }]}>
              <Card.Content style={styles.statCardContent}>
                <View style={[styles.statIcon, { backgroundColor: colors.success[100] }]}>
                  <Ionicons name="cash-outline" size={24} color={colors.success[600]} />
                </View>
                <Text style={styles.statNumber}>₹{earnings.today || 0}</Text>
                <Text style={styles.statLabel}>Today's Earnings</Text>
              </Card.Content>
            </Card>
          </View>

          <View style={styles.statsRow}>
            <Card style={[styles.statCard, { backgroundColor: colors.warning[50] }]}>
              <Card.Content style={styles.statCardContent}>
                <View style={[styles.statIcon, { backgroundColor: colors.warning[100] }]}>
                  <Ionicons name="star-outline" size={24} color={colors.warning[600]} />
                </View>
                <Text style={styles.statNumber}>{stats.average_rating || '0.0'}</Text>
                <Text style={styles.statLabel}>Rating</Text>
              </Card.Content>
            </Card>

            <Card style={[styles.statCard, { backgroundColor: colors.blue[50] }]}>
              <Card.Content style={styles.statCardContent}>
                <View style={[styles.statIcon, { backgroundColor: colors.blue[100] }]}>
                  <Ionicons name="trending-up-outline" size={24} color={colors.blue[600]} />
                </View>
                <Text style={styles.statNumber}>₹{earnings.this_month || 0}</Text>
                <Text style={styles.statLabel}>This Month</Text>
              </Card.Content>
            </Card>
          </View>
        </View>

        {/* Available Tasks */}
        {availableTasks.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Available Tasks</Text>
              <Button
                mode="text"
                onPress={() => navigation.navigate('Tasks')}
                labelStyle={styles.sectionLink}
              >
                View All
              </Button>
            </View>

            {availableTasks.slice(0, 3).map((task) => (
              <Card key={task.id} style={styles.taskCard}>
                <Card.Content style={styles.taskCardContent}>
                  <View style={styles.taskHeader}>
                    <View style={styles.taskInfo}>
                      <Text style={styles.taskTitle} numberOfLines={1}>
                        {task.title}
                      </Text>
                      <Text style={styles.taskLocation}>
                        {task.city} • ₹{task.amount}
                      </Text>
                    </View>
                    <Text style={styles.taskDistance}>
                      {task.distance ? `${task.distance.toFixed(1)} km` : 'Near you'}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            ))}
          </View>
        )}

        {/* Recent Tasks */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Tasks</Text>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Tasks')}
              labelStyle={styles.sectionLink}
            >
              View All
            </Button>
          </View>

          {recentTasks.length > 0 ? (
            recentTasks.map((task) => (
              <Card key={task.id} style={styles.taskCard}>
                <Card.Content style={styles.taskCardContent}>
                  <View style={styles.taskHeader}>
                    <View style={styles.taskInfo}>
                      <Text style={styles.taskTitle} numberOfLines={1}>
                        {task.title}
                      </Text>
                      <Text style={styles.taskDate}>
                        {format(new Date(task.completed_at || task.created_at), 'MMM dd, yyyy')}
                      </Text>
                    </View>
                    <View style={styles.taskAmount}>
                      <Text style={styles.taskAmountText}>₹{task.amount}</Text>
                      <Chip
                        mode="outlined"
                        style={[
                          styles.statusChip,
                          { borderColor: getStatusColor(task.status) }
                        ]}
                        textStyle={[
                          styles.statusChipText,
                          { color: getStatusColor(task.status) }
                        ]}
                      >
                        {task.status}
                      </Chip>
                    </View>
                  </View>
                </Card.Content>
              </Card>
            ))
          ) : (
            <Card style={styles.emptyCard}>
              <Card.Content style={styles.emptyCardContent}>
                <Ionicons name="clipboard-outline" size={48} color={colors.gray[400]} />
                <Text style={styles.emptyText}>No recent tasks</Text>
                <Text style={styles.emptySubtext}>
                  Complete your first task to see it here
                </Text>
              </Card.Content>
            </Card>
          )}
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      {availableTasks.length > 0 && (
        <FAB
          icon="list"
          style={styles.fab}
          onPress={() => navigation.navigate('Tasks')}
          label={`${availableTasks.length} Available`}
          extended
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
    borderBottomLeftRadius: borderRadius.xl,
    borderBottomRightRadius: borderRadius.xl,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: colors.white,
    marginRight: spacing.md,
  },
  avatarLabel: {
    color: colors.primary[600],
    fontFamily: typography.fontFamily.bold,
  },
  headerText: {
    justifyContent: 'center',
  },
  greeting: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.primary[100],
  },
  userName: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.white,
  },
  headerRight: {
    flexDirection: 'row',
  },
  notificationButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  availabilityCard: {
    borderRadius: borderRadius.lg,
  },
  availabilityContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
  },
  availabilityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  availabilityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: spacing.md,
  },
  availabilityText: {
    flex: 1,
  },
  availabilityTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
  },
  availabilitySubtitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
  },
  section: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
  },
  sectionLink: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.primary[600],
  },
  currentTaskCard: {
    borderRadius: borderRadius.lg,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary[500],
  },
  currentTaskContent: {
    paddingVertical: spacing.lg,
  },
  currentTaskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  currentTaskTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    flex: 1,
    marginRight: spacing.md,
  },
  currentTaskCustomer: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
    marginBottom: spacing.md,
  },
  currentTaskMeta: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
  },
  currentTaskMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.lg,
  },
  currentTaskMetaText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    marginLeft: spacing.xs,
  },
  currentTaskActions: {
    flexDirection: 'row',
  },
  currentTaskButton: {
    flex: 1,
    borderRadius: borderRadius.lg,
  },
  currentTaskButtonLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
  },
  statsContainer: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  statCard: {
    width: (screenWidth - spacing.lg * 2 - spacing.md) / 2,
    borderRadius: borderRadius.lg,
  },
  statCardContent: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statNumber: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[600],
  },
  taskCard: {
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
    elevation: 2,
  },
  taskCardContent: {
    paddingVertical: spacing.md,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  taskTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  taskLocation: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
  },
  taskDate: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
  },
  taskDistance: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.primary[600],
  },
  taskAmount: {
    alignItems: 'flex-end',
  },
  taskAmountText: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.primary[600],
    marginBottom: spacing.xs,
  },
  statusChip: {
    backgroundColor: colors.transparent,
    height: 28,
  },
  statusChipText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.medium,
    textTransform: 'capitalize',
  },
  emptyCard: {
    borderRadius: borderRadius.lg,
  },
  emptyCardContent: {
    alignItems: 'center',
    paddingVertical: spacing['2xl'],
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[600],
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptySubtext: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[500],
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: spacing.lg,
    right: 0,
    bottom: 0,
    backgroundColor: colors.primary[600],
  },
});

export default DashboardScreen;
