import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';

// Base API configuration
const API_BASE_URL = __DEV__
  ? 'http://localhost:8000/api' // Development URL
  : 'https://your-production-api.com/api'; // Production URL

// API endpoints configuration
export const endpoints = {
  // Auth endpoints
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    verifyEmail: '/auth/verify-email',
  },

  // User endpoints
  user: {
    profile: '/user/profile',
    avatar: '/user/avatar',
    location: '/user/location',
    deviceToken: '/user/device-token',
  },

  // Task endpoints
  tasks: {
    list: '/tasks',
    detail: (id) => `/tasks/${id}`,
    accept: (id) => `/tasks/${id}/accept`,
    start: (id) => `/tasks/${id}/start`,
    complete: (id) => `/tasks/${id}/complete`,
    cancel: (id) => `/tasks/${id}/cancel`,
    updateStatus: (id) => `/tasks/${id}/status`,
    uploadImage: (id) => `/tasks/${id}/images`,
  },

  // Notification endpoints
  notifications: {
    list: '/notifications',
    markRead: (id) => `/notifications/${id}/read`,
    markAllRead: '/notifications/read-all',
  },

  // Earnings endpoints
  earnings: {
    list: '/earnings',
    stats: '/earnings/stats',
    payout: '/earnings/payout',
  },

  // Support endpoints
  support: {
    tickets: '/support/tickets',
    ticket: (id) => `/support/tickets/${id}`,
    reply: (id) => `/support/tickets/${id}/reply`,
  },
};

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await SecureStore.getItemAsync('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      await SecureStore.deleteItemAsync('authToken');
      await AsyncStorage.removeItem('user');
      // You might want to redirect to login screen here
    }
    return Promise.reject(error);
  }
);

export const apiService = {
  // Auth endpoints
  auth: {
    login: async (credentials) => {
      const response = await api.post('/auth/login', credentials);
      return response.data;
    },

    register: async (userData) => {
      const response = await api.post('/auth/register', userData);
      return response.data;
    },

    logout: async () => {
      const response = await api.post('/auth/logout');
      return response.data;
    },

    refreshToken: async () => {
      const response = await api.post('/auth/refresh');
      return response.data;
    },

    forgotPassword: async (email) => {
      const response = await api.post('/auth/forgot-password', { email });
      return response.data;
    },

    resetPassword: async (token, password) => {
      const response = await api.post('/auth/reset-password', { token, password });
      return response.data;
    },

    verifyEmail: async (token) => {
      const response = await api.post('/auth/verify-email', { token });
      return response.data;
    },
  },

  // User profile endpoints
  user: {
    getProfile: async () => {
      const response = await api.get('/user/profile');
      return response.data;
    },

    updateProfile: async (profileData) => {
      const response = await api.put('/user/profile', profileData);
      return response.data;
    },

    uploadAvatar: async (imageData) => {
      const formData = new FormData();
      formData.append('avatar', {
        uri: imageData.uri,
        type: imageData.type,
        name: imageData.fileName || 'avatar.jpg',
      });

      const response = await api.post('/user/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    },

    updateLocation: async (location) => {
      const response = await api.put('/user/location', location);
      return response.data;
    },

    updateDeviceToken: async (token) => {
      const response = await api.put('/user/device-token', { token });
      return response.data;
    },
  },

  // Tasks endpoints
  tasks: {
    getTasks: async (params = {}) => {
      const response = await api.get('/tasks', { params });
      return response.data;
    },

    getTask: async (taskId) => {
      const response = await api.get(`/tasks/${taskId}`);
      return response.data;
    },

    acceptTask: async (taskId) => {
      const response = await api.post(`/tasks/${taskId}/accept`);
      return response.data;
    },

    startTask: async (taskId) => {
      const response = await api.post(`/tasks/${taskId}/start`);
      return response.data;
    },

    completeTask: async (taskId, completionData) => {
      const response = await api.post(`/tasks/${taskId}/complete`, completionData);
      return response.data;
    },

    cancelTask: async (taskId, reason) => {
      const response = await api.post(`/tasks/${taskId}/cancel`, { reason });
      return response.data;
    },

    updateTaskStatus: async (taskId, status) => {
      const response = await api.put(`/tasks/${taskId}/status`, { status });
      return response.data;
    },

    uploadTaskImage: async (taskId, imageData) => {
      const formData = new FormData();
      formData.append('image', {
        uri: imageData.uri,
        type: imageData.type,
        name: imageData.fileName || 'task-image.jpg',
      });

      const response = await api.post(`/tasks/${taskId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    },
  },

  // Notifications endpoints
  notifications: {
    getNotifications: async (params = {}) => {
      const response = await api.get('/notifications', { params });
      return response.data;
    },

    markAsRead: async (notificationId) => {
      const response = await api.put(`/notifications/${notificationId}/read`);
      return response.data;
    },

    markAllAsRead: async () => {
      const response = await api.put('/notifications/read-all');
      return response.data;
    },
  },

  // Earnings endpoints
  earnings: {
    getEarnings: async (params = {}) => {
      const response = await api.get('/earnings', { params });
      return response.data;
    },

    getEarningsStats: async () => {
      const response = await api.get('/earnings/stats');
      return response.data;
    },

    requestPayout: async (amount, method) => {
      const response = await api.post('/earnings/payout', { amount, method });
      return response.data;
    },
  },

  // Support endpoints
  support: {
    getTickets: async () => {
      const response = await api.get('/support/tickets');
      return response.data;
    },

    createTicket: async (ticketData) => {
      const response = await api.post('/support/tickets', ticketData);
      return response.data;
    },

    getTicket: async (ticketId) => {
      const response = await api.get(`/support/tickets/${ticketId}`);
      return response.data;
    },

    replyToTicket: async (ticketId, message) => {
      const response = await api.post(`/support/tickets/${ticketId}/reply`, { message });
      return response.data;
    },
  },

  // Generic API methods
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
};
