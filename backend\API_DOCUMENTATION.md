# Work-Boy Booking Platform API Documentation

## Overview
This is the complete REST API documentation for the Work-Boy Booking Platform built with CodeIgniter 4, featuring Firebase Authentication, Razorpay payments, and comprehensive user management.

## Base URL
```
http://localhost:8080/api/v1
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <JWT_TOKEN>
```

## Response Format
All API responses follow this standard format:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Response data (optional)
  "meta": {}, // Pagination/additional metadata (optional)
  "errors": {} // Validation errors (optional)
}
```

## Authentication Endpoints

### Register User
```http
POST /auth/register
```
**Body:**
```json
{
  "firebase_uid": "string",
  "email": "string",
  "phone": "string",
  "first_name": "string",
  "last_name": "string",
  "user_type": "customer|workboy"
}
```

### Login User
```http
POST /auth/login
```
**Body:**
```json
{
  "firebase_uid": "string"
}
```

### Get Profile
```http
GET /auth/profile
```
**Headers:** `Authorization: Bearer <token>`

### Update Profile
```http
PUT /auth/profile
```
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "first_name": "string",
  "last_name": "string",
  "phone": "string",
  "profile_image": "string"
}
```

## Task Management

### Get Task Categories
```http
GET /tasks/categories
```

### Create Task (Customer only)
```http
POST /tasks
```
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "category_id": "integer",
  "title": "string",
  "description": "string",
  "task_images": ["string"],
  "address_id": "integer",
  "scheduled_date": "YYYY-MM-DD",
  "scheduled_time": "HH:MM:SS",
  "estimated_duration": "integer",
  "priority": "low|medium|high"
}
```

### Get Tasks
```http
GET /tasks?status=pending&category_id=1&page=1&limit=20
```
**Headers:** `Authorization: Bearer <token>`

### Get Task Details
```http
GET /tasks/{id}
```
**Headers:** `Authorization: Bearer <token>`

### Update Task Status
```http
POST /tasks/{id}/status
```
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "status": "assigned|in_progress|completed|cancelled",
  "notes": "string",
  "location_lat": "decimal",
  "location_lng": "decimal"
}
```

## Customer APIs

### Get Customer Dashboard
```http
GET /customer/dashboard
```
**Headers:** `Authorization: Bearer <token>`

### Get Addresses
```http
GET /customer/addresses
```
**Headers:** `Authorization: Bearer <token>`

### Create Address
```http
POST /customer/addresses
```
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "label": "string",
  "address_line_1": "string",
  "address_line_2": "string",
  "city": "string",
  "state": "string",
  "postal_code": "string",
  "country": "string",
  "latitude": "decimal",
  "longitude": "decimal",
  "is_default": "boolean"
}
```

## Work-Boy APIs

### Get Work-Boy Dashboard
```http
GET /workboy/dashboard
```
**Headers:** `Authorization: Bearer <token>`

### Submit KYC Documents
```http
POST /workboy/kyc
```
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "documents": {
    "id_proof": "string",
    "address_proof": "string",
    "photo": "string"
  }
}
```

### Get Available Tasks
```http
GET /workboy/available-tasks?category_id=1&location_lat=12.9716&location_lng=77.5946&radius=10
```
**Headers:** `Authorization: Bearer <token>`

### Accept Task
```http
POST /workboy/tasks/{id}/accept
```
**Headers:** `Authorization: Bearer <token>`

### Get Earnings
```http
GET /workboy/earnings?period=month
```
**Headers:** `Authorization: Bearer <token>`

## Payment APIs

### Create Payment
```http
POST /payments/create
```
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "task_id": "integer",
  "amount": "decimal",
  "tip_amount": "decimal"
}
```

### Confirm Payment
```http
POST /payments/confirm
```
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "payment_id": "string",
  "payment_gateway_id": "string",
  "signature": "string"
}
```

### Get Payment History
```http
GET /payments/history?status=completed&page=1&limit=20
```
**Headers:** `Authorization: Bearer <token>`

## Review APIs

### Create Review (Customer only)
```http
POST /reviews
```
**Headers:** `Authorization: Bearer <token>`
**Body:**
```json
{
  "task_id": "integer",
  "workboy_id": "integer",
  "rating": "integer (1-5)",
  "review_text": "string"
}
```

### Get Work-Boy Reviews
```http
GET /reviews/workboy/{id}?rating=5&page=1&limit=20
```

### Get Top Rated Work-Boys
```http
GET /reviews/top-workboys?limit=10&min_reviews=5
```

## Notification APIs

### Get Notifications
```http
GET /notifications?type=task_update&is_read=0&page=1&limit=20
```
**Headers:** `Authorization: Bearer <token>`

### Mark as Read
```http
PUT /notifications/{id}/read
```
**Headers:** `Authorization: Bearer <token>`

### Mark All as Read
```http
PUT /notifications/read-all
```
**Headers:** `Authorization: Bearer <token>`

## Admin APIs

### Get Admin Dashboard
```http
GET /admin/dashboard
```
**Headers:** `Authorization: Bearer <token>` (Admin only)

### Get Users
```http
GET /admin/users?user_type=customer&status=active&search=john&page=1&limit=20
```
**Headers:** `Authorization: Bearer <token>` (Admin only)

### Update User Status
```http
PUT /admin/users/{id}/status
```
**Headers:** `Authorization: Bearer <token>` (Admin only)
**Body:**
```json
{
  "status": "active|inactive|suspended|pending_verification",
  "reason": "string"
}
```

### Get Analytics
```http
GET /admin/analytics?period=30
```
**Headers:** `Authorization: Bearer <token>` (Admin only)

### Approve KYC
```http
POST /admin/workboy/{id}/approve-kyc
```
**Headers:** `Authorization: Bearer <token>` (Admin only)

### Reject KYC
```http
POST /admin/workboy/{id}/reject-kyc
```
**Headers:** `Authorization: Bearer <token>` (Admin only)
**Body:**
```json
{
  "reason": "string"
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 200  | Success |
| 201  | Created |
| 400  | Bad Request |
| 401  | Unauthorized |
| 403  | Forbidden |
| 404  | Not Found |
| 409  | Conflict |
| 422  | Validation Error |
| 500  | Internal Server Error |

## Rate Limiting
- 100 requests per minute per IP
- 1000 requests per hour per authenticated user

## Pagination
All list endpoints support pagination:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)

Response includes pagination metadata:
```json
{
  "meta": {
    "current_page": 1,
    "per_page": 20,
    "total": 150,
    "total_pages": 8,
    "has_next": true,
    "has_prev": false
  }
}
```

## WebSocket Events (Future Implementation)
- `task.assigned`: Task assigned to Work-Boy
- `task.status_updated`: Task status changed
- `payment.completed`: Payment processed
- `message.received`: New chat message

## Environment Variables
```env
# Database
database.default.hostname = localhost
database.default.database = workboy_booking
database.default.username = root
database.default.password = 

# JWT
JWT_SECRET = your-secret-key
JWT_EXPIRE_TIME = 86400

# Firebase
FIREBASE_PROJECT_ID = your-project-id
FIREBASE_PRIVATE_KEY = your-private-key
FCM_SERVER_KEY = your-fcm-server-key

# Razorpay
RAZORPAY_KEY_ID = your-key-id
RAZORPAY_KEY_SECRET = your-key-secret
RAZORPAY_WEBHOOK_SECRET = your-webhook-secret
```
