{"name": "workboy-admin-panel", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-chartjs-2": "^5.2.0", "chart.js": "^4.4.0", "lucide-react": "^0.294.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-hot-toast": "^2.4.1", "react-table": "^7.8.0", "@tanstack/react-table": "^8.10.7", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "react-dropzone": "^14.2.3", "react-modal": "^3.16.1", "react-loading-skeleton": "^3.3.1", "react-infinite-scroll-component": "^6.1.0", "react-helmet-async": "^1.3.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "uuid": "^9.0.1", "file-saver": "^2.0.5", "xlsx": "^0.18.5", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "react-beautiful-dnd": "^13.1.1", "framer-motion": "^10.16.5", "react-transition-group": "^4.4.5"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^4.5.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/file-saver": "^2.0.7", "@types/react-table": "^7.7.18", "@types/react-datepicker": "^4.19.4", "@types/react-modal": "^3.16.3", "@types/react-beautiful-dnd": "^13.1.8"}}