import React, { forwardRef } from 'react'
import { clsx } from 'clsx'

export const PremiumInput = forwardRef(({
  label,
  error,
  helper,
  icon: Icon,
  iconPosition = 'left',
  size = 'md',
  variant = 'default',
  className = '',
  containerClassName = '',
  ...props
}, ref) => {
  const baseClasses = 'block w-full transition-all duration-300 focus:outline-none'
  
  const variants = {
    default: 'border border-gray-300 bg-white focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20',
    filled: 'border-0 bg-gray-100 focus:bg-white focus:ring-2 focus:ring-primary-500/20',
    outlined: 'border-2 border-gray-300 bg-transparent focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20',
    premium: 'border border-gray-200 bg-gray-50/50 focus:bg-white focus:border-primary-300 focus:ring-2 focus:ring-primary-500/20 hover:bg-white hover:border-gray-300'
  }
  
  const sizes = {
    sm: 'px-3 py-2 text-sm rounded-lg',
    md: 'px-4 py-3 text-sm rounded-xl',
    lg: 'px-5 py-4 text-base rounded-xl'
  }
  
  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }
  
  const paddingWithIcon = {
    sm: iconPosition === 'left' ? 'pl-10' : 'pr-10',
    md: iconPosition === 'left' ? 'pl-12' : 'pr-12',
    lg: iconPosition === 'left' ? 'pl-14' : 'pr-14'
  }

  return (
    <div className={clsx('space-y-2', containerClassName)}>
      {label && (
        <label className="block text-sm font-bold text-gray-700 mb-2">
          {label}
        </label>
      )}
      
      <div className="relative group">
        {Icon && (
          <div className={clsx(
            'absolute inset-y-0 flex items-center pointer-events-none transition-colors duration-300',
            iconPosition === 'left' ? 'left-0 pl-4' : 'right-0 pr-4',
            error ? 'text-danger-500' : 'text-gray-400 group-focus-within:text-primary-500'
          )}>
            <Icon className={iconSizes[size]} />
          </div>
        )}
        
        <input
          ref={ref}
          className={clsx(
            baseClasses,
            variants[variant],
            sizes[size],
            Icon && paddingWithIcon[size],
            error && 'border-danger-300 focus:border-danger-500 focus:ring-danger-500/20',
            'placeholder:text-gray-400 placeholder:font-medium',
            className
          )}
          {...props}
        />
        
        {/* Focus ring enhancement */}
        <div className="absolute inset-0 rounded-xl pointer-events-none transition-all duration-300 group-focus-within:ring-2 group-focus-within:ring-primary-500/20 group-focus-within:ring-offset-1"></div>
      </div>
      
      {error && (
        <p className="text-sm text-danger-600 font-medium flex items-center space-x-1">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span>{error}</span>
        </p>
      )}
      
      {helper && !error && (
        <p className="text-sm text-gray-500 font-medium">{helper}</p>
      )}
    </div>
  )
})

PremiumInput.displayName = 'PremiumInput'

export const PremiumTextarea = forwardRef(({
  label,
  error,
  helper,
  rows = 4,
  resize = true,
  className = '',
  containerClassName = '',
  ...props
}, ref) => {
  return (
    <div className={clsx('space-y-2', containerClassName)}>
      {label && (
        <label className="block text-sm font-bold text-gray-700 mb-2">
          {label}
        </label>
      )}
      
      <div className="relative group">
        <textarea
          ref={ref}
          rows={rows}
          className={clsx(
            'block w-full px-4 py-3 text-sm border border-gray-200 bg-gray-50/50 rounded-xl transition-all duration-300',
            'focus:outline-none focus:bg-white focus:border-primary-300 focus:ring-2 focus:ring-primary-500/20',
            'hover:bg-white hover:border-gray-300',
            'placeholder:text-gray-400 placeholder:font-medium',
            !resize && 'resize-none',
            error && 'border-danger-300 focus:border-danger-500 focus:ring-danger-500/20',
            className
          )}
          {...props}
        />
        
        {/* Focus ring enhancement */}
        <div className="absolute inset-0 rounded-xl pointer-events-none transition-all duration-300 group-focus-within:ring-2 group-focus-within:ring-primary-500/20 group-focus-within:ring-offset-1"></div>
      </div>
      
      {error && (
        <p className="text-sm text-danger-600 font-medium flex items-center space-x-1">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span>{error}</span>
        </p>
      )}
      
      {helper && !error && (
        <p className="text-sm text-gray-500 font-medium">{helper}</p>
      )}
    </div>
  )
})

PremiumTextarea.displayName = 'PremiumTextarea'

export default PremiumInput
