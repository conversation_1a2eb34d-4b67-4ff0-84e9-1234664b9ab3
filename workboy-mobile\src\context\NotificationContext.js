import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform, Alert } from 'react-native';

const NotificationContext = createContext();

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export const NotificationProvider = ({ children }) => {
  const [expoPushToken, setExpoPushToken] = useState('');
  const [notification, setNotification] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const notificationListener = useRef();
  const responseListener = useRef();

  // Register for push notifications
  const registerForPushNotificationsAsync = async () => {
    let token;

    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      setPermissionStatus(finalStatus);
      
      if (finalStatus !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Push notifications are required to receive task updates and important alerts.',
          [{ text: 'OK' }]
        );
        return;
      }
      
      try {
        token = (await Notifications.getExpoPushTokenAsync()).data;
        setExpoPushToken(token);
        console.log('Expo Push Token:', token);
      } catch (error) {
        console.error('Error getting push token:', error);
        setError('Failed to get push token');
      }
    } else {
      Alert.alert('Must use physical device for Push Notifications');
    }

    return token;
  };

  // Send local notification
  const sendLocalNotification = async (title, body, data = {}) => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Error sending local notification:', error);
      setError('Failed to send notification');
    }
  };

  // Schedule notification
  const scheduleNotification = async (title, body, trigger, data = {}) => {
    try {
      const id = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger,
      });
      return id;
    } catch (error) {
      console.error('Error scheduling notification:', error);
      setError('Failed to schedule notification');
      return null;
    }
  };

  // Cancel notification
  const cancelNotification = async (notificationId) => {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
      setError('Failed to cancel notification');
    }
  };

  // Cancel all notifications
  const cancelAllNotifications = async () => {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
      setError('Failed to cancel notifications');
    }
  };

  // Get badge count
  const getBadgeCount = async () => {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('Error getting badge count:', error);
      return 0;
    }
  };

  // Set badge count
  const setBadgeCount = async (count) => {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Error setting badge count:', error);
      setError('Failed to set badge count');
    }
  };

  // Clear badge
  const clearBadge = async () => {
    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.error('Error clearing badge:', error);
      setError('Failed to clear badge');
    }
  };

  // Handle notification received
  const handleNotificationReceived = (notification) => {
    setNotification(notification);
    console.log('Notification received:', notification);
  };

  // Handle notification response
  const handleNotificationResponse = (response) => {
    console.log('Notification response:', response);
    // Handle navigation or actions based on notification data
    const data = response.notification.request.content.data;
    
    if (data.screen) {
      // Navigate to specific screen
      // This would typically use navigation ref
      console.log('Navigate to:', data.screen);
    }
  };

  // Initialize notifications
  useEffect(() => {
    const initializeNotifications = async () => {
      setLoading(true);
      try {
        await registerForPushNotificationsAsync();
      } catch (error) {
        console.error('Error initializing notifications:', error);
        setError('Failed to initialize notifications');
      } finally {
        setLoading(false);
      }
    };

    initializeNotifications();

    // Set up notification listeners
    notificationListener.current = Notifications.addNotificationReceivedListener(
      handleNotificationReceived
    );

    responseListener.current = Notifications.addNotificationResponseReceivedListener(
      handleNotificationResponse
    );

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  const value = {
    expoPushToken,
    notification,
    permissionStatus,
    loading,
    error,
    registerForPushNotificationsAsync,
    sendLocalNotification,
    scheduleNotification,
    cancelNotification,
    cancelAllNotifications,
    getBadgeCount,
    setBadgeCount,
    clearBadge,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
