import React, { useState, useEffect } from 'react'
import { X, Bell, Mail, MessageSquare, Smartphone } from 'lucide-react'
import LoadingSpinner from '../ui/LoadingSpinner'
import { apiHelpers, endpoints } from '../../config/api'
import { requestNotificationPermission } from '../../config/firebase'
import toast from 'react-hot-toast'

const NotificationSettings = ({ isOpen, onClose }) => {
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [preferences, setPreferences] = useState({
    email_notifications: true,
    push_notifications: true,
    sms_notifications: false,
    task_updates: true,
    payment_updates: true,
    promotional: false,
    review_reminders: true,
    weekly_summary: true,
  })
  const [pushPermission, setPushPermission] = useState('default')

  useEffect(() => {
    if (isOpen) {
      fetchPreferences()
      checkPushPermission()
    }
  }, [isOpen])

  const fetchPreferences = async () => {
    try {
      setLoading(true)
      const response = await apiHelpers.get(endpoints.notifications.preferences)
      
      if (response.success) {
        setPreferences(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch notification preferences:', error)
    } finally {
      setLoading(false)
    }
  }

  const checkPushPermission = () => {
    if ('Notification' in window) {
      setPushPermission(Notification.permission)
    }
  }

  const handlePreferenceChange = (key, value) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const enablePushNotifications = async () => {
    try {
      const token = await requestNotificationPermission()
      if (token) {
        setPushPermission('granted')
        handlePreferenceChange('push_notifications', true)
        toast.success('Push notifications enabled!')
      } else {
        toast.error('Failed to enable push notifications')
      }
    } catch (error) {
      console.error('Failed to enable push notifications:', error)
      toast.error('Failed to enable push notifications')
    }
  }

  const savePreferences = async () => {
    try {
      setSaving(true)
      const response = await apiHelpers.put(endpoints.notifications.preferences, preferences)
      
      if (response.success) {
        toast.success('Notification preferences updated!')
        onClose()
      }
    } catch (error) {
      console.error('Failed to save preferences:', error)
      toast.error('Failed to save preferences')
    } finally {
      setSaving(false)
    }
  }

  if (!isOpen) return null

  const notificationTypes = [
    {
      key: 'task_updates',
      title: 'Task Updates',
      description: 'Get notified when your task status changes',
      icon: Bell,
      color: 'text-blue-600 bg-blue-100'
    },
    {
      key: 'payment_updates',
      title: 'Payment Updates',
      description: 'Notifications about payment confirmations and receipts',
      icon: Bell,
      color: 'text-green-600 bg-green-100'
    },
    {
      key: 'review_reminders',
      title: 'Review Reminders',
      description: 'Reminders to review completed services',
      icon: Bell,
      color: 'text-yellow-600 bg-yellow-100'
    },
    {
      key: 'promotional',
      title: 'Promotional Offers',
      description: 'Special offers, discounts, and new service announcements',
      icon: Bell,
      color: 'text-purple-600 bg-purple-100'
    },
    {
      key: 'weekly_summary',
      title: 'Weekly Summary',
      description: 'Weekly summary of your activities and upcoming tasks',
      icon: Bell,
      color: 'text-gray-600 bg-gray-100'
    }
  ]

  const deliveryMethods = [
    {
      key: 'push_notifications',
      title: 'Push Notifications',
      description: 'Instant notifications on your device',
      icon: Smartphone,
      color: 'text-blue-600 bg-blue-100',
      requiresPermission: true
    },
    {
      key: 'email_notifications',
      title: 'Email Notifications',
      description: 'Notifications sent to your email address',
      icon: Mail,
      color: 'text-green-600 bg-green-100'
    },
    {
      key: 'sms_notifications',
      title: 'SMS Notifications',
      description: 'Text messages for important updates',
      icon: MessageSquare,
      color: 'text-orange-600 bg-orange-100'
    }
  ]

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Notification Settings
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-8">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <>
                {/* Delivery Methods */}
                <div>
                  <h4 className="text-base font-medium text-gray-900 mb-4">
                    How would you like to receive notifications?
                  </h4>
                  <div className="space-y-4">
                    {deliveryMethods.map((method) => (
                      <div key={method.key} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${method.color}`}>
                            <method.icon className="w-5 h-5" />
                          </div>
                          <div>
                            <h5 className="text-sm font-medium text-gray-900">
                              {method.title}
                            </h5>
                            <p className="text-sm text-gray-500">
                              {method.description}
                            </p>
                            {method.requiresPermission && pushPermission === 'denied' && (
                              <p className="text-xs text-red-600 mt-1">
                                Permission denied. Please enable in browser settings.
                              </p>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          {method.requiresPermission && pushPermission === 'default' && !preferences[method.key] && (
                            <button
                              onClick={enablePushNotifications}
                              className="btn btn-primary btn-sm"
                            >
                              Enable
                            </button>
                          )}
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              className="sr-only peer"
                              checked={preferences[method.key]}
                              onChange={(e) => handlePreferenceChange(method.key, e.target.checked)}
                              disabled={method.requiresPermission && pushPermission === 'denied'}
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Notification Types */}
                <div>
                  <h4 className="text-base font-medium text-gray-900 mb-4">
                    What notifications would you like to receive?
                  </h4>
                  <div className="space-y-4">
                    {notificationTypes.map((type) => (
                      <div key={type.key} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${type.color}`}>
                            <type.icon className="w-5 h-5" />
                          </div>
                          <div>
                            <h5 className="text-sm font-medium text-gray-900">
                              {type.title}
                            </h5>
                            <p className="text-sm text-gray-500">
                              {type.description}
                            </p>
                          </div>
                        </div>
                        
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={preferences[type.key]}
                            onChange={(e) => handlePreferenceChange(type.key, e.target.checked)}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Browser Notification Info */}
                {pushPermission !== 'granted' && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <Bell className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div className="text-sm text-blue-700">
                        <p className="font-medium mb-1">Enable Browser Notifications</p>
                        <p>
                          To receive instant push notifications, please allow notifications when prompted by your browser.
                          You can also enable them manually in your browser settings.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-4 p-6 border-t border-gray-200">
            <button
              onClick={onClose}
              className="btn btn-outline btn-md"
            >
              Cancel
            </button>
            <button
              onClick={savePreferences}
              disabled={saving || loading}
              className="btn btn-primary btn-md"
            >
              {saving ? (
                <LoadingSpinner size="sm" color="white" />
              ) : (
                'Save Settings'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NotificationSettings
