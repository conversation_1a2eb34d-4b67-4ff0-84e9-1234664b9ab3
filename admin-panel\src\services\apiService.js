import axios from 'axios'
import { authService } from './authService'
import toast from 'react-hot-toast'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = authService.getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    // Return the data directly for successful responses
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    
    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Unauthorized - clear auth and redirect to login
          authService.removeToken()
          window.location.href = '/login'
          break
          
        case 403:
          toast.error('Access denied. You don\'t have permission to perform this action.')
          break
          
        case 404:
          toast.error('Resource not found.')
          break
          
        case 422:
          // Validation errors
          if (data.errors) {
            const errorMessages = Object.values(data.errors).flat()
            errorMessages.forEach(message => toast.error(message))
          } else {
            toast.error(data.message || 'Validation error occurred.')
          }
          break
          
        case 429:
          toast.error('Too many requests. Please try again later.')
          break
          
        case 500:
          toast.error('Server error. Please try again later.')
          break
          
        default:
          toast.error(data.message || 'An unexpected error occurred.')
      }
      
      return Promise.reject(error.response.data)
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.')
      return Promise.reject({ message: 'Network error' })
    } else {
      // Other error
      toast.error('An unexpected error occurred.')
      return Promise.reject({ message: error.message })
    }
  }
)

// API endpoints
export const endpoints = {
  // Admin Authentication (using regular auth endpoints with admin role)
  admin: {
    login: '/auth/login',
    logout: '/auth/logout',
    profile: '/auth/profile',
    dashboard: '/admin/dashboard',
    analytics: '/admin/analytics',
  },

  // User Management
  users: {
    list: '/admin/users',
    detail: (id) => `/users/${id}`,
    update: (id) => `/users/${id}`,
    updateStatus: (id) => `/admin/users/${id}/status`,
  },
  
  // Work-Boy Management
  workboys: {
    list: '/admin/users?user_type=workboy',
    detail: (id) => `/users/${id}`,
    update: (id) => `/users/${id}`,
    updateStatus: (id) => `/admin/users/${id}/status`,
    approveKyc: (id) => `/admin/workboy/${id}/approve-kyc`,
    rejectKyc: (id) => `/admin/workboy/${id}/reject-kyc`,
  },

  // Task Management
  tasks: {
    list: '/admin/tasks',
    detail: (id) => `/tasks/${id}`,
    update: (id) => `/tasks/${id}`,
    updateStatus: (id) => `/tasks/${id}/status`,
    categories: '/tasks/categories',
  },
  
  // Financial Management
  payments: {
    list: '/admin/payments',
    detail: (id) => `/payments/${id}`,
  },
}

// API service methods
export const apiService = {
  // GET request
  get: async (url, config = {}) => {
    try {
      return await api.get(url, config)
    } catch (error) {
      throw error
    }
  },
  
  // POST request
  post: async (url, data = {}, config = {}) => {
    try {
      return await api.post(url, data, config)
    } catch (error) {
      throw error
    }
  },
  
  // PUT request
  put: async (url, data = {}, config = {}) => {
    try {
      return await api.put(url, data, config)
    } catch (error) {
      throw error
    }
  },
  
  // DELETE request
  delete: async (url, config = {}) => {
    try {
      return await api.delete(url, config)
    } catch (error) {
      throw error
    }
  },
  
  // Upload file
  uploadFile: async (url, file, onProgress = null) => {
    const formData = new FormData()
    formData.append('file', file)
    
    try {
      return await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            onProgress(percentCompleted)
          }
        },
      })
    } catch (error) {
      throw error
    }
  },
  
  // Upload multiple files
  uploadFiles: async (url, files, onProgress = null) => {
    const formData = new FormData()
    
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })
    
    try {
      return await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            onProgress(percentCompleted)
          }
        },
      })
    } catch (error) {
      throw error
    }
  },
  
  // Download file
  downloadFile: async (url, filename) => {
    try {
      const response = await api.get(url, {
        responseType: 'blob',
      })
      
      // Create blob link to download
      const blob = new Blob([response])
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
      
      return { success: true }
    } catch (error) {
      throw error
    }
  },
}

export default apiService
