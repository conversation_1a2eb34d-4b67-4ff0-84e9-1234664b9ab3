import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, TextInput, Card, Title, Paragraph } from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import { useAuth } from '@context/AuthContext';
import Toast from 'react-native-toast-message';

const ForgotPasswordScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const { forgotPassword } = useAuth();

  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm({
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      await forgotPassword(data.email);
      setEmailSent(true);
      Toast.show({
        type: 'success',
        text1: 'Reset Email Sent!',
        text2: 'Please check your email for password reset instructions.',
      });
    } catch (error) {
      console.error('Forgot password error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to send reset email. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  const handleResendEmail = async () => {
    const email = getValues('email');
    if (email) {
      await onSubmit({ email });
    }
  };

  if (emailSent) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.centeredContainer}>
          <Card style={styles.card}>
            <Card.Content style={styles.successContent}>
              <View style={styles.iconContainer}>
                <Text style={styles.successIcon}>✉️</Text>
              </View>
              
              <Title style={styles.successTitle}>Check Your Email</Title>
              
              <Paragraph style={styles.successText}>
                We've sent password reset instructions to your email address.
                Please check your inbox and follow the link to reset your password.
              </Paragraph>

              <View style={styles.buttonContainer}>
                <Button
                  mode="outlined"
                  onPress={handleResendEmail}
                  loading={loading}
                  disabled={loading}
                  style={styles.resendButton}
                  labelStyle={styles.resendButtonText}
                >
                  Resend Email
                </Button>

                <Button
                  mode="contained"
                  onPress={handleBackToLogin}
                  style={styles.backButton}
                  labelStyle={styles.backButtonText}
                >
                  Back to Login
                </Button>
              </View>
            </Card.Content>
          </Card>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Title style={styles.title}>Forgot Password?</Title>
            <Paragraph style={styles.subtitle}>
              Enter your email address and we'll send you instructions to reset your password.
            </Paragraph>
          </View>

          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.form}>
                <Controller
                  control={control}
                  rules={{
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Please enter a valid email address',
                    },
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Email Address"
                      mode="outlined"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.email}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      autoComplete="email"
                      style={styles.input}
                      left={<TextInput.Icon icon="email" />}
                      placeholder="Enter your registered email"
                    />
                  )}
                  name="email"
                />
                {errors.email && (
                  <Text style={styles.errorText}>{errors.email.message}</Text>
                )}

                <Button
                  mode="contained"
                  onPress={handleSubmit(onSubmit)}
                  loading={loading}
                  disabled={loading}
                  style={styles.resetButton}
                  labelStyle={styles.resetButtonText}
                >
                  {loading ? 'Sending...' : 'Send Reset Instructions'}
                </Button>
              </View>
            </Card.Content>
          </Card>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Remember your password? </Text>
            <Button
              mode="text"
              onPress={handleBackToLogin}
              labelStyle={styles.backToLoginText}
              compact
            >
              Back to Login
            </Button>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#10b981',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  card: {
    elevation: 4,
    borderRadius: 12,
    marginBottom: 20,
  },
  form: {
    paddingVertical: 10,
  },
  input: {
    marginBottom: 16,
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: -12,
    marginBottom: 16,
    marginLeft: 12,
  },
  resetButton: {
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#10b981',
    marginTop: 10,
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  footerText: {
    fontSize: 16,
    color: '#6b7280',
  },
  backToLoginText: {
    color: '#10b981',
    fontSize: 16,
    fontWeight: '600',
  },
  // Success screen styles
  successContent: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  iconContainer: {
    marginBottom: 20,
  },
  successIcon: {
    fontSize: 64,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#10b981',
    marginBottom: 16,
    textAlign: 'center',
  },
  successText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  resendButton: {
    borderColor: '#10b981',
    borderRadius: 8,
    paddingVertical: 4,
  },
  resendButtonText: {
    color: '#10b981',
    fontSize: 16,
    fontWeight: '600',
  },
  backButton: {
    backgroundColor: '#10b981',
    borderRadius: 8,
    paddingVertical: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ForgotPasswordScreen;
