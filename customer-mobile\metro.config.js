const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add support for additional file extensions
config.resolver.assetExts.push(
  // Adds support for `.db` files for SQLite databases
  'db',
  // Add other extensions if needed
  'bin',
  'txt',
  'jpg',
  'png',
  'webp',
  'gif',
  'svg'
);

// Add support for TypeScript
config.resolver.sourceExts.push('ts', 'tsx');

module.exports = config;
