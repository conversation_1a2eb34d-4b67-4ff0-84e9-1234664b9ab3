import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  StatusBar,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  IconButton,
  Searchbar,
  Menu,
  Button,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTask } from '@context/TaskContext';
import { colors, spacing, typography, borderRadius } from '@constants/theme';
import { showToast } from '@utils/toast';
import { format, formatDistanceToNow } from 'date-fns';

const TasksScreen = ({ navigation }) => {
  const {
    tasks,
    availableTasks,
    currentTask,
    loading,
    refreshTasks,
    acceptTask,
    rejectTask,
  } = useTask();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('available');
  const [menuVisible, setMenuVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filterOptions = [
    { value: 'available', label: 'Available Tasks' },
    { value: 'current', label: 'Current Task' },
    { value: 'completed', label: 'Completed' },
    { value: 'all', label: 'All Tasks' },
  ];

  useEffect(() => {
    refreshTasks();
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await refreshTasks();
    setRefreshing(false);
  }, []);

  const handleAcceptTask = async (taskId) => {
    Alert.alert(
      'Accept Task',
      'Are you sure you want to accept this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Accept',
          onPress: async () => {
            try {
              await acceptTask(taskId);
              navigation.navigate('TaskDetail', { taskId });
            } catch (error) {
              // Error is handled in context
            }
          },
        },
      ]
    );
  };

  const handleRejectTask = async (taskId) => {
    Alert.alert(
      'Reject Task',
      'Are you sure you want to reject this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reject',
          style: 'destructive',
          onPress: async () => {
            try {
              await rejectTask(taskId, 'Not suitable');
            } catch (error) {
              // Error is handled in context
            }
          },
        },
      ]
    );
  };

  const getFilteredTasks = () => {
    let filteredTasks = [];
    
    switch (selectedFilter) {
      case 'available':
        filteredTasks = availableTasks;
        break;
      case 'current':
        filteredTasks = currentTask ? [currentTask] : [];
        break;
      case 'completed':
        filteredTasks = tasks.filter(task => task.status === 'completed');
        break;
      case 'all':
        filteredTasks = tasks;
        break;
      default:
        filteredTasks = availableTasks;
    }

    if (searchQuery.trim()) {
      filteredTasks = filteredTasks.filter(task =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.category_name?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filteredTasks;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return colors.warning[500];
      case 'assigned':
        return colors.primary[500];
      case 'in_progress':
        return colors.blue[500];
      case 'completed':
        return colors.success[500];
      case 'cancelled':
        return colors.error[500];
      default:
        return colors.gray[500];
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return 'time-outline';
      case 'assigned':
        return 'person-outline';
      case 'in_progress':
        return 'play-circle-outline';
      case 'completed':
        return 'checkmark-circle-outline';
      case 'cancelled':
        return 'close-circle-outline';
      default:
        return 'time-outline';
    }
  };

  const getPriorityColor = (urgency) => {
    switch (urgency) {
      case 'urgent':
        return colors.error[500];
      case 'high':
        return colors.warning[500];
      case 'normal':
        return colors.primary[500];
      case 'low':
        return colors.success[500];
      default:
        return colors.gray[500];
    }
  };

  const renderTaskItem = ({ item }) => {
    const isAvailable = selectedFilter === 'available';
    const isCurrent = item.id === currentTask?.id;
    
    return (
      <Card 
        style={[
          styles.taskCard,
          isCurrent && styles.currentTaskCard
        ]}
        onPress={() => navigation.navigate('TaskDetail', { taskId: item.id })}
      >
        <Card.Content style={styles.taskCardContent}>
          <View style={styles.taskHeader}>
            <View style={styles.taskInfo}>
              <Text style={styles.taskTitle} numberOfLines={2}>
                {item.title}
              </Text>
              <Text style={styles.taskDescription} numberOfLines={2}>
                {item.description}
              </Text>
            </View>
            
            <View style={styles.taskMeta}>
              <Text style={styles.taskAmount}>₹{item.amount}</Text>
              {item.urgency && (
                <Chip
                  mode="outlined"
                  style={[
                    styles.urgencyChip,
                    { borderColor: getPriorityColor(item.urgency) }
                  ]}
                  textStyle={[
                    styles.urgencyChipText,
                    { color: getPriorityColor(item.urgency) }
                  ]}
                >
                  {item.urgency}
                </Chip>
              )}
            </View>
          </View>

          <View style={styles.taskDetails}>
            <View style={styles.taskDetailItem}>
              <Ionicons name="person-outline" size={16} color={colors.gray[500]} />
              <Text style={styles.taskDetailText}>{item.customer_name}</Text>
            </View>
            
            <View style={styles.taskDetailItem}>
              <Ionicons name="location-outline" size={16} color={colors.gray[500]} />
              <Text style={styles.taskDetailText}>{item.city}</Text>
            </View>
            
            <View style={styles.taskDetailItem}>
              <Ionicons name="time-outline" size={16} color={colors.gray[500]} />
              <Text style={styles.taskDetailText}>
                {item.scheduled_date ? 
                  format(new Date(item.scheduled_date), 'MMM dd, yyyy') :
                  formatDistanceToNow(new Date(item.created_at), { addSuffix: true })
                }
              </Text>
            </View>

            {item.distance && (
              <View style={styles.taskDetailItem}>
                <Ionicons name="navigate-outline" size={16} color={colors.gray[500]} />
                <Text style={styles.taskDetailText}>{item.distance.toFixed(1)} km away</Text>
              </View>
            )}
          </View>

          {!isAvailable && (
            <View style={styles.taskStatus}>
              <Chip
                mode="outlined"
                style={[
                  styles.statusChip,
                  { borderColor: getStatusColor(item.status) }
                ]}
                textStyle={[
                  styles.statusChipText,
                  { color: getStatusColor(item.status) }
                ]}
                icon={() => (
                  <Ionicons
                    name={getStatusIcon(item.status)}
                    size={14}
                    color={getStatusColor(item.status)}
                  />
                )}
              >
                {item.status.replace('_', ' ')}
              </Chip>
            </View>
          )}

          {isAvailable && (
            <View style={styles.taskActions}>
              <Button
                mode="outlined"
                onPress={() => handleRejectTask(item.id)}
                style={[styles.actionButton, styles.rejectButton]}
                labelStyle={styles.rejectButtonLabel}
              >
                Reject
              </Button>
              
              <Button
                mode="contained"
                onPress={() => handleAcceptTask(item.id)}
                style={[styles.actionButton, styles.acceptButton]}
                labelStyle={styles.acceptButtonLabel}
              >
                Accept
              </Button>
            </View>
          )}
        </Card.Content>
      </Card>
    );
  };

  const renderEmptyState = () => {
    const emptyMessages = {
      available: {
        icon: 'clipboard-outline',
        title: 'No available tasks',
        subtitle: 'Turn on availability to receive new tasks',
      },
      current: {
        icon: 'checkmark-circle-outline',
        title: 'No current task',
        subtitle: 'Accept a task to see it here',
      },
      completed: {
        icon: 'trophy-outline',
        title: 'No completed tasks',
        subtitle: 'Complete your first task to see it here',
      },
      all: {
        icon: 'list-outline',
        title: 'No tasks found',
        subtitle: 'Tasks will appear here when available',
      },
    };

    const message = emptyMessages[selectedFilter] || emptyMessages.available;

    return (
      <View style={styles.emptyContainer}>
        <Ionicons name={message.icon} size={64} color={colors.gray[400]} />
        <Text style={styles.emptyTitle}>{message.title}</Text>
        <Text style={styles.emptySubtitle}>{message.subtitle}</Text>
      </View>
    );
  };

  const filteredTasks = getFilteredTasks();
  const currentFilterLabel = filterOptions.find(f => f.value === selectedFilter)?.label || 'Tasks';

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Tasks</Text>
        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <IconButton
              icon="filter-outline"
              size={24}
              iconColor={colors.gray[600]}
              onPress={() => setMenuVisible(true)}
            />
          }
        >
          {filterOptions.map((option) => (
            <Menu.Item
              key={option.value}
              onPress={() => {
                setSelectedFilter(option.value);
                setMenuVisible(false);
              }}
              title={option.label}
              leadingIcon={selectedFilter === option.value ? 'check' : undefined}
            />
          ))}
        </Menu>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search tasks..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={colors.gray[500]}
        />
      </View>

      {/* Filter Indicator */}
      <View style={styles.filterIndicator}>
        <Chip
          mode="outlined"
          style={styles.filterChip}
          textStyle={styles.filterChipText}
        >
          {currentFilterLabel} ({filteredTasks.length})
        </Chip>
      </View>

      {/* Tasks List */}
      <FlatList
        data={filteredTasks}
        renderItem={renderTaskItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
  },
  searchBar: {
    backgroundColor: colors.gray[100],
    elevation: 0,
  },
  searchInput: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
  },
  filterIndicator: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
    backgroundColor: colors.white,
  },
  filterChip: {
    alignSelf: 'flex-start',
    backgroundColor: colors.primary[50],
    borderColor: colors.primary[200],
  },
  filterChipText: {
    color: colors.primary[700],
    fontSize: typography.fontSize.sm,
  },
  listContainer: {
    padding: spacing.lg,
  },
  taskCard: {
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
    elevation: 2,
  },
  currentTaskCard: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary[500],
  },
  taskCardContent: {
    paddingVertical: spacing.md,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  taskInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  taskTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  taskDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    lineHeight: typography.lineHeight.sm,
  },
  taskMeta: {
    alignItems: 'flex-end',
  },
  taskAmount: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.primary[600],
    marginBottom: spacing.xs,
  },
  urgencyChip: {
    backgroundColor: colors.transparent,
    height: 24,
  },
  urgencyChipText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.medium,
    textTransform: 'capitalize',
  },
  taskDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.md,
  },
  taskDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.lg,
    marginBottom: spacing.xs,
  },
  taskDetailText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    marginLeft: spacing.xs,
  },
  taskStatus: {
    alignItems: 'flex-start',
  },
  statusChip: {
    backgroundColor: colors.transparent,
    height: 28,
  },
  statusChipText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.medium,
    textTransform: 'capitalize',
  },
  taskActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    borderRadius: borderRadius.lg,
  },
  rejectButton: {
    borderColor: colors.error[300],
    marginRight: spacing.md,
  },
  rejectButtonLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.error[600],
  },
  acceptButton: {
    backgroundColor: colors.primary[600],
  },
  acceptButtonLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing['4xl'],
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
    paddingHorizontal: spacing.xl,
  },
});

export default TasksScreen;
