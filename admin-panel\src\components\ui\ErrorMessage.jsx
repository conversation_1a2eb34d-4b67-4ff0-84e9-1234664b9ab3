import React from 'react'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { clsx } from 'clsx'

export const ErrorMessage = ({ 
  title = 'Error',
  message,
  className = '',
  showIcon = true,
  variant = 'default',
  onRetry,
  ...props 
}) => {
  const variantClasses = {
    default: 'bg-red-50 border-red-200 text-red-800',
    minimal: 'text-red-600',
    card: 'bg-white border border-red-200 shadow-sm'
  }

  if (variant === 'minimal') {
    return (
      <div className={clsx('text-sm', variantClasses[variant], className)} {...props}>
        {message || title}
      </div>
    )
  }

  return (
    <div className={clsx(
      'rounded-md p-4',
      variantClasses[variant],
      className
    )} {...props}>
      <div className="flex">
        {showIcon && (
          <div className="flex-shrink-0">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
          </div>
        )}
        <div className={clsx('ml-3', !showIcon && 'ml-0')}>
          <h3 className="text-sm font-medium">
            {title}
          </h3>
          {message && (
            <div className="mt-2 text-sm">
              <p>{message}</p>
            </div>
          )}
          {onRetry && (
            <div className="mt-4">
              <button
                type="button"
                onClick={onRetry}
                className="bg-red-50 text-red-800 rounded-md px-2 py-1.5 text-sm font-medium hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try again
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ErrorMessage
