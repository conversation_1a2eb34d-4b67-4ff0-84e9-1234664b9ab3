name: PHPUnit

on:
  pull_request:
    branches:
      - develop

jobs:
  main:
    name: Build and test

    strategy:
      matrix:
        php-versions: ['8.1', '8.3']

    runs-on: ubuntu-latest

    if: (! contains(github.event.head_commit.message, '[ci skip]'))

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          tools: composer, pecl, phpunit
          extensions: intl, json, mbstring, mysqlnd, xdebug, xml, sqlite3
          coverage: xdebug

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache composer dependencies
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        run: composer install --no-progress --no-interaction --prefer-dist --optimize-autoloader
        # To prevent rate limiting you may need to supply an OAuth token in Settings > Secrets
        # env:
          # https://getcomposer.org/doc/articles/troubleshooting.md#api-rate-limit-and-oauth-tokens
          # COMPOSER_AUTH: ${{ secrets.COMPOSER_AUTH }}

      - name: Test with phpunit
        run: vendor/bin/phpunit --coverage-text
