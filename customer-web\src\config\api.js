import axios from 'axios'
import { auth } from './firebase'
import toast from 'react-hot-toast'

// API base URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const user = auth?.currentUser || auth?.user
      if (user) {
        // Check if this is a real Firebase user or mock user
        if (typeof user.getIdToken === 'function') {
          const token = await user.getIdToken()
          config.headers.Authorization = `Bearer ${token}`
        } else {
          // Mock user - use a placeholder token for development
          config.headers.Authorization = `Bearer mock-token-${user.uid}`
        }
      }
    } catch (error) {
      console.error('Error getting auth token:', error)
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    // Return the data directly for successful responses
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    
    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          toast.error('Session expired. Please login again.')
          // Clear local storage and redirect to login
          localStorage.clear()
          window.location.href = '/login'
          break
          
        case 403:
          toast.error('Access denied. You don\'t have permission to perform this action.')
          break
          
        case 404:
          toast.error('Resource not found.')
          break
          
        case 422:
          // Validation errors
          if (data.errors) {
            const errorMessages = Object.values(data.errors).flat()
            errorMessages.forEach(message => toast.error(message))
          } else {
            toast.error(data.message || 'Validation error occurred.')
          }
          break
          
        case 429:
          toast.error('Too many requests. Please try again later.')
          break
          
        case 500:
          toast.error('Server error. Please try again later.')
          break
          
        default:
          toast.error(data.message || 'An unexpected error occurred.')
      }
      
      return Promise.reject(error.response.data)
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.')
      return Promise.reject({ message: 'Network error' })
    } else {
      // Other error
      toast.error('An unexpected error occurred.')
      return Promise.reject({ message: error.message })
    }
  }
)

// API endpoints
export const endpoints = {
  // Authentication
  auth: {
    register: '/auth/register',
    login: '/auth/login',
    profile: '/auth/profile',
    logout: '/auth/logout',
    refreshToken: '/auth/refresh-token',
  },
  
  // Tasks
  tasks: {
    list: '/tasks',
    create: '/tasks',
    detail: (id) => `/tasks/${id}`,
    update: (id) => `/tasks/${id}`,
    delete: (id) => `/tasks/${id}`,
    categories: '/tasks/categories',
    updateStatus: (id) => `/tasks/${id}/status`,
  },
  
  // Customer
  customer: {
    dashboard: '/customer/dashboard',
    addresses: '/customer/addresses',
    createAddress: '/customer/addresses',
    updateAddress: (id) => `/customer/addresses/${id}`,
    deleteAddress: (id) => `/customer/addresses/${id}`,
    preferences: '/customer/preferences',
  },
  
  // Payments
  payments: {
    create: '/payments/create',
    confirm: '/payments/confirm',
    history: '/payments/history',
  },
  
  // Reviews
  reviews: {
    create: '/reviews',
    workboy: (id) => `/reviews/workboy/${id}`,
    customer: '/reviews/customer',
  },
  
  // Notifications
  notifications: {
    list: '/notifications',
    markRead: (id) => `/notifications/${id}/read`,
    markAllRead: '/notifications/read-all',
    delete: (id) => `/notifications/${id}`,
    preferences: '/notifications/preferences',
  },
}

// Helper functions for common API operations
export const apiHelpers = {
  // Get with error handling
  get: async (url, config = {}) => {
    try {
      return await api.get(url, config)
    } catch (error) {
      throw error
    }
  },
  
  // Post with error handling
  post: async (url, data = {}, config = {}) => {
    try {
      return await api.post(url, data, config)
    } catch (error) {
      throw error
    }
  },
  
  // Put with error handling
  put: async (url, data = {}, config = {}) => {
    try {
      return await api.put(url, data, config)
    } catch (error) {
      throw error
    }
  },
  
  // Delete with error handling
  delete: async (url, config = {}) => {
    try {
      return await api.delete(url, config)
    } catch (error) {
      throw error
    }
  },
  
  // Upload file
  uploadFile: async (url, file, onProgress = null) => {
    const formData = new FormData()
    formData.append('file', file)
    
    try {
      return await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            onProgress(percentCompleted)
          }
        },
      })
    } catch (error) {
      throw error
    }
  },
}

// Export the configured axios instance
export default api
