# Work-Boy Booking Platform - Backend API

A comprehensive task booking platform built with CodeIgniter 4, featuring Firebase Authentication, Razorpay payments, and real-time notifications.

## 🚀 Features

### Core Functionality
- **Multi-role Authentication**: Customer, Work-Boy, and Admin roles with Firebase Auth
- **Task Management**: Complete CRUD operations with status tracking
- **Payment Integration**: Razorpay payment gateway with webhooks
- **Review System**: Rating and review system for Work-Boys
- **Real-time Notifications**: Firebase Cloud Messaging (FCM) integration
- **Location Services**: GPS-based task assignment and tracking
- **Admin Dashboard**: Comprehensive analytics and user management

### Technical Features
- **RESTful API**: Clean, well-documented REST API endpoints
- **JWT Authentication**: Secure token-based authentication
- **Database Migrations**: Version-controlled database schema
- **Model Relationships**: Comprehensive data modeling with CodeIgniter 4
- **Input Validation**: Robust server-side validation
- **Error Handling**: Standardized error responses
- **Logging**: Comprehensive activity logging
- **CORS Support**: Cross-origin resource sharing enabled

## 📋 Prerequisites

- PHP 8.0 or higher
- MySQL 5.7 or higher
- Composer
- Firebase project (for authentication and FCM)
- Razorpay account (for payments)

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd backend
```

### 2. Install Dependencies
```bash
composer install
```

### 3. Environment Configuration
```bash
cp env .env
```

Edit `.env` file with your configuration:
```env
# Database Configuration
database.default.hostname = localhost
database.default.database = workboy_booking
database.default.username = root
database.default.password = your_password

# JWT Configuration
JWT_SECRET = your-super-secret-jwt-key
JWT_EXPIRE_TIME = 86400

# Firebase Configuration
FIREBASE_PROJECT_ID = your-firebase-project-id
FIREBASE_PRIVATE_KEY = "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FCM_SERVER_KEY = your-fcm-server-key
FCM_SENDER_ID = your-sender-id

# Razorpay Configuration
RAZORPAY_KEY_ID = your-razorpay-key-id
RAZORPAY_KEY_SECRET = your-razorpay-key-secret
RAZORPAY_WEBHOOK_SECRET = your-webhook-secret

# App Configuration
CI_ENVIRONMENT = development
app.baseURL = 'http://localhost:8080'
```

### 4. Database Setup
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE workboy_booking"

# Run migrations
php spark migrate

# Seed initial data
php spark db:seed DatabaseSeeder
```

### 5. Start Development Server
```bash
php spark serve
```

The API will be available at `http://localhost:8080`

## 📚 API Documentation

### Base URL
```
http://localhost:8080/api/v1
```

### Authentication
All protected endpoints require a Bearer token:
```
Authorization: Bearer <JWT_TOKEN>
```

### Key Endpoints

#### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `GET /auth/profile` - Get user profile
- `PUT /auth/profile` - Update profile

#### Tasks
- `GET /tasks/categories` - Get task categories
- `POST /tasks` - Create new task (Customer)
- `GET /tasks` - Get tasks list
- `GET /tasks/{id}` - Get task details
- `POST /tasks/{id}/status` - Update task status

#### Payments
- `POST /payments/create` - Create payment
- `POST /payments/confirm` - Confirm payment
- `GET /payments/history` - Payment history

#### Reviews
- `POST /reviews` - Create review
- `GET /reviews/workboy/{id}` - Get Work-Boy reviews

For complete API documentation, see [API_DOCUMENTATION.md](API_DOCUMENTATION.md)

## 🏗️ Project Structure

```
backend/
├── app/
│   ├── Controllers/
│   │   └── Api/
│   │       ├── BaseApiController.php
│   │       └── V1/
│   │           ├── AuthController.php
│   │           ├── TaskController.php
│   │           ├── CustomerController.php
│   │           ├── WorkBoyController.php
│   │           ├── PaymentController.php
│   │           ├── ReviewController.php
│   │           ├── NotificationController.php
│   │           ├── UserController.php
│   │           └── AdminController.php
│   ├── Models/
│   │   ├── UserModel.php
│   │   ├── TaskModel.php
│   │   ├── PaymentModel.php
│   │   ├── ReviewModel.php
│   │   └── ...
│   ├── Filters/
│   │   ├── AuthFilter.php
│   │   └── AdminFilter.php
│   ├── Services/
│   │   ├── RazorpayService.php
│   │   └── FirebaseService.php
│   ├── Database/
│   │   ├── Migrations/
│   │   └── Seeds/
│   └── Config/
├── public/
└── vendor/
```

## 🔧 Configuration

### Database Schema
The platform uses 10 main tables:
- `users` - User accounts
- `customer_profiles` - Customer-specific data
- `workboy_profiles` - Work-Boy specific data
- `addresses` - User addresses
- `task_categories` - Task categories
- `tasks` - Main tasks table
- `task_status_history` - Task status tracking
- `payments` - Payment records
- `reviews` - Review and ratings
- `notifications` - User notifications

### User Roles
1. **Customer**: Can create tasks, make payments, leave reviews
2. **Work-Boy**: Can accept tasks, update status, receive payments
3. **Admin**: Can manage users, approve KYC, view analytics

## 🧪 Testing

### Run Migrations
```bash
php spark migrate
```

### Seed Test Data
```bash
php spark db:seed DatabaseSeeder
```

### API Testing
Use tools like Postman or curl to test endpoints:
```bash
# Register a new customer
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firebase_uid": "test123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "user_type": "customer"
  }'
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based auth
- **Input Validation**: Server-side validation for all inputs
- **SQL Injection Protection**: Parameterized queries
- **CORS Configuration**: Controlled cross-origin access
- **Rate Limiting**: API rate limiting (future implementation)
- **Password Hashing**: Secure password storage (Firebase handles this)

## 📊 Monitoring & Analytics

### Admin Dashboard Features
- User registration trends
- Task completion rates
- Revenue analytics
- Work-Boy performance metrics
- Platform health monitoring

### Logging
- User activities
- Payment transactions
- Error tracking
- Performance monitoring

## 🚀 Deployment

### Production Setup
1. Set `CI_ENVIRONMENT = production` in `.env`
2. Configure production database
3. Set up SSL certificates
4. Configure web server (Apache/Nginx)
5. Set up monitoring and backups

## Server Requirements

PHP version 8.1 or higher is required, with the following extensions installed:

- [intl](https://php.net/manual/en/intl.requirements.php)
- [mbstring](https://php.net/manual/en/mbstring.installation.php)
- [mysqlnd](https://php.net/manual/en/mysqlnd.install.php) if you plan to use MySQL
- [libcurl](https://php.net/manual/en/curl.requirements.php) if you plan to use the HTTP\CURLRequest library

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation
- Review the error logs in `writable/logs/`

## 🔄 Version History

- **v1.0.0** - Initial release with core features
- **v1.1.0** - Added payment integration
- **v1.2.0** - Added notification system
- **v1.3.0** - Added admin analytics

## 🎯 Future Enhancements

- WebSocket integration for real-time updates
- Advanced search and filtering
- Multi-language support
- Mobile app deep linking
- Advanced analytics and reporting
- Machine learning for task recommendations
