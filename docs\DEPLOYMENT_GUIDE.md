# WorkBoy Platform Deployment Guide

## Overview

This guide provides comprehensive deployment instructions for the WorkBoy platform across all environments (Development, Staging, Production).

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Server    │    │   Database      │
│   (Nginx/ALB)   │────│   (Apache/Nginx)│────│   (MySQL)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   API Server    │    │   Redis Cache   │
                       │   (PHP CI4)     │────│                 │
                       └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │  WebSocket      │    │   File Storage  │
                       │  Server         │    │   (S3/Local)    │
                       └─────────────────┘    └─────────────────┘
```

## Environment Setup

### Development Environment

#### Prerequisites
- PHP 8.1+
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Composer
- npm/yarn

#### Backend Setup
```bash
# Clone repository
git clone https://github.com/workboy/platform.git
cd platform/backend

# Install dependencies
composer install

# Environment configuration
cp .env.example .env
php spark key:generate

# Database setup
php spark migrate
php spark db:seed

# Start development server
php spark serve --port=8080
```

#### Frontend Setup
```bash
# Customer Web App
cd customer-web
npm install
npm start

# Admin Panel
cd admin-panel
npm install
npm run dev

# Mobile Apps (requires Expo CLI)
cd customer-mobile
npm install
npx expo start

cd workboy-mobile
npm install
npx expo start
```

### Staging Environment

#### Docker Compose Setup
```yaml
# docker-compose.staging.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/staging.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
      - web

  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.staging
    environment:
      - CI_ENVIRONMENT=staging
      - DATABASE_HOST=mysql
      - REDIS_HOST=redis
    volumes:
      - ./backend:/var/www/html
    depends_on:
      - mysql
      - redis

  web:
    build:
      context: ./customer-web
      dockerfile: Dockerfile.staging
    environment:
      - REACT_APP_API_URL=https://staging-api.workboy.com
      - REACT_APP_WS_URL=wss://staging-ws.workboy.com

  admin:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile.staging
    environment:
      - VITE_API_BASE_URL=https://staging-api.workboy.com

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: staging_password
      MYSQL_DATABASE: workboy_staging
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

#### Deployment Commands
```bash
# Deploy to staging
docker-compose -f docker-compose.staging.yml up -d

# Run migrations
docker-compose exec api php spark migrate

# Seed data
docker-compose exec api php spark db:seed
```

### Production Environment

#### AWS Infrastructure (Terraform)
```hcl
# infrastructure/main.tf
provider "aws" {
  region = "ap-south-1"
}

# VPC and Networking
resource "aws_vpc" "workboy_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "workboy-vpc"
  }
}

# Application Load Balancer
resource "aws_lb" "workboy_alb" {
  name               = "workboy-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb_sg.id]
  subnets           = aws_subnet.public[*].id

  enable_deletion_protection = true
}

# ECS Cluster
resource "aws_ecs_cluster" "workboy_cluster" {
  name = "workboy-cluster"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

# RDS Database
resource "aws_db_instance" "workboy_db" {
  identifier     = "workboy-production"
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = "db.t3.medium"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_encrypted     = true
  
  db_name  = "workboy_production"
  username = "workboy_admin"
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  db_subnet_group_name   = aws_db_subnet_group.workboy_db_subnet_group.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = false
  final_snapshot_identifier = "workboy-final-snapshot"
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "workboy_cache_subnet" {
  name       = "workboy-cache-subnet"
  subnet_ids = aws_subnet.private[*].id
}

resource "aws_elasticache_cluster" "workboy_redis" {
  cluster_id           = "workboy-redis"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.workboy_cache_subnet.name
  security_group_ids   = [aws_security_group.redis_sg.id]
}

# S3 Bucket for file storage
resource "aws_s3_bucket" "workboy_storage" {
  bucket = "workboy-production-storage"
}

resource "aws_s3_bucket_versioning" "workboy_storage_versioning" {
  bucket = aws_s3_bucket.workboy_storage.id
  versioning_configuration {
    status = "Enabled"
  }
}
```

#### ECS Task Definitions
```json
{
  "family": "workboy-api",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "workboy-api",
      "image": "workboy/api:latest",
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "CI_ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_PASSWORD",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:workboy/db-password"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/workboy-api",
          "awslogs-region": "ap-south-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

## CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run Backend Tests
        run: |
          cd backend
          composer install
          php spark test
      
      - name: Run Frontend Tests
        run: |
          cd customer-web
          npm ci
          npm test -- --coverage
      
      - name: Run Mobile Tests
        run: |
          cd customer-mobile
          npm ci
          npm test

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1
      
      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
      
      - name: Build and push API image
        run: |
          cd backend
          docker build -t workboy/api:${{ github.sha }} .
          docker tag workboy/api:${{ github.sha }} $ECR_REGISTRY/workboy/api:${{ github.sha }}
          docker push $ECR_REGISTRY/workboy/api:${{ github.sha }}
      
      - name: Build and push Web image
        run: |
          cd customer-web
          docker build -t workboy/web:${{ github.sha }} .
          docker tag workboy/web:${{ github.sha }} $ECR_REGISTRY/workboy/web:${{ github.sha }}
          docker push $ECR_REGISTRY/workboy/web:${{ github.sha }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to ECS
        run: |
          aws ecs update-service \
            --cluster workboy-cluster \
            --service workboy-api-service \
            --task-definition workboy-api:${{ github.sha }} \
            --force-new-deployment
```

## Mobile App Deployment

### iOS Deployment (App Store)
```yaml
# .github/workflows/ios-deploy.yml
name: iOS Deployment

on:
  push:
    tags:
      - 'ios-v*'

jobs:
  deploy-ios:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
      
      - name: Setup Expo CLI
        run: npm install -g @expo/cli
      
      - name: Install dependencies
        run: |
          cd customer-mobile
          npm ci
      
      - name: Build iOS app
        run: |
          cd customer-mobile
          expo build:ios --release-channel production
      
      - name: Upload to App Store
        run: |
          cd customer-mobile
          expo upload:ios
```

### Android Deployment (Play Store)
```yaml
# .github/workflows/android-deploy.yml
name: Android Deployment

on:
  push:
    tags:
      - 'android-v*'

jobs:
  deploy-android:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
      
      - name: Setup Expo CLI
        run: npm install -g @expo/cli
      
      - name: Install dependencies
        run: |
          cd customer-mobile
          npm ci
      
      - name: Build Android app
        run: |
          cd customer-mobile
          expo build:android --release-channel production
      
      - name: Upload to Play Store
        run: |
          cd customer-mobile
          expo upload:android
```

## Environment Variables

### Production Environment Variables
```bash
# Backend (.env)
CI_ENVIRONMENT=production
APP_BASEURL=https://api.workboy.com
DATABASE_HOST=workboy-db.cluster-xxx.ap-south-1.rds.amazonaws.com
DATABASE_USERNAME=workboy_admin
DATABASE_PASSWORD=${DB_PASSWORD}
REDIS_HOST=workboy-redis.xxx.cache.amazonaws.com
JWT_SECRET_KEY=${JWT_SECRET}
RAZORPAY_KEY_ID=${RAZORPAY_KEY}
RAZORPAY_KEY_SECRET=${RAZORPAY_SECRET}
AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_KEY}
AWS_DEFAULT_REGION=ap-south-1
AWS_BUCKET=workboy-production-storage
FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
```

### Frontend Environment Variables
```bash
# Customer Web (.env.production)
REACT_APP_API_URL=https://api.workboy.com/api/v1
REACT_APP_WS_URL=wss://ws.workboy.com
REACT_APP_FIREBASE_API_KEY=${FIREBASE_API_KEY}
REACT_APP_FIREBASE_AUTH_DOMAIN=workboy-prod.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=workboy-prod
REACT_APP_RAZORPAY_KEY_ID=${RAZORPAY_KEY_ID}
REACT_APP_GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
```

### Mobile Environment Variables
```bash
# Mobile Apps (.env.production)
EXPO_PUBLIC_API_URL=https://api.workboy.com/api/v1
EXPO_PUBLIC_WS_URL=wss://ws.workboy.com
EXPO_PUBLIC_FIREBASE_API_KEY=${FIREBASE_API_KEY}
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=workboy-prod.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=workboy-prod
```

## Monitoring and Logging

### Application Monitoring
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./monitoring/logstash.conf:/usr/share/logstash/pipeline/logstash.conf

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200

volumes:
  grafana_data:
```

## Security Configuration

### SSL/TLS Setup
```nginx
# nginx/production.conf
server {
    listen 443 ssl http2;
    server_name api.workboy.com;

    ssl_certificate /etc/nginx/ssl/workboy.crt;
    ssl_certificate_key /etc/nginx/ssl/workboy.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://api:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Security Headers
```nginx
# Security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

## Backup and Recovery

### Database Backup
```bash
#!/bin/bash
# backup-db.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/mysql"
DB_NAME="workboy_production"

# Create backup
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/workboy_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/workboy_$DATE.sql

# Upload to S3
aws s3 cp $BACKUP_DIR/workboy_$DATE.sql.gz s3://workboy-backups/database/

# Cleanup old backups (keep last 30 days)
find $BACKUP_DIR -name "workboy_*.sql.gz" -mtime +30 -delete
```

### File Storage Backup
```bash
#!/bin/bash
# backup-files.sh
aws s3 sync s3://workboy-production-storage s3://workboy-backups/files/ --delete
```

## Rollback Procedures

### Application Rollback
```bash
# Rollback to previous version
aws ecs update-service \
  --cluster workboy-cluster \
  --service workboy-api-service \
  --task-definition workboy-api:previous-version \
  --force-new-deployment
```

### Database Rollback
```bash
# Restore from backup
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME < backup_file.sql
```

## Health Checks

### Application Health Endpoints
```php
// Backend health check
Route::get('/health', function() {
    return [
        'status' => 'healthy',
        'timestamp' => time(),
        'version' => '1.0.0',
        'database' => checkDatabaseConnection(),
        'redis' => checkRedisConnection(),
        'storage' => checkStorageConnection()
    ];
});
```

### Load Balancer Health Checks
```json
{
  "HealthCheckPath": "/health",
  "HealthCheckIntervalSeconds": 30,
  "HealthCheckTimeoutSeconds": 5,
  "HealthyThresholdCount": 2,
  "UnhealthyThresholdCount": 3
}
```

## Performance Optimization

### CDN Configuration
```javascript
// CloudFront distribution
{
  "Origins": [
    {
      "DomainName": "workboy-storage.s3.amazonaws.com",
      "OriginPath": "/static",
      "S3OriginConfig": {
        "OriginAccessIdentity": "origin-access-identity/cloudfront/ABCDEFG1234567"
      }
    }
  ],
  "DefaultCacheBehavior": {
    "TargetOriginId": "S3-workboy-storage",
    "ViewerProtocolPolicy": "redirect-to-https",
    "CachePolicyId": "managed-caching-optimized"
  }
}
```

### Database Optimization
```sql
-- Index optimization
CREATE INDEX idx_tasks_status_created ON tasks(status, created_at);
CREATE INDEX idx_users_type_active ON users(user_type, is_active);
CREATE INDEX idx_payments_status_date ON payments(status, created_at);

-- Query optimization
ANALYZE TABLE tasks;
OPTIMIZE TABLE tasks;
```

This comprehensive deployment guide ensures a robust, scalable, and secure production environment for the WorkBoy platform.
