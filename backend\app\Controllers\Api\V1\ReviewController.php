<?php

namespace App\Controllers\Api\V1;

use App\Controllers\Api\BaseApiController;
use App\Models\ReviewModel;
use App\Models\TaskModel;

/**
 * Review Controller for Work-Boy Booking API
 * 
 * Handles review and rating system for Work-Boys
 */
class ReviewController extends BaseApiController
{
    protected $reviewModel;
    protected $taskModel;

    public function __construct()
    {
        parent::__construct();
        $this->reviewModel = new ReviewModel();
        $this->taskModel = new TaskModel();
    }

    /**
     * Create a review
     * POST /api/v1/reviews
     */
    public function create()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $rules = [
                'task_id'     => 'required|integer',
                'workboy_id'  => 'required|integer',
                'rating'      => 'required|integer|greater_than[0]|less_than[6]',
                'review_text' => 'permit_empty',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Check if customer can review this task
            $canReview = $this->reviewModel->canReviewTask($data['task_id'], $currentUser['id']);
            
            if (!$canReview['can_review']) {
                return $this->errorResponse($canReview['reason'], 400);
            }

            // Verify task details
            $task = $this->taskModel->find($data['task_id']);
            
            if ($task['workboy_id'] != $data['workboy_id']) {
                return $this->errorResponse('Work-Boy ID does not match task assignment', 400);
            }

            // Create review
            $reviewData = [
                'task_id'     => $data['task_id'],
                'customer_id' => $currentUser['id'],
                'workboy_id'  => $data['workboy_id'],
                'rating'      => $data['rating'],
                'review_text' => $data['review_text'] ?? null,
            ];

            $reviewId = $this->reviewModel->insert($reviewData);

            if (!$reviewId) {
                return $this->serverErrorResponse('Failed to create review');
            }

            // Get created review with details
            $review = $this->reviewModel->select('reviews.*, tasks.title as task_title,
                                                customers.first_name as customer_first_name, 
                                                customers.last_name as customer_last_name')
                                      ->join('tasks', 'tasks.id = reviews.task_id')
                                      ->join('users customers', 'customers.id = reviews.customer_id')
                                      ->where('reviews.id', $reviewId)
                                      ->first();

            $this->logActivity('review_created', [
                'review_id' => $reviewId,
                'task_id' => $data['task_id'],
                'workboy_id' => $data['workboy_id'],
                'rating' => $data['rating']
            ]);

            return $this->apiResponse($review, 'Review created successfully', 201);

        } catch (\Exception $e) {
            log_message('error', 'Create review error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to create review');
        }
    }

    /**
     * Get reviews for a Work-Boy
     * GET /api/v1/reviews/workboy/{id}
     */
    public function getWorkBoyReviews($id = null)
    {
        try {
            if (!$id) {
                return $this->errorResponse('Work-Boy ID is required', 400);
            }

            $pagination = $this->getPaginationParams();
            $rating = $this->request->getGet('rating');

            $builder = $this->reviewModel->select('reviews.*, tasks.title as task_title,
                                                 customers.first_name as customer_first_name, 
                                                 customers.last_name as customer_last_name,
                                                 customers.profile_image as customer_profile_image')
                                        ->join('tasks', 'tasks.id = reviews.task_id')
                                        ->join('users customers', 'customers.id = reviews.customer_id')
                                        ->where('reviews.workboy_id', $id);

            // Filter by rating if specified
            if ($rating) {
                $builder->where('reviews.rating', $rating);
            }

            // Get total count for pagination
            $total = $builder->countAllResults(false);

            // Get reviews
            $reviews = $builder->orderBy('reviews.created_at', 'DESC')
                             ->limit($pagination['limit'], $pagination['offset'])
                             ->get()
                             ->getResultArray();

            // Get rating statistics
            $ratingStats = $this->reviewModel->getWorkBoyRatingStats($id);

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);
            $meta['rating_stats'] = $ratingStats;

            return $this->apiResponse($reviews, 'Reviews retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Get Work-Boy reviews error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve reviews');
        }
    }

    /**
     * Get reviews by customer
     * GET /api/v1/reviews/customer
     */
    public function getCustomerReviews()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $pagination = $this->getPaginationParams();

            $reviews = $this->reviewModel->getByCustomerId($currentUser['id'], $pagination['limit'], $pagination['offset']);

            // Get total count for pagination
            $total = $this->reviewModel->where('customer_id', $currentUser['id'])->countAllResults();

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($reviews, 'Customer reviews retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Get customer reviews error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve customer reviews');
        }
    }

    /**
     * Get recent reviews (Admin only)
     * GET /api/v1/reviews/recent
     */
    public function getRecentReviews()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $limit = min(50, max(1, (int) $this->request->getGet('limit') ?: 10));

            $reviews = $this->reviewModel->getRecentReviews($limit);

            return $this->apiResponse($reviews, 'Recent reviews retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get recent reviews error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve recent reviews');
        }
    }

    /**
     * Get top rated Work-Boys
     * GET /api/v1/reviews/top-workboys
     */
    public function getTopWorkBoys()
    {
        try {
            $limit = min(20, max(1, (int) $this->request->getGet('limit') ?: 10));
            $minReviews = max(1, (int) $this->request->getGet('min_reviews') ?: 5);

            $topWorkBoys = $this->reviewModel->getTopRatedWorkBoys($limit, $minReviews);

            return $this->apiResponse($topWorkBoys, 'Top rated Work-Boys retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get top Work-Boys error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve top rated Work-Boys');
        }
    }

    /**
     * Search reviews (Admin only)
     * GET /api/v1/reviews/search
     */
    public function searchReviews()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $query = $this->request->getGet('q');
            
            if (!$query) {
                return $this->errorResponse('Search query is required', 400);
            }

            $pagination = $this->getPaginationParams();

            $reviews = $this->reviewModel->searchReviews($query, $pagination['limit'], $pagination['offset']);

            // Get total count for pagination (approximate)
            $total = count($reviews); // This is not accurate for pagination, but works for demo

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($reviews, 'Search results retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Search reviews error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to search reviews');
        }
    }

    /**
     * Get review statistics (Admin only)
     * GET /api/v1/reviews/stats
     */
    public function getStats()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $workboyId = $this->request->getGet('workboy_id');

            // Get overall rating distribution
            $ratingDistribution = $this->reviewModel->getRatingDistribution($workboyId);

            // Get monthly review trends
            $monthlyTrends = $this->reviewModel->getMonthlyReviewTrends(12);

            // Get basic statistics
            $builder = $this->reviewModel->builder();
            
            if ($workboyId) {
                $builder->where('workboy_id', $workboyId);
            }

            $basicStats = $builder->select('
                                COUNT(*) as total_reviews,
                                AVG(rating) as average_rating,
                                MIN(rating) as min_rating,
                                MAX(rating) as max_rating,
                                COUNT(CASE WHEN review_text IS NOT NULL AND review_text != "" THEN 1 END) as reviews_with_text
                            ')
                            ->get()
                            ->getRowArray();

            $statsData = [
                'basic_stats' => $basicStats,
                'rating_distribution' => $ratingDistribution,
                'monthly_trends' => $monthlyTrends,
            ];

            return $this->apiResponse($statsData, 'Review statistics retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get review stats error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve review statistics');
        }
    }

    /**
     * Delete review (Admin only)
     * DELETE /api/v1/reviews/{id}
     */
    public function delete($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $review = $this->reviewModel->find($id);

            if (!$review) {
                return $this->notFoundResponse('Review not found');
            }

            $deleted = $this->reviewModel->delete($id);

            if (!$deleted) {
                return $this->serverErrorResponse('Failed to delete review');
            }

            // Note: In a production system, you might want to recalculate the Work-Boy's rating
            // after deleting a review

            $this->logActivity('review_deleted', [
                'review_id' => $id,
                'workboy_id' => $review['workboy_id'],
                'task_id' => $review['task_id']
            ]);

            return $this->apiResponse(null, 'Review deleted successfully');

        } catch (\Exception $e) {
            log_message('error', 'Delete review error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to delete review');
        }
    }
}
