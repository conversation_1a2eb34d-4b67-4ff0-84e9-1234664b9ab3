<?php

namespace App\Models;

use CodeIgniter\Model;

class WorkboyProfileModel extends Model
{
    protected $table            = 'workboy_profiles';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'user_id',
        'kyc_status',
        'kyc_documents',
        'skills',
        'hourly_rate',
        'availability_status',
        'rating',
        'total_tasks_completed',
        'total_earnings',
        'bank_details'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id'             => 'required|integer|is_unique[workboy_profiles.user_id,id,{id}]',
        'kyc_status'          => 'permit_empty|in_list[pending,approved,rejected]',
        'kyc_documents'       => 'permit_empty|valid_json',
        'skills'              => 'permit_empty|valid_json',
        'hourly_rate'         => 'permit_empty|decimal|greater_than[0]',
        'availability_status' => 'permit_empty|in_list[available,busy,offline]',
        'rating'              => 'permit_empty|decimal|greater_than_equal_to[0]|less_than_equal_to[5]',
        'total_tasks_completed' => 'permit_empty|integer|greater_than_equal_to[0]',
        'total_earnings'      => 'permit_empty|decimal|greater_than_equal_to[0]',
        'bank_details'        => 'permit_empty|valid_json',
    ];

    protected $validationMessages = [
        'user_id' => [
            'required'  => 'User ID is required',
            'is_unique' => 'Work-Boy profile already exists for this user',
        ],
        'kyc_status' => [
            'in_list' => 'KYC status must be pending, approved, or rejected',
        ],
        'availability_status' => [
            'in_list' => 'Availability status must be available, busy, or offline',
        ],
        'rating' => [
            'greater_than_equal_to' => 'Rating must be between 0 and 5',
            'less_than_equal_to'    => 'Rating must be between 0 and 5',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['encodeJsonFields'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['encodeJsonFields'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = ['decodeJsonFields'];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Encode JSON fields before saving
     */
    protected function encodeJsonFields(array $data)
    {
        $jsonFields = ['kyc_documents', 'skills', 'bank_details'];
        
        foreach ($jsonFields as $field) {
            if (isset($data['data'][$field]) && is_array($data['data'][$field])) {
                $data['data'][$field] = json_encode($data['data'][$field]);
            }
        }

        return $data;
    }

    /**
     * Decode JSON fields after retrieval
     */
    protected function decodeJsonFields(array $data)
    {
        $jsonFields = ['kyc_documents', 'skills', 'bank_details'];
        
        if (isset($data['data'])) {
            // Handle single record
            foreach ($jsonFields as $field) {
                if (isset($data['data'][$field])) {
                    $data['data'][$field] = json_decode($data['data'][$field], true);
                }
            }
        } else {
            // Handle multiple records
            foreach ($data as &$record) {
                foreach ($jsonFields as $field) {
                    if (isset($record[$field])) {
                        $record[$field] = json_decode($record[$field], true);
                    }
                }
            }
        }

        return $data;
    }

    /**
     * Get Work-Boy profile by user ID
     */
    public function getByUserId(int $userId)
    {
        return $this->where('user_id', $userId)->first();
    }

    /**
     * Update KYC status
     */
    public function updateKYCStatus(int $userId, string $status)
    {
        return $this->where('user_id', $userId)
                   ->set('kyc_status', $status)
                   ->update();
    }

    /**
     * Submit KYC documents
     */
    public function submitKYC(int $userId, array $documents)
    {
        return $this->where('user_id', $userId)
                   ->set([
                       'kyc_documents' => json_encode($documents),
                       'kyc_status' => 'pending'
                   ])
                   ->update();
    }

    /**
     * Update availability status
     */
    public function updateAvailability(int $userId, string $status)
    {
        return $this->where('user_id', $userId)
                   ->set('availability_status', $status)
                   ->update();
    }

    /**
     * Update skills
     */
    public function updateSkills(int $userId, array $skills)
    {
        return $this->where('user_id', $userId)
                   ->set('skills', json_encode($skills))
                   ->update();
    }

    /**
     * Update bank details
     */
    public function updateBankDetails(int $userId, array $bankDetails)
    {
        return $this->where('user_id', $userId)
                   ->set('bank_details', json_encode($bankDetails))
                   ->update();
    }

    /**
     * Update rating after task completion
     */
    public function updateRating(int $userId, float $newRating)
    {
        // Get current stats
        $profile = $this->getByUserId($userId);
        if (!$profile) {
            return false;
        }

        $currentRating = $profile['rating'];
        $totalTasks = $profile['total_tasks_completed'];
        
        // Calculate new average rating
        $newAverageRating = (($currentRating * $totalTasks) + $newRating) / ($totalTasks + 1);
        
        return $this->where('user_id', $userId)
                   ->set([
                       'rating' => round($newAverageRating, 2),
                       'total_tasks_completed' => $totalTasks + 1
                   ])
                   ->update();
    }

    /**
     * Add earnings
     */
    public function addEarnings(int $userId, float $amount)
    {
        return $this->where('user_id', $userId)
                   ->set('total_earnings', 'total_earnings + ' . $amount, false)
                   ->update();
    }

    /**
     * Get available Work-Boys
     */
    public function getAvailableWorkBoys(int $limit = 20, int $offset = 0)
    {
        return $this->select('workboy_profiles.*, users.first_name, users.last_name, users.email, users.phone, users.profile_image')
                   ->join('users', 'users.id = workboy_profiles.user_id')
                   ->where('workboy_profiles.kyc_status', 'approved')
                   ->where('workboy_profiles.availability_status', 'available')
                   ->where('users.status', 'active')
                   ->orderBy('workboy_profiles.rating', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get Work-Boys by location (for future implementation)
     */
    public function getWorkBoysByLocation(float $latitude, float $longitude, float $radius = 10, int $limit = 20)
    {
        // This would require location data in the profile or separate location tracking
        // For now, return available Work-Boys
        return $this->getAvailableWorkBoys($limit);
    }

    /**
     * Get Work-Boys pending KYC approval
     */
    public function getPendingKYC(int $limit = 20, int $offset = 0)
    {
        return $this->select('workboy_profiles.*, users.first_name, users.last_name, users.email, users.phone, users.created_at')
                   ->join('users', 'users.id = workboy_profiles.user_id')
                   ->where('workboy_profiles.kyc_status', 'pending')
                   ->where('users.status', 'active')
                   ->orderBy('workboy_profiles.created_at', 'ASC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get Work-Boy with user data and stats
     */
    public function getWorkBoyWithStats(int $userId)
    {
        $builder = $this->db->table('workboy_profiles wp');
        
        return $builder->select('wp.*, u.first_name, u.last_name, u.email, u.phone, u.profile_image, u.status,
                                COUNT(t.id) as total_assigned_tasks,
                                SUM(CASE WHEN t.status = "completed" THEN 1 ELSE 0 END) as completed_tasks,
                                SUM(CASE WHEN t.status = "in_progress" THEN 1 ELSE 0 END) as active_tasks,
                                AVG(r.rating) as average_rating,
                                COUNT(r.id) as total_reviews')
                      ->join('users u', 'u.id = wp.user_id')
                      ->join('tasks t', 't.workboy_id = wp.user_id', 'left')
                      ->join('reviews r', 'r.workboy_id = wp.user_id', 'left')
                      ->where('wp.user_id', $userId)
                      ->groupBy('wp.id')
                      ->get()
                      ->getRowArray();
    }

    /**
     * Get top rated Work-Boys
     */
    public function getTopRatedWorkBoys(int $limit = 10)
    {
        return $this->select('workboy_profiles.*, users.first_name, users.last_name, users.profile_image')
                   ->join('users', 'users.id = workboy_profiles.user_id')
                   ->where('workboy_profiles.kyc_status', 'approved')
                   ->where('workboy_profiles.total_tasks_completed >', 0)
                   ->where('users.status', 'active')
                   ->orderBy('workboy_profiles.rating', 'DESC')
                   ->orderBy('workboy_profiles.total_tasks_completed', 'DESC')
                   ->findAll($limit);
    }
}
