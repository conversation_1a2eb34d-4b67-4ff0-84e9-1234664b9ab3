import { useState, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import { <PERSON> } from 'react-router-dom'
import {
  CreditCardIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  CalendarDaysIcon,
  UserIcon,
  BanknotesIcon,
  CurrencyDollarIcon,
  EyeIcon,
} from '@heroicons/react/24/outline'
import PremiumCard, { PremiumCardHeader, PremiumCardTitle, PremiumCardContent } from '../../components/ui/PremiumCard'
import PremiumButton from '../../components/ui/PremiumButton'
import PremiumInput from '../../components/ui/PremiumInput'

const PaymentsPage = () => {
  const [payments, setPayments] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterMethod, setFilterMethod] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const paymentsPerPage = 10

  // Mock data
  useEffect(() => {
    const mockPayments = [
      {
        id: 1,
        transactionId: 'TXN001234567',
        customer: 'John Doe',
        workBoy: 'Rajesh Kumar',
        taskTitle: 'Fix Kitchen Sink Leak',
        amount: 2500,
        method: 'UPI',
        status: 'completed',
        date: '2024-03-15T10:30:00Z',
        gateway: 'Razorpay',
        gatewayFee: 62.5,
        netAmount: 2437.5
      },
      {
        id: 2,
        transactionId: 'TXN001234568',
        customer: 'Jane Smith',
        workBoy: 'Amit Sharma',
        taskTitle: 'Electrical Wiring Installation',
        amount: 8500,
        method: 'Credit Card',
        status: 'pending',
        date: '2024-03-14T15:45:00Z',
        gateway: 'Stripe',
        gatewayFee: 255,
        netAmount: 8245
      },
      {
        id: 3,
        transactionId: 'TXN001234569',
        customer: 'Mike Johnson',
        workBoy: 'Suresh Patel',
        taskTitle: 'Deep House Cleaning',
        amount: 3500,
        method: 'Debit Card',
        status: 'completed',
        date: '2024-03-13T09:15:00Z',
        gateway: 'Razorpay',
        gatewayFee: 87.5,
        netAmount: 3412.5
      },
      {
        id: 4,
        transactionId: 'TXN001234570',
        customer: 'Sarah Wilson',
        workBoy: 'Vikram Singh',
        taskTitle: 'Wooden Furniture Repair',
        amount: 4200,
        method: 'Net Banking',
        status: 'failed',
        date: '2024-03-12T14:20:00Z',
        gateway: 'Paytm',
        gatewayFee: 0,
        netAmount: 0
      },
      {
        id: 5,
        transactionId: 'TXN001234571',
        customer: 'David Brown',
        workBoy: 'Manoj Gupta',
        taskTitle: 'Wall Painting Service',
        amount: 6800,
        method: 'Wallet',
        status: 'refunded',
        date: '2024-03-11T11:30:00Z',
        gateway: 'PhonePe',
        gatewayFee: 170,
        netAmount: 6630
      }
    ]

    setTimeout(() => {
      setPayments(mockPayments)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.transactionId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.customer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.taskTitle.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || payment.status === filterStatus
    const matchesMethod = filterMethod === 'all' || payment.method === filterMethod
    return matchesSearch && matchesStatus && matchesMethod
  })

  const totalPages = Math.ceil(filteredPayments.length / paymentsPerPage)
  const startIndex = (currentPage - 1) * paymentsPerPage
  const paginatedPayments = filteredPayments.slice(startIndex, startIndex + paymentsPerPage)

  const getStatusBadge = (status) => {
    const statusConfig = {
      completed: { bg: 'bg-success-100', text: 'text-success-800', icon: CheckCircleIcon },
      pending: { bg: 'bg-warning-100', text: 'text-warning-800', icon: ClockIcon },
      failed: { bg: 'bg-danger-100', text: 'text-danger-800', icon: XCircleIcon },
      refunded: { bg: 'bg-gray-100', text: 'text-gray-800', icon: ExclamationTriangleIcon }
    }

    const config = statusConfig[status] || statusConfig.pending
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-bold ${config.bg} ${config.text}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    )
  }

  const getMethodIcon = (method) => {
    const methodIcons = {
      'UPI': '📱',
      'Credit Card': '💳',
      'Debit Card': '💳',
      'Net Banking': '🏦',
      'Wallet': '👛'
    }

    return methodIcons[method] || '💳'
  }

  if (loading) {
    return (
      <div className="space-y-6 animate-fade-in-up">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 animate-fade-in-up">
      <Helmet>
        <title>Payments Management - WorkBoy Admin</title>
      </Helmet>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-black bg-gradient-to-r from-gray-900 via-primary-800 to-brand-700 bg-clip-text text-transparent">
            Payments Management
          </h1>
          <p className="text-gray-600 mt-2 font-medium">Monitor and manage all payment transactions</p>
        </div>
        <div className="flex space-x-3">
          <PremiumButton variant="outline" icon={ArrowDownTrayIcon}>
            Export
          </PremiumButton>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 shadow-lg shadow-primary-500/25">
              <CreditCardIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Total Payments</p>
              <p className="text-2xl font-black text-gray-900">{payments.length}</p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-success-500 to-success-600 shadow-lg shadow-success-500/25">
              <CheckCircleIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Completed</p>
              <p className="text-2xl font-black text-gray-900">
                {payments.filter(p => p.status === 'completed').length}
              </p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-warning-500 to-warning-600 shadow-lg shadow-warning-500/25">
              <ClockIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Pending</p>
              <p className="text-2xl font-black text-gray-900">
                {payments.filter(p => p.status === 'pending').length}
              </p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-accent-500 to-accent-600 shadow-lg shadow-accent-500/25">
              <CurrencyDollarIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Total Value</p>
              <p className="text-2xl font-black text-gray-900">
                ₹{payments.filter(p => p.status === 'completed').reduce((sum, p) => sum + p.amount, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </PremiumCard>
      </div>

      {/* Filters and Search */}
      <PremiumCard gradient>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1 max-w-md">
            <PremiumInput
              placeholder="Search by transaction ID, customer, or task..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={MagnifyingGlassIcon}
              variant="premium"
            />
          </div>

          <div className="flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-xl text-sm font-medium bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-300"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="refunded">Refunded</option>
            </select>

            <select
              value={filterMethod}
              onChange={(e) => setFilterMethod(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-xl text-sm font-medium bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-300"
            >
              <option value="all">All Methods</option>
              <option value="UPI">UPI</option>
              <option value="Credit Card">Credit Card</option>
              <option value="Debit Card">Debit Card</option>
              <option value="Net Banking">Net Banking</option>
              <option value="Wallet">Wallet</option>
            </select>

            <PremiumButton variant="outline" icon={FunnelIcon} size="sm">
              More Filters
            </PremiumButton>
          </div>
        </div>
      </PremiumCard>

      {/* Payments Table */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={CreditCardIcon}>
            Payments List ({filteredPayments.length} payments)
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Transaction
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Customer & Task
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Gateway
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedPayments.map((payment) => (
                  <tr key={payment.id} className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-2xl mr-3">{getMethodIcon(payment.method)}</div>
                        <div>
                          <div className="text-sm font-bold text-gray-900">{payment.transactionId}</div>
                          <div className="text-xs text-gray-500">ID: {payment.id}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        <div className="text-sm text-gray-900 flex items-center">
                          <UserIcon className="h-3 w-3 mr-2 text-gray-400" />
                          {payment.customer}
                        </div>
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {payment.taskTitle}
                        </div>
                        <div className="text-xs text-gray-400">
                          Work-Boy: {payment.workBoy}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="font-bold text-lg">₹{payment.amount.toLocaleString()}</div>
                        {payment.status === 'completed' && (
                          <div className="text-xs text-gray-500">
                            Fee: ₹{payment.gatewayFee} | Net: ₹{payment.netAmount.toLocaleString()}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">{payment.method}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{payment.gateway}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 flex items-center">
                        <CalendarDaysIcon className="h-3 w-3 mr-2 text-gray-400" />
                        <div>
                          <div>{new Date(payment.date).toLocaleDateString()}</div>
                          <div className="text-xs text-gray-500">
                            {new Date(payment.date).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(payment.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/payments/${payment.id}`}
                          className="text-primary-600 hover:text-primary-900 p-1 rounded-lg hover:bg-primary-50 transition-colors duration-200"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </PremiumCardContent>
      </PremiumCard>
    </div>
  )
}

export default PaymentsPage
