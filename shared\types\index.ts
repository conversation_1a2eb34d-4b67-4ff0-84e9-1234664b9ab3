// Work-Boy Booking Platform - Shared Types

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[] | Record<string, string[]>;
  meta?: PaginationMeta;
}

export interface PaginationMeta {
  current_page: number;
  per_page: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

// User Types
export type UserType = 'customer' | 'workboy' | 'admin';
export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending_verification';

export interface User {
  id: number;
  firebase_uid: string;
  email: string;
  phone?: string;
  first_name: string;
  last_name: string;
  profile_image?: string;
  user_type: UserType;
  status: UserStatus;
  email_verified: boolean;
  phone_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface CustomerProfile {
  id: number;
  user_id: number;
  preferred_payment_method: 'card' | 'wallet' | 'upi';
  default_address_id?: number;
  notification_preferences: NotificationPreferences;
  created_at: string;
  updated_at: string;
}

export interface WorkBoyProfile {
  id: number;
  user_id: number;
  kyc_status: 'pending' | 'approved' | 'rejected';
  kyc_documents: KYCDocuments;
  skills: string[];
  hourly_rate?: number;
  availability_status: 'available' | 'busy' | 'offline';
  rating: number;
  total_tasks_completed: number;
  total_earnings: number;
  bank_details?: BankDetails;
  created_at: string;
  updated_at: string;
}

export interface KYCDocuments {
  id_proof?: string;
  address_proof?: string;
  photo?: string;
}

export interface BankDetails {
  account_number: string;
  ifsc_code: string;
  account_holder_name: string;
}

export interface NotificationPreferences {
  email_notifications: boolean;
  push_notifications: boolean;
  sms_notifications: boolean;
  task_updates: boolean;
  promotional: boolean;
}

// Address Types
export interface Address {
  id: number;
  user_id: number;
  label?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  latitude?: number;
  longitude?: number;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

// Task Types
export type TaskStatus = 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
export type TaskPriority = 'low' | 'medium' | 'high';

export interface TaskCategory {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  base_price: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Task {
  id: number;
  customer_id: number;
  workboy_id?: number;
  category_id?: number;
  title: string;
  description: string;
  task_images: string[];
  address_id: number;
  scheduled_date: string;
  scheduled_time: string;
  estimated_duration?: number;
  status: TaskStatus;
  priority: TaskPriority;
  total_amount?: number;
  tip_amount: number;
  created_at: string;
  updated_at: string;
  
  // Relations
  customer?: User;
  workboy?: User;
  category?: TaskCategory;
  address?: Address;
  status_history?: TaskStatusHistory[];
  payment?: Payment;
  review?: Review;
}

export interface TaskStatusHistory {
  id: number;
  task_id: number;
  status: TaskStatus;
  notes?: string;
  location_lat?: number;
  location_lng?: number;
  created_by?: number;
  created_at: string;
}

// Payment Types
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded';

export interface Payment {
  id: number;
  task_id: number;
  customer_id: number;
  workboy_id?: number;
  amount: number;
  tip_amount: number;
  payment_method?: string;
  payment_gateway_id?: string;
  payment_status: PaymentStatus;
  gateway_response?: any;
  created_at: string;
  updated_at: string;
}

// Review Types
export interface Review {
  id: number;
  task_id: number;
  customer_id: number;
  workboy_id: number;
  rating: number;
  review_text?: string;
  created_at: string;
  
  // Relations
  customer?: User;
  task?: Task;
}

// Notification Types
export type NotificationType = 'task_update' | 'payment' | 'system' | 'promotion';

export interface Notification {
  id: number;
  user_id: number;
  title: string;
  message: string;
  type: NotificationType;
  related_id?: number;
  is_read: boolean;
  created_at: string;
}

// API Request Types
export interface RegisterRequest {
  firebase_uid: string;
  email: string;
  phone?: string;
  first_name: string;
  last_name: string;
  user_type: UserType;
}

export interface CreateTaskRequest {
  category_id?: number;
  title: string;
  description: string;
  task_images?: string[];
  address_id: number;
  scheduled_date: string;
  scheduled_time: string;
  estimated_duration?: number;
}

export interface CreateAddressRequest {
  label?: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  latitude?: number;
  longitude?: number;
  is_default?: boolean;
}

export interface UpdateTaskStatusRequest {
  status: TaskStatus;
  notes?: string;
  location_lat?: number;
  location_lng?: number;
}

export interface CreateReviewRequest {
  task_id: number;
  workboy_id: number;
  rating: number;
  review_text?: string;
}

export interface CreatePaymentRequest {
  task_id: number;
  amount: number;
  tip_amount?: number;
}

// Dashboard Types
export interface CustomerDashboard {
  recent_tasks: Task[];
  task_stats: {
    pending: number;
    in_progress: number;
    completed: number;
    total_spent: number;
  };
  recommended_workboys: User[];
}

export interface WorkBoyDashboard {
  available_tasks: Task[];
  earnings_summary: {
    today: number;
    this_week: number;
    this_month: number;
    total: number;
  };
  performance_stats: {
    rating: number;
    total_tasks: number;
    completion_rate: number;
  };
}

export interface AdminDashboard {
  user_stats: {
    total_customers: number;
    total_workboys: number;
    active_users: number;
    pending_kyc: number;
  };
  task_stats: {
    total_tasks: number;
    active_tasks: number;
    completed_today: number;
    revenue_today: number;
  };
  recent_activities: any[];
}

// Location Types
export interface Location {
  latitude: number;
  longitude: number;
}

export interface LocationUpdate {
  task_id: number;
  location: Location;
  timestamp: string;
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export interface TaskUpdateMessage extends WebSocketMessage {
  type: 'task_update';
  data: {
    task_id: number;
    status: TaskStatus;
    message: string;
  };
}

export interface LocationUpdateMessage extends WebSocketMessage {
  type: 'location_update';
  data: LocationUpdate;
}
