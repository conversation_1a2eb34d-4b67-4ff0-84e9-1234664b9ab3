import React from 'react'
import { clsx } from 'clsx'

export const PremiumButton = ({
  children,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  iconPosition = 'left',
  loading = false,
  disabled = false,
  className = '',
  glow = false,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-bold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden'
  
  const variants = {
    primary: 'bg-gradient-to-r from-primary-600 to-brand-600 text-white hover:from-primary-700 hover:to-brand-700 focus:ring-primary-500 shadow-lg shadow-primary-500/30 hover:shadow-primary-500/50 hover:scale-105',
    secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus:ring-gray-500 shadow-md hover:shadow-lg hover:scale-105',
    success: 'bg-gradient-to-r from-success-600 to-success-700 text-white hover:from-success-700 hover:to-success-800 focus:ring-success-500 shadow-lg shadow-success-500/30 hover:shadow-success-500/50 hover:scale-105',
    warning: 'bg-gradient-to-r from-warning-600 to-warning-700 text-white hover:from-warning-700 hover:to-warning-800 focus:ring-warning-500 shadow-lg shadow-warning-500/30 hover:shadow-warning-500/50 hover:scale-105',
    danger: 'bg-gradient-to-r from-danger-600 to-danger-700 text-white hover:from-danger-700 hover:to-danger-800 focus:ring-danger-500 shadow-lg shadow-danger-500/30 hover:shadow-danger-500/50 hover:scale-105',
    outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gradient-to-r hover:from-primary-50 hover:to-brand-50/30 hover:border-primary-300 hover:text-primary-700 focus:ring-primary-500 hover:scale-105',
    ghost: 'text-gray-700 hover:bg-gradient-to-r hover:from-primary-50 hover:to-brand-50/30 hover:text-primary-700 focus:ring-primary-500 hover:scale-105'
  }
  
  const sizes = {
    xs: 'px-3 py-1.5 text-xs rounded-lg',
    sm: 'px-4 py-2 text-sm rounded-xl',
    md: 'px-6 py-3 text-sm rounded-xl',
    lg: 'px-8 py-4 text-base rounded-2xl',
    xl: 'px-10 py-5 text-lg rounded-2xl'
  }
  
  const glowClasses = glow ? 'animate-glow' : ''
  
  return (
    <button
      className={clsx(
        baseClasses,
        variants[variant],
        sizes[size],
        glowClasses,
        loading && 'cursor-wait',
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 bg-white/20 flex items-center justify-center">
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      
      {Icon && iconPosition === 'left' && !loading && (
        <Icon className={clsx('flex-shrink-0', size === 'xs' ? 'w-3 h-3' : size === 'sm' ? 'w-4 h-4' : 'w-5 h-5', children ? 'mr-2' : '')} />
      )}
      
      {children}
      
      {Icon && iconPosition === 'right' && !loading && (
        <Icon className={clsx('flex-shrink-0', size === 'xs' ? 'w-3 h-3' : size === 'sm' ? 'w-4 h-4' : 'w-5 h-5', children ? 'ml-2' : '')} />
      )}
      
      {/* Shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
    </button>
  )
}

export default PremiumButton
