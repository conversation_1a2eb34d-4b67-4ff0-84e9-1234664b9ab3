import React, { useState, useEffect } from 'react'
import { Link, useSearchParams } from 'react-router-dom'
import { 
  Plus, 
  Search, 
  Filter, 
  Calendar,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Eye
} from 'lucide-react'
import DashboardLayout from '../../components/layout/DashboardLayout'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import { apiHelpers, endpoints } from '../../config/api'
import { format } from 'date-fns'

const TasksPage = () => {
  const [tasks, setTasks] = useState([])
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [searchParams, setSearchParams] = useSearchParams()

  useEffect(() => {
    // Get initial filters from URL params
    const status = searchParams.get('status')
    const category = searchParams.get('category')
    const search = searchParams.get('search')

    if (status) setSelectedStatus(status)
    if (category) setSelectedCategory(category)
    if (search) setSearchTerm(search)

    fetchTasks()
    fetchCategories()
  }, [searchParams])

  const fetchTasks = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      if (selectedStatus) params.append('status', selectedStatus)
      if (selectedCategory) params.append('category_id', selectedCategory)
      if (searchTerm) params.append('search', searchTerm)

      const response = await apiHelpers.get(`${endpoints.tasks.list}?${params.toString()}`)
      
      if (response.success) {
        setTasks(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await apiHelpers.get(endpoints.tasks.categories)
      
      if (response.success) {
        setCategories(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error)
    }
  }

  const handleSearch = (e) => {
    e.preventDefault()
    updateFilters()
  }

  const updateFilters = () => {
    const params = new URLSearchParams()
    
    if (selectedStatus) params.append('status', selectedStatus)
    if (selectedCategory) params.append('category_id', selectedCategory)
    if (searchTerm) params.append('search', searchTerm)

    setSearchParams(params)
    fetchTasks()
  }

  const clearFilters = () => {
    setSearchTerm('')
    setSelectedStatus('')
    setSelectedCategory('')
    setSearchParams({})
    fetchTasks()
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'text-warning-700 bg-warning-100 border-warning-200'
      case 'assigned':
        return 'text-blue-700 bg-blue-100 border-blue-200'
      case 'in_progress':
        return 'text-primary-700 bg-primary-100 border-primary-200'
      case 'completed':
        return 'text-success-700 bg-success-100 border-success-200'
      case 'cancelled':
        return 'text-error-700 bg-error-100 border-error-200'
      default:
        return 'text-gray-700 bg-gray-100 border-gray-200'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return Clock
      case 'assigned':
      case 'in_progress':
        return AlertCircle
      case 'completed':
        return CheckCircle
      case 'cancelled':
        return XCircle
      default:
        return Clock
    }
  }

  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'assigned', label: 'Assigned' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Tasks</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage and track all your service requests
            </p>
          </div>
          <Link
            to="/tasks/create"
            className="mt-4 sm:mt-0 btn btn-primary btn-md"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Task
          </Link>
        </div>

        {/* Filters */}
        <div className="card p-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="md:col-span-2">
                <label htmlFor="search" className="form-label">
                  Search tasks
                </label>
                <div className="relative">
                  <input
                    id="search"
                    type="text"
                    className="input pl-10"
                    placeholder="Search by title or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                </div>
              </div>

              {/* Status Filter */}
              <div>
                <label htmlFor="status" className="form-label">
                  Status
                </label>
                <select
                  id="status"
                  className="input"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Category Filter */}
              <div>
                <label htmlFor="category" className="form-label">
                  Category
                </label>
                <select
                  id="category"
                  className="input"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                type="submit"
                className="btn btn-primary btn-sm"
              >
                <Filter className="w-4 h-4 mr-2" />
                Apply Filters
              </button>
              <button
                type="button"
                onClick={clearFilters}
                className="btn btn-outline btn-sm"
              >
                Clear Filters
              </button>
            </div>
          </form>
        </div>

        {/* Tasks List */}
        <div className="card">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : tasks.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {tasks.map((task) => {
                const StatusIcon = getStatusIcon(task.status)
                return (
                  <div key={task.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center border ${getStatusColor(task.status)}`}>
                            <StatusIcon className="w-5 h-5" />
                          </div>
                          <div className="flex-1">
                            <h3 className="text-lg font-medium text-gray-900">
                              {task.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">
                              {task.description}
                            </p>
                            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                              {task.category_name && (
                                <span className="badge badge-secondary">
                                  {task.category_name}
                                </span>
                              )}
                              <div className="flex items-center">
                                <Calendar className="w-4 h-4 mr-1" />
                                {format(new Date(task.scheduled_date), 'MMM dd, yyyy')}
                              </div>
                              <div className="flex items-center">
                                <Clock className="w-4 h-4 mr-1" />
                                {task.scheduled_time}
                              </div>
                              {task.city && (
                                <div className="flex items-center">
                                  <MapPin className="w-4 h-4 mr-1" />
                                  {task.city}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <span className={`badge border ${getStatusColor(task.status)}`}>
                          {task.status.replace('_', ' ')}
                        </span>
                        <Link
                          to={`/tasks/${task.id}`}
                          className="btn btn-outline btn-sm"
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          View
                        </Link>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ClipboardList className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No tasks found
              </h3>
              <p className="text-gray-500 mb-6">
                {searchTerm || selectedStatus || selectedCategory
                  ? 'Try adjusting your filters or search terms.'
                  : 'Get started by creating your first task.'}
              </p>
              <Link
                to="/tasks/create"
                className="btn btn-primary btn-md"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Task
              </Link>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}

export default TasksPage
