<?php

namespace App\Models;

use CodeIgniter\Model;

class PaymentModel extends Model
{
    protected $table            = 'payments';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'task_id',
        'customer_id',
        'workboy_id',
        'amount',
        'tip_amount',
        'payment_method',
        'payment_gateway_id',
        'payment_status',
        'gateway_response'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'task_id'            => 'required|integer',
        'customer_id'        => 'required|integer',
        'workboy_id'         => 'permit_empty|integer',
        'amount'             => 'required|decimal|greater_than[0]',
        'tip_amount'         => 'permit_empty|decimal|greater_than_equal_to[0]',
        'payment_method'     => 'permit_empty|max_length[50]',
        'payment_gateway_id' => 'permit_empty|max_length[255]',
        'payment_status'     => 'permit_empty|in_list[pending,completed,failed,refunded]',
        'gateway_response'   => 'permit_empty|valid_json',
    ];

    protected $validationMessages = [
        'task_id' => [
            'required' => 'Task ID is required',
        ],
        'customer_id' => [
            'required' => 'Customer ID is required',
        ],
        'amount' => [
            'required'     => 'Payment amount is required',
            'greater_than' => 'Payment amount must be greater than 0',
        ],
        'payment_status' => [
            'in_list' => 'Invalid payment status',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['encodeJsonFields'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['encodeJsonFields'];
    protected $afterUpdate    = ['updateWorkBoyEarnings'];
    protected $beforeFind     = [];
    protected $afterFind      = ['decodeJsonFields'];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Encode JSON fields before saving
     */
    protected function encodeJsonFields(array $data)
    {
        if (isset($data['data']['gateway_response']) && is_array($data['data']['gateway_response'])) {
            $data['data']['gateway_response'] = json_encode($data['data']['gateway_response']);
        }

        return $data;
    }

    /**
     * Decode JSON fields after retrieval
     */
    protected function decodeJsonFields(array $data)
    {
        if (isset($data['data'])) {
            // Handle single record
            if (isset($data['data']['gateway_response'])) {
                $data['data']['gateway_response'] = json_decode($data['data']['gateway_response'], true);
            }
        } else {
            // Handle multiple records
            foreach ($data as &$record) {
                if (isset($record['gateway_response'])) {
                    $record['gateway_response'] = json_decode($record['gateway_response'], true);
                }
            }
        }

        return $data;
    }

    /**
     * Update Work-Boy earnings after payment completion
     */
    protected function updateWorkBoyEarnings(array $data)
    {
        if (isset($data['id']) && isset($data['data']['payment_status']) && $data['data']['payment_status'] === 'completed') {
            $paymentId = is_array($data['id']) ? $data['id'][0] : $data['id'];
            $payment = $this->find($paymentId);
            
            if ($payment && $payment['workboy_id']) {
                $workboyProfileModel = new WorkboyProfileModel();
                $totalAmount = $payment['amount'] + $payment['tip_amount'];
                $workboyProfileModel->addEarnings($payment['workboy_id'], $totalAmount);
            }
        }

        return $data;
    }

    /**
     * Get payment by task ID
     */
    public function getByTaskId(int $taskId)
    {
        return $this->where('task_id', $taskId)->first();
    }

    /**
     * Get payments by customer ID
     */
    public function getByCustomerId(int $customerId, int $limit = 20, int $offset = 0)
    {
        return $this->where('customer_id', $customerId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get payments by Work-Boy ID
     */
    public function getByWorkBoyId(int $workboyId, int $limit = 20, int $offset = 0)
    {
        return $this->where('workboy_id', $workboyId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get payment with task and user details
     */
    public function getPaymentWithDetails(int $paymentId)
    {
        return $this->select('payments.*,
                             tasks.title as task_title, tasks.status as task_status,
                             customers.first_name as customer_first_name, customers.last_name as customer_last_name,
                             workboys.first_name as workboy_first_name, workboys.last_name as workboy_last_name')
                   ->join('tasks', 'tasks.id = payments.task_id')
                   ->join('users customers', 'customers.id = payments.customer_id')
                   ->join('users workboys', 'workboys.id = payments.workboy_id', 'left')
                   ->where('payments.id', $paymentId)
                   ->first();
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus(int $paymentId, string $status, array $gatewayResponse = null)
    {
        $updateData = ['payment_status' => $status];
        
        if ($gatewayResponse) {
            $updateData['gateway_response'] = json_encode($gatewayResponse);
        }

        return $this->update($paymentId, $updateData);
    }

    /**
     * Get payment statistics
     */
    public function getPaymentStats(array $filters = [])
    {
        $builder = $this->builder();
        
        // Apply filters
        if (isset($filters['customer_id'])) {
            $builder->where('customer_id', $filters['customer_id']);
        }
        
        if (isset($filters['workboy_id'])) {
            $builder->where('workboy_id', $filters['workboy_id']);
        }
        
        if (isset($filters['date_from'])) {
            $builder->where('created_at >=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $builder->where('created_at <=', $filters['date_to']);
        }

        return $builder->select('
                            COUNT(*) as total_payments,
                            SUM(CASE WHEN payment_status = "completed" THEN 1 ELSE 0 END) as completed_payments,
                            SUM(CASE WHEN payment_status = "pending" THEN 1 ELSE 0 END) as pending_payments,
                            SUM(CASE WHEN payment_status = "failed" THEN 1 ELSE 0 END) as failed_payments,
                            SUM(CASE WHEN payment_status = "refunded" THEN 1 ELSE 0 END) as refunded_payments,
                            SUM(CASE WHEN payment_status = "completed" THEN amount + tip_amount ELSE 0 END) as total_revenue,
                            SUM(CASE WHEN payment_status = "completed" THEN tip_amount ELSE 0 END) as total_tips,
                            AVG(CASE WHEN payment_status = "completed" THEN amount + tip_amount END) as avg_payment_amount
                        ')
                      ->get()
                      ->getRowArray();
    }

    /**
     * Get daily revenue
     */
    public function getDailyRevenue(int $days = 30)
    {
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        
        return $this->select('DATE(created_at) as date, 
                             SUM(CASE WHEN payment_status = "completed" THEN amount + tip_amount ELSE 0 END) as revenue,
                             COUNT(CASE WHEN payment_status = "completed" THEN 1 END) as completed_payments')
                   ->where('created_at >=', $startDate)
                   ->groupBy('DATE(created_at)')
                   ->orderBy('date', 'ASC')
                   ->findAll();
    }

    /**
     * Get payment method distribution
     */
    public function getPaymentMethodStats()
    {
        return $this->select('payment_method, COUNT(*) as count, 
                             SUM(CASE WHEN payment_status = "completed" THEN amount + tip_amount ELSE 0 END) as revenue')
                   ->where('payment_method IS NOT NULL')
                   ->groupBy('payment_method')
                   ->orderBy('count', 'DESC')
                   ->findAll();
    }

    /**
     * Get pending payments
     */
    public function getPendingPayments(int $limit = 20, int $offset = 0)
    {
        return $this->select('payments.*, tasks.title as task_title,
                             customers.first_name as customer_first_name, customers.last_name as customer_last_name')
                   ->join('tasks', 'tasks.id = payments.task_id')
                   ->join('users customers', 'customers.id = payments.customer_id')
                   ->where('payments.payment_status', 'pending')
                   ->orderBy('payments.created_at', 'ASC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get failed payments
     */
    public function getFailedPayments(int $limit = 20, int $offset = 0)
    {
        return $this->select('payments.*, tasks.title as task_title,
                             customers.first_name as customer_first_name, customers.last_name as customer_last_name')
                   ->join('tasks', 'tasks.id = payments.task_id')
                   ->join('users customers', 'customers.id = payments.customer_id')
                   ->where('payments.payment_status', 'failed')
                   ->orderBy('payments.created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get Work-Boy earnings summary
     */
    public function getWorkBoyEarnings(int $workboyId, string $period = 'all')
    {
        $builder = $this->builder();
        $builder->where('workboy_id', $workboyId)
                ->where('payment_status', 'completed');

        switch ($period) {
            case 'today':
                $builder->where('DATE(created_at)', date('Y-m-d'));
                break;
            case 'week':
                $builder->where('created_at >=', date('Y-m-d', strtotime('-7 days')));
                break;
            case 'month':
                $builder->where('created_at >=', date('Y-m-d', strtotime('-30 days')));
                break;
        }

        return $builder->select('
                            COUNT(*) as total_payments,
                            SUM(amount + tip_amount) as total_earnings,
                            SUM(amount) as base_earnings,
                            SUM(tip_amount) as total_tips,
                            AVG(amount + tip_amount) as avg_earning_per_task
                        ')
                      ->get()
                      ->getRowArray();
    }

    /**
     * Process refund
     */
    public function processRefund(int $paymentId, string $reason = null)
    {
        $payment = $this->find($paymentId);
        
        if (!$payment || $payment['payment_status'] !== 'completed') {
            return [
                'success' => false,
                'message' => 'Payment not found or not eligible for refund'
            ];
        }

        // Update payment status to refunded
        $gatewayResponse = $payment['gateway_response'] ?? [];
        $gatewayResponse['refund_reason'] = $reason;
        $gatewayResponse['refund_date'] = date('Y-m-d H:i:s');

        $updated = $this->update($paymentId, [
            'payment_status' => 'refunded',
            'gateway_response' => json_encode($gatewayResponse)
        ]);

        if ($updated && $payment['workboy_id']) {
            // Deduct from Work-Boy earnings
            $workboyProfileModel = new WorkboyProfileModel();
            $totalAmount = $payment['amount'] + $payment['tip_amount'];
            $workboyProfileModel->addEarnings($payment['workboy_id'], -$totalAmount);
        }

        return [
            'success' => $updated,
            'message' => $updated ? 'Refund processed successfully' : 'Failed to process refund'
        ];
    }
}
