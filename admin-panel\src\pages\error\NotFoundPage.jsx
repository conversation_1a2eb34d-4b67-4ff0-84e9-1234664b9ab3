import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { He<PERSON><PERSON> } from 'react-helmet-async'
import { HomeIcon } from '@heroicons/react/24/outline'
import { But<PERSON> } from '@components/ui/Button'

const NotFoundPage = () => {
  return (
    <div className="min-h-screen bg-white px-4 py-16 sm:px-6 sm:py-24 md:grid md:place-items-center lg:px-8">
      <Helmet>
        <title>Page Not Found - WorkBoy Admin</title>
      </Helmet>
      
      <div className="mx-auto max-w-max">
        <main className="sm:flex">
          <p className="text-4xl font-bold tracking-tight text-primary-600 sm:text-5xl">404</p>
          <div className="sm:ml-6">
            <div className="sm:border-l sm:border-gray-200 sm:pl-6">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                Page not found
              </h1>
              <p className="mt-1 text-base text-gray-500">
                Please check the URL in the address bar and try again.
              </p>
            </div>
            <div className="mt-10 flex space-x-3 sm:border-l sm:border-transparent sm:pl-6">
              <Link to="/dashboard">
                <Button className="inline-flex items-center">
                  <HomeIcon className="mr-2 h-4 w-4" />
                  Go back home
                </Button>
              </Link>
              <Button
                variant="outline"
                onClick={() => window.history.back()}
              >
                Go back
              </Button>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

export default NotFoundPage
