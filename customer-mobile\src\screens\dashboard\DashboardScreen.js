import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  StatusBar,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  IconButton,
  Avatar,
  Chip,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '@context/AuthContext';
import { apiService, endpoints } from '@services/apiService';
import { colors, spacing, typography, borderRadius } from '@constants/theme';
import { showToast } from '@utils/toast';

const { width: screenWidth } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const { userProfile } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await apiService.get(endpoints.customer.dashboard);
      if (response.success) {
        setDashboardData(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      showToast('Failed to load dashboard data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return colors.warning[500];
      case 'assigned':
        return colors.primary[500];
      case 'in_progress':
        return colors.blue[500];
      case 'completed':
        return colors.success[500];
      case 'cancelled':
        return colors.error[500];
      default:
        return colors.gray[500];
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return 'time-outline';
      case 'assigned':
      case 'in_progress':
        return 'play-circle-outline';
      case 'completed':
        return 'checkmark-circle-outline';
      case 'cancelled':
        return 'close-circle-outline';
      default:
        return 'time-outline';
    }
  };

  const stats = dashboardData?.task_stats || {};
  const recentTasks = dashboardData?.recent_tasks || [];
  const upcomingTasks = dashboardData?.upcoming_tasks || [];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary[600]} />
      
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <LinearGradient
          colors={[colors.primary[600], colors.primary[700]]}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View style={styles.headerLeft}>
              <Avatar.Text
                size={48}
                label={userProfile?.first_name?.charAt(0) || 'U'}
                style={styles.avatar}
                labelStyle={styles.avatarLabel}
              />
              <View style={styles.headerText}>
                <Text style={styles.greeting}>Good morning</Text>
                <Text style={styles.userName}>
                  {userProfile?.first_name || 'User'}
                </Text>
              </View>
            </View>
            
            <View style={styles.headerRight}>
              <IconButton
                icon="notifications-outline"
                size={24}
                iconColor={colors.white}
                onPress={() => navigation.navigate('Notifications')}
                style={styles.notificationButton}
              />
            </View>
          </View>

          {/* Quick Book Button */}
          <Button
            mode="contained"
            onPress={() => navigation.navigate('Book')}
            style={styles.quickBookButton}
            contentStyle={styles.quickBookButtonContent}
            labelStyle={styles.quickBookButtonLabel}
            icon="plus"
          >
            Book a Service
          </Button>
        </LinearGradient>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            <Card style={[styles.statCard, { backgroundColor: colors.warning[50] }]}>
              <Card.Content style={styles.statCardContent}>
                <View style={[styles.statIcon, { backgroundColor: colors.warning[100] }]}>
                  <Ionicons name="time-outline" size={24} color={colors.warning[600]} />
                </View>
                <Text style={styles.statNumber}>{stats.pending_tasks || 0}</Text>
                <Text style={styles.statLabel}>Pending</Text>
              </Card.Content>
            </Card>

            <Card style={[styles.statCard, { backgroundColor: colors.primary[50] }]}>
              <Card.Content style={styles.statCardContent}>
                <View style={[styles.statIcon, { backgroundColor: colors.primary[100] }]}>
                  <Ionicons name="play-circle-outline" size={24} color={colors.primary[600]} />
                </View>
                <Text style={styles.statNumber}>{stats.in_progress_tasks || 0}</Text>
                <Text style={styles.statLabel}>In Progress</Text>
              </Card.Content>
            </Card>
          </View>

          <View style={styles.statsRow}>
            <Card style={[styles.statCard, { backgroundColor: colors.success[50] }]}>
              <Card.Content style={styles.statCardContent}>
                <View style={[styles.statIcon, { backgroundColor: colors.success[100] }]}>
                  <Ionicons name="checkmark-circle-outline" size={24} color={colors.success[600]} />
                </View>
                <Text style={styles.statNumber}>{stats.completed_tasks || 0}</Text>
                <Text style={styles.statLabel}>Completed</Text>
              </Card.Content>
            </Card>

            <Card style={[styles.statCard, { backgroundColor: colors.gray[50] }]}>
              <Card.Content style={styles.statCardContent}>
                <View style={[styles.statIcon, { backgroundColor: colors.gray[100] }]}>
                  <Ionicons name="trending-up-outline" size={24} color={colors.gray[600]} />
                </View>
                <Text style={styles.statNumber}>₹{dashboardData?.payment_stats?.total_spent || 0}</Text>
                <Text style={styles.statLabel}>Total Spent</Text>
              </Card.Content>
            </Card>
          </View>
        </View>

        {/* Recent Tasks */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Tasks</Text>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Tasks')}
              labelStyle={styles.sectionLink}
            >
              View All
            </Button>
          </View>

          {recentTasks.length > 0 ? (
            recentTasks.map((task) => (
              <Card key={task.id} style={styles.taskCard}>
                <Card.Content style={styles.taskCardContent}>
                  <View style={styles.taskHeader}>
                    <View style={styles.taskInfo}>
                      <Text style={styles.taskTitle} numberOfLines={1}>
                        {task.title}
                      </Text>
                      <Text style={styles.taskCategory}>
                        {task.category_name}
                      </Text>
                    </View>
                    <Chip
                      mode="outlined"
                      style={[
                        styles.statusChip,
                        { borderColor: getStatusColor(task.status) }
                      ]}
                      textStyle={[
                        styles.statusChipText,
                        { color: getStatusColor(task.status) }
                      ]}
                      icon={() => (
                        <Ionicons
                          name={getStatusIcon(task.status)}
                          size={16}
                          color={getStatusColor(task.status)}
                        />
                      )}
                    >
                      {task.status.replace('_', ' ')}
                    </Chip>
                  </View>
                </Card.Content>
              </Card>
            ))
          ) : (
            <Card style={styles.emptyCard}>
              <Card.Content style={styles.emptyCardContent}>
                <Ionicons name="clipboard-outline" size={48} color={colors.gray[400]} />
                <Text style={styles.emptyText}>No recent tasks</Text>
                <Button
                  mode="contained"
                  onPress={() => navigation.navigate('Book')}
                  style={styles.emptyButton}
                  labelStyle={styles.emptyButtonLabel}
                >
                  Book Your First Service
                </Button>
              </Card.Content>
            </Card>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.quickActionsGrid}>
            <Card style={styles.quickActionCard}>
              <Card.Content style={styles.quickActionContent}>
                <View style={[styles.quickActionIcon, { backgroundColor: colors.primary[100] }]}>
                  <Ionicons name="add-circle-outline" size={32} color={colors.primary[600]} />
                </View>
                <Text style={styles.quickActionTitle}>Book Service</Text>
                <Text style={styles.quickActionSubtitle}>Create new task</Text>
              </Card.Content>
            </Card>

            <Card style={styles.quickActionCard}>
              <Card.Content style={styles.quickActionContent}>
                <View style={[styles.quickActionIcon, { backgroundColor: colors.blue[100] }]}>
                  <Ionicons name="location-outline" size={32} color={colors.blue[600]} />
                </View>
                <Text style={styles.quickActionTitle}>Addresses</Text>
                <Text style={styles.quickActionSubtitle}>Manage locations</Text>
              </Card.Content>
            </Card>

            <Card style={styles.quickActionCard}>
              <Card.Content style={styles.quickActionContent}>
                <View style={[styles.quickActionIcon, { backgroundColor: colors.success[100] }]}>
                  <Ionicons name="card-outline" size={32} color={colors.success[600]} />
                </View>
                <Text style={styles.quickActionTitle}>Payments</Text>
                <Text style={styles.quickActionSubtitle}>View history</Text>
              </Card.Content>
            </Card>

            <Card style={styles.quickActionCard}>
              <Card.Content style={styles.quickActionContent}>
                <View style={[styles.quickActionIcon, { backgroundColor: colors.warning[100] }]}>
                  <Ionicons name="help-circle-outline" size={32} color={colors.warning[600]} />
                </View>
                <Text style={styles.quickActionTitle}>Help</Text>
                <Text style={styles.quickActionSubtitle}>Get support</Text>
              </Card.Content>
            </Card>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
    borderBottomLeftRadius: borderRadius.xl,
    borderBottomRightRadius: borderRadius.xl,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: colors.white,
    marginRight: spacing.md,
  },
  avatarLabel: {
    color: colors.primary[600],
    fontFamily: typography.fontFamily.bold,
  },
  headerText: {
    justifyContent: 'center',
  },
  greeting: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.primary[100],
  },
  userName: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.white,
  },
  headerRight: {
    flexDirection: 'row',
  },
  notificationButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  quickBookButton: {
    backgroundColor: colors.white,
    borderRadius: borderRadius.lg,
  },
  quickBookButtonContent: {
    paddingVertical: spacing.sm,
  },
  quickBookButtonLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.primary[600],
  },
  statsContainer: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  statCard: {
    width: (screenWidth - spacing.lg * 2 - spacing.md) / 2,
    borderRadius: borderRadius.lg,
  },
  statCardContent: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statNumber: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[600],
  },
  section: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
  },
  sectionLink: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.primary[600],
  },
  taskCard: {
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
  },
  taskCardContent: {
    paddingVertical: spacing.md,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  taskTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  taskCategory: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
  },
  statusChip: {
    backgroundColor: colors.transparent,
  },
  statusChipText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.medium,
  },
  emptyCard: {
    borderRadius: borderRadius.lg,
  },
  emptyCardContent: {
    alignItems: 'center',
    paddingVertical: spacing['2xl'],
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[600],
    marginTop: spacing.md,
    marginBottom: spacing.lg,
  },
  emptyButton: {
    backgroundColor: colors.primary[600],
    borderRadius: borderRadius.lg,
  },
  emptyButtonLabel: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.white,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: spacing.md,
  },
  quickActionCard: {
    width: (screenWidth - spacing.lg * 2 - spacing.md) / 2,
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
  },
  quickActionContent: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  quickActionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  quickActionTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  quickActionSubtitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
  },
});

export default DashboardScreen;
