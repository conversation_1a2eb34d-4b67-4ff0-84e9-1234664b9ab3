{"expo": {"name": "WorkBoy Customer", "slug": "workboy-customer", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#3b82f6"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.workboy.customer", "buildNumber": "1.0.0", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs access to your location to show nearby Work-Boys and provide accurate service delivery.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs access to your location to show nearby Work-Boys and provide accurate service delivery.", "NSCameraUsageDescription": "This app needs access to your camera to take photos for task documentation.", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to select images for task documentation.", "NSMicrophoneUsageDescription": "This app needs access to your microphone for voice notes and communication features."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#3b82f6"}, "package": "com.workboy.customer", "versionCode": 1, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "RECORD_AUDIO", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK"], "googleServicesFile": "./google-services.json"}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#3b82f6", "sounds": ["./assets/notification-sound.wav"]}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow WorkBoy to use your location to show nearby Work-Boys and provide accurate service delivery."}], ["expo-camera", {"cameraPermission": "Allow WorkBoy to access your camera to take photos for task documentation."}], ["expo-image-picker", {"photosPermission": "Allow WorkBoy to access your photo library to select images for task documentation."}]], "scheme": "workboy-customer", "extra": {"router": {"origin": false}, "eas": {"projectId": "your-eas-project-id"}}, "owner": "workboy-team"}}