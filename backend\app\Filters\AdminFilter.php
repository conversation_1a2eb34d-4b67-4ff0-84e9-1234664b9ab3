<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Admin Filter for Work-Boy Booking API
 * 
 * This filter ensures only admin users can access admin-only routes.
 * It should be used after the AuthFilter to ensure user is authenticated.
 */
class AdminFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Check if user data exists (should be set by AuthFilter)
        if (!isset($request->user)) {
            return $this->forbiddenResponse('Authentication required');
        }

        $user = $request->user;
        
        // Check if user is admin
        if (!isset($user['user_type']) || $user['user_type'] !== 'admin') {
            return $this->forbiddenResponse('Admin access required');
        }

        return $request;
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do after request
    }

    /**
     * Return forbidden response
     * 
     * @param string $message Error message
     * @return ResponseInterface
     */
    private function forbiddenResponse(string $message = 'Forbidden')
    {
        $response = service('response');
        
        $data = [
            'success' => false,
            'message' => $message,
        ];
        
        return $response->setJSON($data)->setStatusCode(403);
    }
}
