<?php

namespace App\Controllers\Api\V1;

use App\Controllers\Api\BaseApiController;
use App\Models\TaskModel;
use App\Models\TaskCategoryModel;
use App\Models\TaskStatusHistoryModel;
use App\Models\AddressModel;

/**
 * Task Controller for Work-Boy Booking API
 * 
 * Handles task creation, management, and status updates
 */
class TaskController extends BaseApiController
{
    protected $taskModel;
    protected $taskCategoryModel;
    protected $taskStatusHistoryModel;
    protected $addressModel;

    public function __construct()
    {
        parent::__construct();
        $this->taskModel = new TaskModel();
        $this->taskCategoryModel = new TaskCategoryModel();
        $this->taskStatusHistoryModel = new TaskStatusHistoryModel();
        $this->addressModel = new AddressModel();
    }

    /**
     * Get task categories
     * GET /api/v1/tasks/categories
     */
    public function getCategories()
    {
        try {
            $categories = $this->taskCategoryModel->getActiveCategories();
            return $this->apiResponse($categories, 'Categories retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get categories error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve categories');
        }
    }

    /**
     * Get tasks list
     * GET /api/v1/tasks
     */
    public function index()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $pagination = $this->getPaginationParams();
            $status = $this->request->getGet('status');
            $categoryId = $this->request->getGet('category_id');
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');

            $builder = $this->taskModel->select('tasks.*, 
                                               task_categories.name as category_name, 
                                               task_categories.icon as category_icon,
                                               addresses.city, addresses.state')
                                     ->join('task_categories', 'task_categories.id = tasks.category_id', 'left')
                                     ->join('addresses', 'addresses.id = tasks.address_id');

            // Filter based on user type
            if ($currentUser['user_type'] === 'customer') {
                $builder->where('tasks.customer_id', $currentUser['id']);
            } elseif ($currentUser['user_type'] === 'workboy') {
                $builder->where('tasks.workboy_id', $currentUser['id']);
            }
            // Admin can see all tasks

            // Apply filters
            if ($status) {
                $builder->where('tasks.status', $status);
            }

            if ($categoryId) {
                $builder->where('tasks.category_id', $categoryId);
            }

            if ($dateFrom) {
                $builder->where('tasks.scheduled_date >=', $dateFrom);
            }

            if ($dateTo) {
                $builder->where('tasks.scheduled_date <=', $dateTo);
            }

            // Get total count for pagination
            $total = $builder->countAllResults(false);

            // Get tasks
            $tasks = $builder->orderBy('tasks.created_at', 'DESC')
                           ->limit($pagination['limit'], $pagination['offset'])
                           ->get()
                           ->getResultArray();

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($tasks, 'Tasks retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Get tasks error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve tasks');
        }
    }

    /**
     * Create new task
     * POST /api/v1/tasks
     */
    public function create()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Only customers can create tasks');
            }

            $rules = [
                'category_id'        => 'permit_empty|integer',
                'title'              => 'required|max_length[255]',
                'description'        => 'required',
                'task_images'        => 'permit_empty|valid_json',
                'address_id'         => 'required|integer',
                'scheduled_date'     => 'required|valid_date',
                'scheduled_time'     => 'required',
                'estimated_duration' => 'permit_empty|integer|greater_than[0]',
                'priority'           => 'permit_empty|in_list[low,medium,high]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Verify address belongs to customer
            $address = $this->addressModel->where('id', $data['address_id'])
                                        ->where('user_id', $currentUser['id'])
                                        ->first();

            if (!$address) {
                return $this->errorResponse('Invalid address', 400);
            }

            // Verify category exists if provided
            if (isset($data['category_id'])) {
                $category = $this->taskCategoryModel->find($data['category_id']);
                if (!$category || !$category['is_active']) {
                    return $this->errorResponse('Invalid category', 400);
                }
            }

            // Validate scheduled date is not in the past
            $scheduledDateTime = $data['scheduled_date'] . ' ' . $data['scheduled_time'];
            if (strtotime($scheduledDateTime) < time()) {
                return $this->errorResponse('Scheduled date and time cannot be in the past', 400);
            }

            // Create task
            $taskData = [
                'customer_id'        => $currentUser['id'],
                'category_id'        => $data['category_id'] ?? null,
                'title'              => $data['title'],
                'description'        => $data['description'],
                'task_images'        => isset($data['task_images']) ? json_encode($data['task_images']) : null,
                'address_id'         => $data['address_id'],
                'scheduled_date'     => $data['scheduled_date'],
                'scheduled_time'     => $data['scheduled_time'],
                'estimated_duration' => $data['estimated_duration'] ?? null,
                'status'             => 'pending',
                'priority'           => $data['priority'] ?? 'medium',
            ];

            $taskId = $this->taskModel->insert($taskData);

            if (!$taskId) {
                return $this->serverErrorResponse('Failed to create task');
            }

            // Get created task with details
            $task = $this->taskModel->getTaskWithDetails($taskId);

            $this->logActivity('task_created', ['task_id' => $taskId]);

            return $this->apiResponse($task, 'Task created successfully', 201);

        } catch (\Exception $e) {
            log_message('error', 'Create task error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to create task');
        }
    }

    /**
     * Get task details
     * GET /api/v1/tasks/{id}
     */
    public function show($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $task = $this->taskModel->getTaskWithDetails($id);

            if (!$task) {
                return $this->notFoundResponse('Task not found');
            }

            // Check access permissions
            $hasAccess = false;
            if ($currentUser['user_type'] === 'admin') {
                $hasAccess = true;
            } elseif ($currentUser['user_type'] === 'customer' && $task['customer_id'] == $currentUser['id']) {
                $hasAccess = true;
            } elseif ($currentUser['user_type'] === 'workboy' && $task['workboy_id'] == $currentUser['id']) {
                $hasAccess = true;
            }

            if (!$hasAccess) {
                return $this->forbiddenResponse('Access denied');
            }

            // Get task status history
            $statusHistory = $this->taskStatusHistoryModel->getTaskHistory($id);
            $task['status_history'] = $statusHistory;

            return $this->apiResponse($task, 'Task retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get task error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve task');
        }
    }

    /**
     * Update task
     * PUT /api/v1/tasks/{id}
     */
    public function update($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $task = $this->taskModel->find($id);

            if (!$task) {
                return $this->notFoundResponse('Task not found');
            }

            // Only customer can update their own tasks and only if pending
            if ($currentUser['user_type'] !== 'customer' || 
                $task['customer_id'] != $currentUser['id'] || 
                $task['status'] !== 'pending') {
                return $this->forbiddenResponse('Cannot update this task');
            }

            $rules = [
                'title'              => 'permit_empty|max_length[255]',
                'description'        => 'permit_empty',
                'task_images'        => 'permit_empty|valid_json',
                'scheduled_date'     => 'permit_empty|valid_date',
                'scheduled_time'     => 'permit_empty',
                'estimated_duration' => 'permit_empty|integer|greater_than[0]',
                'priority'           => 'permit_empty|in_list[low,medium,high]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Update task data
            $updateData = [];
            $allowedFields = ['title', 'description', 'task_images', 'scheduled_date', 'scheduled_time', 'estimated_duration', 'priority'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    if ($field === 'task_images' && is_array($data[$field])) {
                        $updateData[$field] = json_encode($data[$field]);
                    } else {
                        $updateData[$field] = $data[$field];
                    }
                }
            }

            if (empty($updateData)) {
                return $this->errorResponse('No valid fields to update', 400);
            }

            // Validate scheduled date if provided
            if (isset($updateData['scheduled_date']) && isset($updateData['scheduled_time'])) {
                $scheduledDateTime = $updateData['scheduled_date'] . ' ' . $updateData['scheduled_time'];
                if (strtotime($scheduledDateTime) < time()) {
                    return $this->errorResponse('Scheduled date and time cannot be in the past', 400);
                }
            }

            $updated = $this->taskModel->update($id, $updateData);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to update task');
            }

            // Get updated task with details
            $updatedTask = $this->taskModel->getTaskWithDetails($id);

            $this->logActivity('task_updated', [
                'task_id' => $id,
                'updated_fields' => array_keys($updateData)
            ]);

            return $this->apiResponse($updatedTask, 'Task updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Update task error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to update task');
        }
    }

    /**
     * Cancel/Delete task
     * DELETE /api/v1/tasks/{id}
     */
    public function delete($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $task = $this->taskModel->find($id);

            if (!$task) {
                return $this->notFoundResponse('Task not found');
            }

            // Only customer can cancel their own tasks and only if pending
            if ($currentUser['user_type'] !== 'customer' ||
                $task['customer_id'] != $currentUser['id'] ||
                !in_array($task['status'], ['pending', 'assigned'])) {
                return $this->forbiddenResponse('Cannot cancel this task');
            }

            // Update status to cancelled instead of deleting
            $updated = $this->taskModel->updateTaskStatus($id, 'cancelled');

            if (!$updated) {
                return $this->serverErrorResponse('Failed to cancel task');
            }

            $this->logActivity('task_cancelled', ['task_id' => $id]);

            return $this->apiResponse(null, 'Task cancelled successfully');

        } catch (\Exception $e) {
            log_message('error', 'Cancel task error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to cancel task');
        }
    }

    /**
     * Assign task to Work-Boy (Admin only)
     * POST /api/v1/tasks/{id}/assign
     */
    public function assignTask($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $task = $this->taskModel->find($id);

            if (!$task) {
                return $this->notFoundResponse('Task not found');
            }

            if ($task['status'] !== 'pending') {
                return $this->errorResponse('Task is not available for assignment', 400);
            }

            $rules = [
                'workboy_id' => 'required|integer',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Verify Work-Boy exists and is approved
            $workboyProfileModel = new \App\Models\WorkboyProfileModel();
            $workboy = $workboyProfileModel->getByUserId($data['workboy_id']);

            if (!$workboy || $workboy['kyc_status'] !== 'approved') {
                return $this->errorResponse('Work-Boy not found or not approved', 400);
            }

            // Assign task
            $assigned = $this->taskModel->assignTask($id, $data['workboy_id']);

            if (!$assigned) {
                return $this->serverErrorResponse('Failed to assign task');
            }

            // Update Work-Boy availability
            $workboyProfileModel->updateAvailability($data['workboy_id'], 'busy');

            // Get updated task with details
            $updatedTask = $this->taskModel->getTaskWithDetails($id);

            $this->logActivity('task_assigned_by_admin', [
                'task_id' => $id,
                'workboy_id' => $data['workboy_id']
            ]);

            return $this->apiResponse($updatedTask, 'Task assigned successfully');

        } catch (\Exception $e) {
            log_message('error', 'Assign task error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to assign task');
        }
    }

    /**
     * Update task status
     * POST /api/v1/tasks/{id}/status
     */
    public function updateStatus($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $task = $this->taskModel->find($id);

            if (!$task) {
                return $this->notFoundResponse('Task not found');
            }

            // Check permissions
            $canUpdate = false;
            if ($currentUser['user_type'] === 'admin') {
                $canUpdate = true;
            } elseif ($currentUser['user_type'] === 'workboy' && $task['workboy_id'] == $currentUser['id']) {
                $canUpdate = true;
            }

            if (!$canUpdate) {
                return $this->forbiddenResponse('Cannot update this task status');
            }

            $rules = [
                'status'       => 'required|in_list[assigned,in_progress,completed,cancelled]',
                'notes'        => 'permit_empty',
                'location_lat' => 'permit_empty|decimal',
                'location_lng' => 'permit_empty|decimal',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Validate status transition
            $validTransitions = [
                'pending' => ['assigned', 'cancelled'],
                'assigned' => ['in_progress', 'cancelled'],
                'in_progress' => ['completed', 'cancelled'],
                'completed' => [], // Cannot change from completed
                'cancelled' => [], // Cannot change from cancelled
            ];

            if (!in_array($data['status'], $validTransitions[$task['status']])) {
                return $this->errorResponse('Invalid status transition', 400);
            }

            // Update task status
            $updated = $this->taskModel->updateTaskStatus($id, $data['status']);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to update task status');
            }

            // Add status history entry
            $this->taskStatusHistoryModel->addStatusWithLocation(
                $id,
                $data['status'],
                $data['location_lat'] ?? null,
                $data['location_lng'] ?? null,
                $data['notes'] ?? null,
                $currentUser['id']
            );

            // Update Work-Boy availability if task completed
            if ($data['status'] === 'completed' && $task['workboy_id']) {
                $workboyProfileModel = new \App\Models\WorkboyProfileModel();
                $workboyProfileModel->updateAvailability($task['workboy_id'], 'available');
            }

            // Get updated task with details
            $updatedTask = $this->taskModel->getTaskWithDetails($id);

            $this->logActivity('task_status_updated', [
                'task_id' => $id,
                'old_status' => $task['status'],
                'new_status' => $data['status']
            ]);

            return $this->apiResponse($updatedTask, 'Task status updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Update task status error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to update task status');
        }
    }
}
