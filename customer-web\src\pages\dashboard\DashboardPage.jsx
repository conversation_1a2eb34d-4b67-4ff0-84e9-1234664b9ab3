import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Plus, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  TrendingUp,
  Calendar,
  MapPin,
  Star
} from 'lucide-react'
import DashboardLayout from '../../components/layout/DashboardLayout'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import { apiHelpers, endpoints } from '../../config/api'
import { useAuth } from '../../contexts/AuthContext'
import { format } from 'date-fns'

const DashboardPage = () => {
  const [dashboardData, setDashboardData] = useState(null)
  const [loading, setLoading] = useState(true)
  const { userProfile } = useAuth()

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      const response = await apiHelpers.get(endpoints.customer.dashboard)
      
      if (response.success) {
        setDashboardData(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'text-warning-600 bg-warning-100'
      case 'assigned':
        return 'text-blue-600 bg-blue-100'
      case 'in_progress':
        return 'text-primary-600 bg-primary-100'
      case 'completed':
        return 'text-success-600 bg-success-100'
      case 'cancelled':
        return 'text-error-600 bg-error-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return Clock
      case 'assigned':
      case 'in_progress':
        return AlertCircle
      case 'completed':
        return CheckCircle
      default:
        return Clock
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    )
  }

  const stats = dashboardData?.task_stats || {}
  const recentTasks = dashboardData?.recent_tasks || []
  const upcomingTasks = dashboardData?.upcoming_tasks || []
  const paymentStats = dashboardData?.payment_stats || {}

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">
                Welcome back, {userProfile?.first_name}!
              </h2>
              <p className="text-primary-100">
                Ready to book your next service? Let's get started.
              </p>
            </div>
            <Link
              to="/tasks/create"
              className="btn bg-white text-primary-600 hover:bg-gray-50 btn-lg"
            >
              <Plus className="w-5 h-5 mr-2" />
              Book Service
            </Link>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Tasks</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pending_tasks || 0}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <AlertCircle className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-gray-900">{stats.in_progress_tasks || 0}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-success-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{stats.completed_tasks || 0}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-warning-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Spent</p>
                <p className="text-2xl font-bold text-gray-900">₹{paymentStats.total_spent || 0}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Tasks */}
          <div className="card">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Recent Tasks</h3>
                <Link
                  to="/tasks"
                  className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  View all
                </Link>
              </div>
            </div>
            <div className="p-6">
              {recentTasks.length > 0 ? (
                <div className="space-y-4">
                  {recentTasks.map((task) => {
                    const StatusIcon = getStatusIcon(task.status)
                    return (
                      <div key={task.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getStatusColor(task.status)}`}>
                            <StatusIcon className="w-5 h-5" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">{task.title}</p>
                            <p className="text-xs text-gray-500">
                              {task.category_name} • {format(new Date(task.created_at), 'MMM dd')}
                            </p>
                          </div>
                        </div>
                        <span className={`badge ${getStatusColor(task.status)}`}>
                          {task.status.replace('_', ' ')}
                        </span>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No recent tasks</p>
                  <Link
                    to="/tasks/create"
                    className="btn btn-primary btn-sm mt-4"
                  >
                    Create your first task
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Upcoming Tasks */}
          <div className="card">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Upcoming Tasks</h3>
                <Link
                  to="/tasks?status=assigned,in_progress"
                  className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  View all
                </Link>
              </div>
            </div>
            <div className="p-6">
              {upcomingTasks.length > 0 ? (
                <div className="space-y-4">
                  {upcomingTasks.map((task) => (
                    <div key={task.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Calendar className="w-5 h-5 text-primary-600" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">{task.title}</p>
                          <div className="flex items-center text-xs text-gray-500">
                            <Calendar className="w-3 h-3 mr-1" />
                            {format(new Date(task.scheduled_date), 'MMM dd, yyyy')} at {task.scheduled_time}
                          </div>
                        </div>
                      </div>
                      <Link
                        to={`/tasks/${task.id}`}
                        className="btn btn-outline btn-sm"
                      >
                        View
                      </Link>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No upcoming tasks</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/tasks/create"
              className="flex items-center p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors"
            >
              <Plus className="w-8 h-8 text-primary-600 mr-3" />
              <div>
                <p className="font-medium text-primary-900">Book a Service</p>
                <p className="text-sm text-primary-600">Create a new task</p>
              </div>
            </Link>

            <Link
              to="/addresses"
              className="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <MapPin className="w-8 h-8 text-blue-600 mr-3" />
              <div>
                <p className="font-medium text-blue-900">Manage Addresses</p>
                <p className="text-sm text-blue-600">Add or edit locations</p>
              </div>
            </Link>

            <Link
              to="/payments"
              className="flex items-center p-4 bg-success-50 rounded-lg hover:bg-success-100 transition-colors"
            >
              <TrendingUp className="w-8 h-8 text-success-600 mr-3" />
              <div>
                <p className="font-medium text-success-900">Payment History</p>
                <p className="text-sm text-success-600">View transactions</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default DashboardPage
