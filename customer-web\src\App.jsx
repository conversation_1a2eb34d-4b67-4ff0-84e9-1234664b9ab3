import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import { LoadingProvider } from './contexts/LoadingContext'
import ProtectedRoute from './components/auth/ProtectedRoute'
import LoadingSpinner from './components/ui/LoadingSpinner'
import ErrorBoundary from './components/ui/ErrorBoundary'

// Lazy load components for better performance
const LandingPage = React.lazy(() => import('./pages/LandingPage'))
const LoginPage = React.lazy(() => import('./pages/auth/LoginPage'))
const RegisterPage = React.lazy(() => import('./pages/auth/RegisterPage'))
const DashboardPage = React.lazy(() => import('./pages/dashboard/DashboardPage'))
const TasksPage = React.lazy(() => import('./pages/tasks/TasksPage'))
const CreateTaskPage = React.lazy(() => import('./pages/tasks/CreateTaskPage'))
const TaskDetailPage = React.lazy(() => import('./pages/tasks/TaskDetailPage'))
const AddressesPage = React.lazy(() => import('./pages/addresses/AddressesPage'))
const PaymentsPage = React.lazy(() => import('./pages/payments/PaymentsPage'))
const ProfilePage = React.lazy(() => import('./pages/profile/ProfilePage'))
const NotificationsPage = React.lazy(() => import('./pages/notifications/NotificationsPage'))
const NotFoundPage = React.lazy(() => import('./pages/NotFoundPage'))

// Loading fallback component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <LoadingSpinner size="lg" />
  </div>
)

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <LoadingProvider>
          <div className="min-h-screen bg-gray-50">
            <Suspense fallback={<PageLoader />}>
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<LandingPage />} />
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />
                
                {/* Protected Routes */}
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <DashboardPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/tasks" element={
                  <ProtectedRoute>
                    <TasksPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/tasks/create" element={
                  <ProtectedRoute>
                    <CreateTaskPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/tasks/:id" element={
                  <ProtectedRoute>
                    <TaskDetailPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/addresses" element={
                  <ProtectedRoute>
                    <AddressesPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/payments" element={
                  <ProtectedRoute>
                    <PaymentsPage />
                  </ProtectedRoute>
                } />
                
                <Route path="/profile" element={
                  <ProtectedRoute>
                    <ProfilePage />
                  </ProtectedRoute>
                } />
                
                <Route path="/notifications" element={
                  <ProtectedRoute>
                    <NotificationsPage />
                  </ProtectedRoute>
                } />
                
                {/* Redirect /app to /dashboard for backward compatibility */}
                <Route path="/app" element={<Navigate to="/dashboard" replace />} />
                
                {/* 404 Page */}
                <Route path="*" element={<NotFoundPage />} />
              </Routes>
            </Suspense>
          </div>
        </LoadingProvider>
      </AuthProvider>
    </ErrorBoundary>
  )
}

export default App
