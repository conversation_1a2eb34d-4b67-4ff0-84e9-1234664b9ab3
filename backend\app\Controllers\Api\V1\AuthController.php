<?php

namespace App\Controllers\Api\V1;

use App\Controllers\Api\BaseApiController;
use App\Models\UserModel;
use App\Models\CustomerProfileModel;
use App\Models\WorkboyProfileModel;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * Authentication Controller for Work-Boy Booking API
 * 
 * Handles user registration, login, profile management, and JWT token operations
 */
class AuthController extends BaseApiController
{
    protected $userModel;
    protected $customerProfileModel;
    protected $workboyProfileModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new UserModel();
        $this->customerProfileModel = new CustomerProfileModel();
        $this->workboyProfileModel = new WorkboyProfileModel();
    }

    /**
     * Register a new user
     * POST /api/v1/auth/register
     */
    public function register()
    {
        try {
            $rules = [
                'firebase_uid' => 'required|max_length[128]',
                'email'        => 'required|valid_email|max_length[255]',
                'phone'        => 'permit_empty|max_length[20]',
                'first_name'   => 'required|max_length[100]',
                'last_name'    => 'required|max_length[100]',
                'user_type'    => 'required|in_list[customer,workboy]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Check if user already exists
            $existingUser = $this->userModel->findByFirebaseUID($data['firebase_uid']);
            if ($existingUser) {
                return $this->errorResponse('User already registered', 409);
            }

            // Check if email already exists
            $existingEmail = $this->userModel->findByEmail($data['email']);
            if ($existingEmail) {
                return $this->errorResponse('Email already registered', 409);
            }

            // Create user
            $userData = [
                'firebase_uid'    => $data['firebase_uid'],
                'email'          => $data['email'],
                'phone'          => $data['phone'] ?? null,
                'first_name'     => $data['first_name'],
                'last_name'      => $data['last_name'],
                'user_type'      => $data['user_type'],
                'status'         => 'active',
                'email_verified' => true, // Assuming Firebase handles email verification
            ];

            $userId = $this->userModel->insert($userData);

            if (!$userId) {
                return $this->serverErrorResponse('Failed to create user account');
            }

            // Get created user with profile
            $user = $this->userModel->getUserWithProfile($userId);

            // Generate JWT token
            $tokenData = [
                'id' => $user['id'],
                'firebase_uid' => $user['firebase_uid'],
                'email' => $user['email'],
                'user_type' => $user['user_type'],
            ];

            $token = $this->generateJWT($tokenData);

            // Remove sensitive data
            unset($user['firebase_uid']);

            $this->logActivity('user_registered', ['user_id' => $userId, 'user_type' => $data['user_type']]);

            return $this->apiResponse([
                'user' => $user,
                'token' => $token,
                'token_type' => 'Bearer',
                'expires_in' => (int) env('JWT_EXPIRE_TIME', 86400),
            ], 'User registered successfully', 201);

        } catch (\Exception $e) {
            log_message('error', 'Registration error: ' . $e->getMessage());
            return $this->serverErrorResponse('Registration failed');
        }
    }

    /**
     * Login user
     * POST /api/v1/auth/login
     */
    public function login()
    {
        try {
            $rules = [
                'firebase_uid' => 'required|max_length[128]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Find user by Firebase UID
            $user = $this->userModel->findByFirebaseUID($data['firebase_uid']);

            if (!$user) {
                return $this->unauthorizedResponse('User not found');
            }

            if ($user['status'] !== 'active') {
                return $this->unauthorizedResponse('Account is inactive');
            }

            // Get user with profile
            $userWithProfile = $this->userModel->getUserWithProfile($user['id']);

            // Generate JWT token
            $tokenData = [
                'id' => $user['id'],
                'firebase_uid' => $user['firebase_uid'],
                'email' => $user['email'],
                'user_type' => $user['user_type'],
            ];

            $token = $this->generateJWT($tokenData);

            // Remove sensitive data
            unset($userWithProfile['firebase_uid']);

            $this->logActivity('user_login', ['user_id' => $user['id']]);

            return $this->apiResponse([
                'user' => $userWithProfile,
                'token' => $token,
                'token_type' => 'Bearer',
                'expires_in' => (int) env('JWT_EXPIRE_TIME', 86400),
            ], 'Login successful');

        } catch (\Exception $e) {
            log_message('error', 'Login error: ' . $e->getMessage());
            return $this->serverErrorResponse('Login failed');
        }
    }

    /**
     * Verify JWT token
     * POST /api/v1/auth/verify-token
     */
    public function verifyToken()
    {
        try {
            $token = $this->getJWTFromRequest();

            if (!$token) {
                return $this->unauthorizedResponse('Token required');
            }

            $userData = $this->verifyJWT($token);

            if (!$userData) {
                return $this->unauthorizedResponse('Invalid token');
            }

            // Check if user still exists and is active
            $user = $this->userModel->find($userData['id']);

            if (!$user || $user['status'] !== 'active') {
                return $this->unauthorizedResponse('User account is inactive');
            }

            return $this->apiResponse([
                'valid' => true,
                'user_id' => $userData['id'],
                'user_type' => $userData['user_type'],
                'expires_at' => date('Y-m-d H:i:s', $userData['exp']),
            ], 'Token is valid');

        } catch (\Exception $e) {
            log_message('error', 'Token verification error: ' . $e->getMessage());
            return $this->unauthorizedResponse('Token verification failed');
        }
    }

    /**
     * Get current user profile
     * GET /api/v1/auth/profile
     */
    public function getProfile()
    {
        try {
            $user = $this->request->user;

            if (!$user) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            // Get user with profile
            $userWithProfile = $this->userModel->getUserWithProfile($user['id']);

            if (!$userWithProfile) {
                return $this->notFoundResponse('User profile not found');
            }

            // Remove sensitive data
            unset($userWithProfile['firebase_uid']);

            return $this->apiResponse($userWithProfile, 'Profile retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get profile error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve profile');
        }
    }

    /**
     * Update user profile
     * PUT /api/v1/auth/profile
     */
    public function updateProfile()
    {
        try {
            $user = $this->request->user;

            if (!$user) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            $rules = [
                'first_name'    => 'permit_empty|max_length[100]',
                'last_name'     => 'permit_empty|max_length[100]',
                'phone'         => 'permit_empty|max_length[20]',
                'profile_image' => 'permit_empty|max_length[500]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Update user data
            $updateData = [];
            $allowedFields = ['first_name', 'last_name', 'phone', 'profile_image'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return $this->errorResponse('No valid fields to update', 400);
            }

            $updated = $this->userModel->update($user['id'], $updateData);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to update profile');
            }

            // Get updated user with profile
            $updatedUser = $this->userModel->getUserWithProfile($user['id']);
            unset($updatedUser['firebase_uid']);

            $this->logActivity('profile_updated', ['updated_fields' => array_keys($updateData)]);

            return $this->apiResponse($updatedUser, 'Profile updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Update profile error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to update profile');
        }
    }

    /**
     * Logout user (invalidate token - for future implementation)
     * POST /api/v1/auth/logout
     */
    public function logout()
    {
        try {
            $user = $this->request->user;

            if (!$user) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            // For now, just log the activity
            // In a production environment, you might want to maintain a blacklist of tokens
            $this->logActivity('user_logout', ['user_id' => $user['id']]);

            return $this->apiResponse(null, 'Logout successful');

        } catch (\Exception $e) {
            log_message('error', 'Logout error: ' . $e->getMessage());
            return $this->serverErrorResponse('Logout failed');
        }
    }

    /**
     * Refresh JWT token
     * POST /api/v1/auth/refresh
     */
    public function refreshToken()
    {
        try {
            $user = $this->request->user;

            if (!$user) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            // Generate new JWT token
            $tokenData = [
                'id' => $user['id'],
                'firebase_uid' => $user['firebase_uid'],
                'email' => $user['email'],
                'user_type' => $user['user_type'],
            ];

            $token = $this->generateJWT($tokenData);

            $this->logActivity('token_refreshed', ['user_id' => $user['id']]);

            return $this->apiResponse([
                'token' => $token,
                'token_type' => 'Bearer',
                'expires_in' => (int) env('JWT_EXPIRE_TIME', 86400),
            ], 'Token refreshed successfully');

        } catch (\Exception $e) {
            log_message('error', 'Token refresh error: ' . $e->getMessage());
            return $this->serverErrorResponse('Token refresh failed');
        }
    }
}
