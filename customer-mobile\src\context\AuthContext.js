import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { apiService } from '@services/apiService';
import { firebaseService } from '@services/firebaseService';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isFirstLaunch, setIsFirstLaunch] = useState(null);

  useEffect(() => {
    checkFirstLaunch();
    checkAuthState();
  }, []);

  const checkFirstLaunch = async () => {
    try {
      const hasLaunched = await AsyncStorage.getItem('hasLaunched');
      if (hasLaunched === null) {
        setIsFirstLaunch(true);
        await AsyncStorage.setItem('hasLaunched', 'true');
      } else {
        setIsFirstLaunch(false);
      }
    } catch (error) {
      console.error('Error checking first launch:', error);
      setIsFirstLaunch(false);
    }
  };

  const checkAuthState = async () => {
    try {
      setLoading(true);
      
      // Check for stored auth token
      const token = await SecureStore.getItemAsync('authToken');
      const userData = await AsyncStorage.getItem('userData');
      
      if (token && userData) {
        const parsedUserData = JSON.parse(userData);
        setUser(parsedUserData);
        
        // Verify token with backend and get fresh user data
        try {
          const response = await apiService.get('/auth/profile');
          if (response.success) {
            setUserProfile(response.data);
          }
        } catch (error) {
          console.error('Token verification failed:', error);
          await logout();
        }
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      setLoading(true);
      
      // Sign in with Firebase
      const firebaseUser = await firebaseService.signInWithEmail(email, password);
      
      if (firebaseUser) {
        // Get ID token
        const idToken = await firebaseUser.getIdToken();
        
        // Login to backend
        const response = await apiService.post('/auth/login', {
          firebase_uid: firebaseUser.uid,
          id_token: idToken,
        });
        
        if (response.success) {
          // Store auth data
          await SecureStore.setItemAsync('authToken', response.data.token);
          await AsyncStorage.setItem('userData', JSON.stringify(firebaseUser));
          
          setUser(firebaseUser);
          setUserProfile(response.data.user);
          
          return { success: true };
        } else {
          throw new Error(response.message || 'Login failed');
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setLoading(true);
      
      // Create Firebase user
      const firebaseUser = await firebaseService.createUserWithEmail(
        userData.email,
        userData.password
      );
      
      if (firebaseUser) {
        // Update Firebase profile
        await firebaseService.updateProfile(firebaseUser, {
          displayName: `${userData.firstName} ${userData.lastName}`,
        });
        
        // Get ID token
        const idToken = await firebaseUser.getIdToken();
        
        // Register with backend
        const response = await apiService.post('/auth/register', {
          firebase_uid: firebaseUser.uid,
          email: userData.email,
          first_name: userData.firstName,
          last_name: userData.lastName,
          phone: userData.phone,
          user_type: 'customer',
          id_token: idToken,
        });
        
        if (response.success) {
          // Store auth data
          await SecureStore.setItemAsync('authToken', response.data.token);
          await AsyncStorage.setItem('userData', JSON.stringify(firebaseUser));
          
          setUser(firebaseUser);
          setUserProfile(response.data.user);
          
          return { success: true };
        } else {
          // If backend registration fails, delete Firebase user
          await firebaseUser.delete();
          throw new Error(response.message || 'Registration failed');
        }
      }
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const loginWithGoogle = async () => {
    try {
      setLoading(true);
      
      const firebaseUser = await firebaseService.signInWithGoogle();
      
      if (firebaseUser) {
        const idToken = await firebaseUser.getIdToken();
        
        // Try to login first
        try {
          const loginResponse = await apiService.post('/auth/login', {
            firebase_uid: firebaseUser.uid,
            id_token: idToken,
          });
          
          if (loginResponse.success) {
            await SecureStore.setItemAsync('authToken', loginResponse.data.token);
            await AsyncStorage.setItem('userData', JSON.stringify(firebaseUser));
            
            setUser(firebaseUser);
            setUserProfile(loginResponse.data.user);
            
            return { success: true };
          }
        } catch (loginError) {
          // If login fails, try to register
          const names = firebaseUser.displayName?.split(' ') || ['', ''];
          const registerResponse = await apiService.post('/auth/register', {
            firebase_uid: firebaseUser.uid,
            email: firebaseUser.email,
            first_name: names[0] || '',
            last_name: names.slice(1).join(' ') || '',
            phone: firebaseUser.phoneNumber || '',
            user_type: 'customer',
            id_token: idToken,
          });
          
          if (registerResponse.success) {
            await SecureStore.setItemAsync('authToken', registerResponse.data.token);
            await AsyncStorage.setItem('userData', JSON.stringify(firebaseUser));
            
            setUser(firebaseUser);
            setUserProfile(registerResponse.data.user);
            
            return { success: true };
          } else {
            throw new Error(registerResponse.message || 'Google authentication failed');
          }
        }
      }
    } catch (error) {
      console.error('Google login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      
      // Sign out from Firebase
      await firebaseService.signOut();
      
      // Clear stored data
      await SecureStore.deleteItemAsync('authToken');
      await AsyncStorage.removeItem('userData');
      
      setUser(null);
      setUserProfile(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (profileData) => {
    try {
      setLoading(true);
      
      const response = await apiService.put('/auth/profile', profileData);
      
      if (response.success) {
        setUserProfile(response.data);
        
        // Update Firebase profile if name changed
        if (profileData.first_name || profileData.last_name) {
          await firebaseService.updateProfile(user, {
            displayName: `${profileData.first_name || userProfile.first_name} ${profileData.last_name || userProfile.last_name}`,
          });
        }
        
        return { success: true };
      } else {
        throw new Error(response.message || 'Profile update failed');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email) => {
    try {
      await firebaseService.sendPasswordResetEmail(email);
      return { success: true };
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  };

  const value = {
    // State
    user,
    userProfile,
    loading,
    isFirstLaunch,
    
    // Methods
    login,
    register,
    loginWithGoogle,
    logout,
    updateProfile,
    resetPassword,
    
    // Computed values
    isAuthenticated: !!user,
    isCustomer: userProfile?.user_type === 'customer',
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
