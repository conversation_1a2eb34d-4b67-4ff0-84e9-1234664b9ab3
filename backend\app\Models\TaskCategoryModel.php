<?php

namespace App\Models;

use CodeIgniter\Model;

class TaskCategoryModel extends Model
{
    protected $table            = 'task_categories';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'name',
        'description',
        'icon',
        'base_price',
        'is_active'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'name'        => 'required|max_length[100]|is_unique[task_categories.name,id,{id}]',
        'description' => 'permit_empty',
        'icon'        => 'permit_empty|max_length[255]',
        'base_price'  => 'permit_empty|decimal|greater_than[0]',
        'is_active'   => 'permit_empty|in_list[0,1]',
    ];

    protected $validationMessages = [
        'name' => [
            'required'  => 'Category name is required',
            'is_unique' => 'Category name already exists',
        ],
        'base_price' => [
            'greater_than' => 'Base price must be greater than 0',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get all active categories
     */
    public function getActiveCategories()
    {
        return $this->where('is_active', 1)
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get categories with task count
     */
    public function getCategoriesWithTaskCount()
    {
        $builder = $this->db->table('task_categories tc');
        
        return $builder->select('tc.*, COUNT(t.id) as task_count')
                      ->join('tasks t', 't.category_id = tc.id', 'left')
                      ->where('tc.is_active', 1)
                      ->groupBy('tc.id')
                      ->orderBy('task_count', 'DESC')
                      ->orderBy('tc.name', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get popular categories (most used)
     */
    public function getPopularCategories(int $limit = 5)
    {
        $builder = $this->db->table('task_categories tc');
        
        return $builder->select('tc.*, COUNT(t.id) as task_count')
                      ->join('tasks t', 't.category_id = tc.id', 'left')
                      ->where('tc.is_active', 1)
                      ->groupBy('tc.id')
                      ->having('task_count >', 0)
                      ->orderBy('task_count', 'DESC')
                      ->limit($limit)
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get category by name
     */
    public function getByName(string $name)
    {
        return $this->where('name', $name)->first();
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(int $categoryId)
    {
        $category = $this->find($categoryId);
        if (!$category) {
            return false;
        }

        $newStatus = $category['is_active'] ? 0 : 1;
        return $this->update($categoryId, ['is_active' => $newStatus]);
    }

    /**
     * Get category statistics
     */
    public function getCategoryStats(int $categoryId)
    {
        $builder = $this->db->table('task_categories tc');
        
        return $builder->select('tc.*,
                                COUNT(t.id) as total_tasks,
                                SUM(CASE WHEN t.status = "completed" THEN 1 ELSE 0 END) as completed_tasks,
                                SUM(CASE WHEN t.status = "pending" THEN 1 ELSE 0 END) as pending_tasks,
                                SUM(CASE WHEN t.status = "in_progress" THEN 1 ELSE 0 END) as active_tasks,
                                AVG(t.total_amount) as avg_task_amount,
                                SUM(t.total_amount) as total_revenue')
                      ->join('tasks t', 't.category_id = tc.id', 'left')
                      ->where('tc.id', $categoryId)
                      ->groupBy('tc.id')
                      ->get()
                      ->getRowArray();
    }

    /**
     * Search categories
     */
    public function searchCategories(string $query, bool $activeOnly = true)
    {
        $builder = $this->builder();
        
        $builder->groupStart()
                ->like('name', $query)
                ->orLike('description', $query)
                ->groupEnd();

        if ($activeOnly) {
            $builder->where('is_active', 1);
        }

        return $builder->orderBy('name', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get categories for admin with detailed stats
     */
    public function getAdminCategoriesList(int $limit = 20, int $offset = 0)
    {
        $builder = $this->db->table('task_categories tc');
        
        return $builder->select('tc.*,
                                COUNT(t.id) as total_tasks,
                                SUM(CASE WHEN t.status = "completed" THEN 1 ELSE 0 END) as completed_tasks,
                                SUM(CASE WHEN t.status = "pending" THEN 1 ELSE 0 END) as pending_tasks,
                                AVG(t.total_amount) as avg_task_amount,
                                SUM(t.total_amount) as total_revenue,
                                MAX(t.created_at) as last_task_date')
                      ->join('tasks t', 't.category_id = tc.id', 'left')
                      ->groupBy('tc.id')
                      ->orderBy('tc.created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->get()
                      ->getResultArray();
    }

    /**
     * Update base price
     */
    public function updateBasePrice(int $categoryId, float $basePrice)
    {
        return $this->update($categoryId, ['base_price' => $basePrice]);
    }

    /**
     * Get category usage trends (for analytics)
     */
    public function getCategoryTrends(int $categoryId, int $days = 30)
    {
        $builder = $this->db->table('tasks t');
        
        return $builder->select('DATE(t.created_at) as date, COUNT(*) as task_count')
                      ->where('t.category_id', $categoryId)
                      ->where('t.created_at >=', date('Y-m-d', strtotime("-{$days} days")))
                      ->groupBy('DATE(t.created_at)')
                      ->orderBy('date', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get categories by price range
     */
    public function getCategoriesByPriceRange(float $minPrice = null, float $maxPrice = null)
    {
        $builder = $this->where('is_active', 1);
        
        if ($minPrice !== null) {
            $builder->where('base_price >=', $minPrice);
        }
        
        if ($maxPrice !== null) {
            $builder->where('base_price <=', $maxPrice);
        }
        
        return $builder->orderBy('base_price', 'ASC')
                      ->findAll();
    }

    /**
     * Check if category can be deleted
     */
    public function canDelete(int $categoryId): array
    {
        $taskModel = new TaskModel();
        $taskCount = $taskModel->where('category_id', $categoryId)->countAllResults();
        
        if ($taskCount > 0) {
            return [
                'can_delete' => false,
                'reason' => "Category has {$taskCount} associated tasks and cannot be deleted"
            ];
        }
        
        return [
            'can_delete' => true,
            'reason' => null
        ];
    }

    /**
     * Safe delete category
     */
    public function safeDelete(int $categoryId)
    {
        $canDelete = $this->canDelete($categoryId);
        
        if (!$canDelete['can_delete']) {
            return [
                'success' => false,
                'message' => $canDelete['reason']
            ];
        }
        
        $deleted = $this->delete($categoryId);
        
        return [
            'success' => $deleted,
            'message' => $deleted ? 'Category deleted successfully' : 'Failed to delete category'
        ];
    }
}
