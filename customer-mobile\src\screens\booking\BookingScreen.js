import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  StatusBar,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Searchbar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { FlatGrid } from 'react-native-super-grid';
import { apiService, endpoints } from '@services/apiService';
import { colors, spacing, typography, borderRadius } from '@constants/theme';
import { showToast } from '@utils/toast';

const { width: screenWidth } = Dimensions.get('window');

const BookingScreen = ({ navigation }) => {
  const [categories, setCategories] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(endpoints.tasks.categories);
      
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      showToast('Failed to load categories', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleCategorySelect = (category) => {
    navigation.navigate('CreateTask', { 
      categoryId: category.id,
      categoryName: category.name 
    });
  };

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getCategoryIcon = (categoryName) => {
    const name = categoryName.toLowerCase();
    if (name.includes('clean')) return 'sparkles-outline';
    if (name.includes('plumb')) return 'water-outline';
    if (name.includes('electric')) return 'flash-outline';
    if (name.includes('paint')) return 'brush-outline';
    if (name.includes('carpen')) return 'hammer-outline';
    if (name.includes('repair')) return 'build-outline';
    if (name.includes('garden')) return 'leaf-outline';
    if (name.includes('move') || name.includes('pack')) return 'cube-outline';
    if (name.includes('cook') || name.includes('chef')) return 'restaurant-outline';
    if (name.includes('beauty') || name.includes('salon')) return 'cut-outline';
    if (name.includes('tutor') || name.includes('teach')) return 'book-outline';
    if (name.includes('pet')) return 'paw-outline';
    return 'construct-outline';
  };

  const getCategoryColor = (index) => {
    const colors_list = [
      colors.primary[500],
      colors.success[500],
      colors.warning[500],
      colors.error[500],
      colors.blue[500],
      colors.purple[500],
      colors.pink[500],
      colors.indigo[500],
    ];
    return colors_list[index % colors_list.length];
  };

  const renderCategoryItem = ({ item, index }) => (
    <Card 
      style={styles.categoryCard}
      onPress={() => handleCategorySelect(item)}
    >
      <Card.Content style={styles.categoryCardContent}>
        <View style={[
          styles.categoryIcon,
          { backgroundColor: `${getCategoryColor(index)}20` }
        ]}>
          <Ionicons
            name={getCategoryIcon(item.name)}
            size={32}
            color={getCategoryColor(index)}
          />
        </View>
        
        <Text style={styles.categoryName} numberOfLines={2}>
          {item.name}
        </Text>
        
        {item.description && (
          <Text style={styles.categoryDescription} numberOfLines={3}>
            {item.description}
          </Text>
        )}

        {item.starting_price && (
          <Text style={styles.categoryPrice}>
            Starting from ₹{item.starting_price}
          </Text>
        )}
      </Card.Content>
    </Card>
  );

  const popularServices = [
    {
      id: 'quick-1',
      name: 'Home Cleaning',
      icon: 'sparkles-outline',
      color: colors.primary[500],
      description: 'Professional home cleaning service',
    },
    {
      id: 'quick-2',
      name: 'Plumbing',
      icon: 'water-outline',
      color: colors.blue[500],
      description: 'Fix leaks, install fixtures',
    },
    {
      id: 'quick-3',
      name: 'Electrical Work',
      icon: 'flash-outline',
      color: colors.warning[500],
      description: 'Wiring, repairs, installations',
    },
    {
      id: 'quick-4',
      name: 'Painting',
      icon: 'brush-outline',
      color: colors.success[500],
      description: 'Interior and exterior painting',
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary[600]} />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <LinearGradient
          colors={[colors.primary[600], colors.primary[700]]}
          style={styles.header}
        >
          <Text style={styles.headerTitle}>Book a Service</Text>
          <Text style={styles.headerSubtitle}>
            Choose from our wide range of professional services
          </Text>
        </LinearGradient>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search for services..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
            inputStyle={styles.searchInput}
            iconColor={colors.gray[500]}
          />
        </View>

        {/* Popular Services */}
        {!searchQuery && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Popular Services</Text>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.popularServicesContainer}
            >
              {popularServices.map((service) => (
                <Card 
                  key={service.id}
                  style={styles.popularServiceCard}
                  onPress={() => navigation.navigate('CreateTask', { 
                    categoryName: service.name 
                  })}
                >
                  <Card.Content style={styles.popularServiceContent}>
                    <View style={[
                      styles.popularServiceIcon,
                      { backgroundColor: `${service.color}20` }
                    ]}>
                      <Ionicons
                        name={service.icon}
                        size={24}
                        color={service.color}
                      />
                    </View>
                    <Text style={styles.popularServiceName} numberOfLines={2}>
                      {service.name}
                    </Text>
                    <Text style={styles.popularServiceDescription} numberOfLines={2}>
                      {service.description}
                    </Text>
                  </Card.Content>
                </Card>
              ))}
            </ScrollView>
          </View>
        )}

        {/* All Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {searchQuery ? 'Search Results' : 'All Services'}
          </Text>
          
          {filteredCategories.length > 0 ? (
            <FlatGrid
              itemDimension={screenWidth / 2 - spacing.lg * 1.5}
              data={filteredCategories}
              style={styles.categoriesGrid}
              spacing={spacing.md}
              renderItem={renderCategoryItem}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="search-outline" size={64} color={colors.gray[400]} />
              <Text style={styles.emptyTitle}>No services found</Text>
              <Text style={styles.emptySubtitle}>
                Try searching with different keywords
              </Text>
            </View>
          )}
        </View>

        {/* Custom Service */}
        <View style={styles.section}>
          <Card style={styles.customServiceCard}>
            <Card.Content style={styles.customServiceContent}>
              <View style={styles.customServiceHeader}>
                <View style={styles.customServiceIcon}>
                  <Ionicons
                    name="add-circle-outline"
                    size={32}
                    color={colors.primary[600]}
                  />
                </View>
                <View style={styles.customServiceText}>
                  <Text style={styles.customServiceTitle}>
                    Need something else?
                  </Text>
                  <Text style={styles.customServiceDescription}>
                    Create a custom task for any service you need
                  </Text>
                </View>
              </View>
              
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('CreateTask')}
                style={styles.customServiceButton}
                labelStyle={styles.customServiceButtonLabel}
              >
                Create Custom Task
              </Button>
            </Card.Content>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
    borderBottomLeftRadius: borderRadius.xl,
    borderBottomRightRadius: borderRadius.xl,
  },
  headerTitle: {
    fontSize: typography.fontSize['3xl'],
    fontFamily: typography.fontFamily.bold,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  headerSubtitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.primary[100],
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    marginTop: -spacing.lg,
  },
  searchBar: {
    backgroundColor: colors.white,
    elevation: 4,
    borderRadius: borderRadius.lg,
  },
  searchInput: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
  },
  section: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.lg,
  },
  popularServicesContainer: {
    paddingRight: spacing.lg,
  },
  popularServiceCard: {
    width: 140,
    marginRight: spacing.md,
    borderRadius: borderRadius.lg,
  },
  popularServiceContent: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  popularServiceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  popularServiceName: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  popularServiceDescription: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
  },
  categoriesGrid: {
    flex: 1,
  },
  categoryCard: {
    borderRadius: borderRadius.lg,
    elevation: 2,
  },
  categoryCardContent: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  categoryIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  categoryName: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  categoryDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  categoryPrice: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.primary[600],
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: spacing['2xl'],
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
  },
  customServiceCard: {
    borderRadius: borderRadius.lg,
    elevation: 2,
  },
  customServiceContent: {
    paddingVertical: spacing.lg,
  },
  customServiceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  customServiceIcon: {
    width: 48,
    height: 48,
    backgroundColor: colors.primary[50],
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  customServiceText: {
    flex: 1,
  },
  customServiceTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  customServiceDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
  },
  customServiceButton: {
    borderColor: colors.primary[600],
    borderRadius: borderRadius.lg,
  },
  customServiceButtonLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.primary[600],
  },
});

export default BookingScreen;
