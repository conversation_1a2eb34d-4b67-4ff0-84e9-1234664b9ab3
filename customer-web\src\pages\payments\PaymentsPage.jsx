import React, { useState, useEffect } from 'react'
import { 
  CreditCard, 
  Calendar, 
  Filter, 
  Download, 
  CheckCircle, 
  XCircle, 
  Clock,
  TrendingUp,
  DollarSign
} from 'lucide-react'
import DashboardLayout from '../../components/layout/DashboardLayout'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import PaymentModal from '../../components/payments/PaymentModal'
import { apiHelpers, endpoints } from '../../config/api'
import { format } from 'date-fns'

const PaymentsPage = () => {
  const [payments, setPayments] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState('')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')
  const [paymentModalOpen, setPaymentModalOpen] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState(null)
  const [stats, setStats] = useState({
    total_payments: 0,
    total_amount: 0,
    completed_payments: 0,
    pending_payments: 0
  })

  useEffect(() => {
    fetchPayments()
  }, [selectedStatus, dateFrom, dateTo])

  const fetchPayments = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      if (selectedStatus) params.append('status', selectedStatus)
      if (dateFrom) params.append('date_from', dateFrom)
      if (dateTo) params.append('date_to', dateTo)

      const response = await apiHelpers.get(`${endpoints.payments.history}?${params.toString()}`)
      
      if (response.success) {
        setPayments(response.data)
        
        // Calculate stats
        const totalAmount = response.data.reduce((sum, payment) => 
          sum + parseFloat(payment.amount || 0) + parseFloat(payment.tip_amount || 0), 0
        )
        const completedPayments = response.data.filter(p => p.payment_status === 'completed').length
        const pendingPayments = response.data.filter(p => p.payment_status === 'pending').length
        
        setStats({
          total_payments: response.data.length,
          total_amount: totalAmount,
          completed_payments: completedPayments,
          pending_payments: pendingPayments
        })
      }
    } catch (error) {
      console.error('Failed to fetch payments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleViewPayment = (payment) => {
    setSelectedPayment(payment)
    setPaymentModalOpen(true)
  }

  const clearFilters = () => {
    setSelectedStatus('')
    setDateFrom('')
    setDateTo('')
  }

  const exportPayments = () => {
    // Create CSV content
    const headers = ['Date', 'Task', 'Amount', 'Tip', 'Total', 'Status', 'Payment Method']
    const csvContent = [
      headers.join(','),
      ...payments.map(payment => [
        format(new Date(payment.created_at), 'yyyy-MM-dd'),
        `"${payment.task_title || 'N/A'}"`,
        payment.amount || 0,
        payment.tip_amount || 0,
        (parseFloat(payment.amount || 0) + parseFloat(payment.tip_amount || 0)).toFixed(2),
        payment.payment_status,
        payment.payment_method || 'N/A'
      ].join(','))
    ].join('\n')

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `payments-${format(new Date(), 'yyyy-MM-dd')}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-success-700 bg-success-100 border-success-200'
      case 'pending':
        return 'text-warning-700 bg-warning-100 border-warning-200'
      case 'failed':
        return 'text-error-700 bg-error-100 border-error-200'
      default:
        return 'text-gray-700 bg-gray-100 border-gray-200'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return CheckCircle
      case 'pending':
        return Clock
      case 'failed':
        return XCircle
      default:
        return Clock
    }
  }

  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'completed', label: 'Completed' },
    { value: 'pending', label: 'Pending' },
    { value: 'failed', label: 'Failed' },
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Payment History</h1>
            <p className="mt-1 text-sm text-gray-500">
              Track all your payments and transactions
            </p>
          </div>
          <button
            onClick={exportPayments}
            disabled={payments.length === 0}
            className="mt-4 sm:mt-0 btn btn-outline btn-md disabled:opacity-50"
          >
            <Download className="w-4 h-4 mr-2" />
            Export CSV
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <CreditCard className="w-6 h-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Payments</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_payments}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-success-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Amount</p>
                <p className="text-2xl font-bold text-gray-900">₹{stats.total_amount.toFixed(2)}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{stats.completed_payments}</p>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-warning-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pending_payments}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label htmlFor="status" className="form-label">
                Status
              </label>
              <select
                id="status"
                className="input"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="dateFrom" className="form-label">
                From Date
              </label>
              <input
                id="dateFrom"
                type="date"
                className="input"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
              />
            </div>

            <div>
              <label htmlFor="dateTo" className="form-label">
                To Date
              </label>
              <input
                id="dateTo"
                type="date"
                className="input"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
              />
            </div>

            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="btn btn-outline btn-md w-full"
              >
                <Filter className="w-4 h-4 mr-2" />
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Payments List */}
        <div className="card">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : payments.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {payments.map((payment) => {
                const StatusIcon = getStatusIcon(payment.payment_status)
                const totalAmount = parseFloat(payment.amount || 0) + parseFloat(payment.tip_amount || 0)
                
                return (
                  <div key={payment.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center border ${getStatusColor(payment.payment_status)}`}>
                          <StatusIcon className="w-6 h-6" />
                        </div>
                        
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            {payment.task_title || 'Payment'}
                          </h3>
                          <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              {format(new Date(payment.created_at), 'MMM dd, yyyy')}
                            </div>
                            <div className="flex items-center">
                              <CreditCard className="w-4 h-4 mr-1" />
                              {payment.payment_method || 'Card'}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900">
                          ₹{totalAmount.toFixed(2)}
                        </div>
                        {payment.tip_amount > 0 && (
                          <div className="text-sm text-gray-500">
                            (₹{payment.amount} + ₹{payment.tip_amount} tip)
                          </div>
                        )}
                        <div className="flex items-center justify-end space-x-3 mt-2">
                          <span className={`badge border ${getStatusColor(payment.payment_status)}`}>
                            {payment.payment_status}
                          </span>
                          <button
                            onClick={() => handleViewPayment(payment)}
                            className="btn btn-outline btn-sm"
                          >
                            View Details
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CreditCard className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No payments found
              </h3>
              <p className="text-gray-500">
                {selectedStatus || dateFrom || dateTo
                  ? 'Try adjusting your filters to see more results.'
                  : 'Your payment history will appear here once you complete your first task.'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Payment Details Modal */}
      <PaymentModal
        isOpen={paymentModalOpen}
        onClose={() => {
          setPaymentModalOpen(false)
          setSelectedPayment(null)
        }}
        payment={selectedPayment}
      />
    </DashboardLayout>
  )
}

export default PaymentsPage
