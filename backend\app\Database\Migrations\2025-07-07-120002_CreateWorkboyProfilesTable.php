<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateWorkboyProfilesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'kyc_status' => [
                'type'       => 'ENUM',
                'constraint' => ['pending', 'approved', 'rejected'],
                'default'    => 'pending',
            ],
            'kyc_documents' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'skills' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'hourly_rate' => [
                'type'       => 'DECIMAL',
                'constraint' => '8,2',
                'null'       => true,
            ],
            'availability_status' => [
                'type'       => 'ENUM',
                'constraint' => ['available', 'busy', 'offline'],
                'default'    => 'offline',
            ],
            'rating' => [
                'type'       => 'DECIMAL',
                'constraint' => '3,2',
                'default'    => 0.00,
            ],
            'total_tasks_completed' => [
                'type'    => 'INT',
                'default' => 0,
            ],
            'total_earnings' => [
                'type'       => 'DECIMAL',
                'constraint' => '10,2',
                'default'    => 0.00,
            ],
            'bank_details' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('kyc_status');
        $this->forge->addKey('availability_status');
        $this->forge->addKey('rating');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        
        $this->forge->createTable('workboy_profiles');
    }

    public function down()
    {
        $this->forge->dropTable('workboy_profiles');
    }
}
