<?php

namespace App\Models;

use CodeIgniter\Model;

class CustomerProfileModel extends Model
{
    protected $table            = 'customer_profiles';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'user_id',
        'preferred_payment_method',
        'default_address_id',
        'notification_preferences'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id'                   => 'required|integer|is_unique[customer_profiles.user_id,id,{id}]',
        'preferred_payment_method'  => 'permit_empty|in_list[card,wallet,upi]',
        'default_address_id'        => 'permit_empty|integer',
        'notification_preferences'  => 'permit_empty|valid_json',
    ];

    protected $validationMessages = [
        'user_id' => [
            'required'  => 'User ID is required',
            'is_unique' => 'Customer profile already exists for this user',
        ],
        'preferred_payment_method' => [
            'in_list' => 'Payment method must be card, wallet, or upi',
        ],
        'notification_preferences' => [
            'valid_json' => 'Notification preferences must be valid JSON',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['encodeJsonFields'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['encodeJsonFields'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = ['decodeJsonFields'];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Encode JSON fields before saving
     */
    protected function encodeJsonFields(array $data)
    {
        if (isset($data['data']['notification_preferences']) && is_array($data['data']['notification_preferences'])) {
            $data['data']['notification_preferences'] = json_encode($data['data']['notification_preferences']);
        }

        return $data;
    }

    /**
     * Decode JSON fields after retrieval
     */
    protected function decodeJsonFields(array $data)
    {
        if (isset($data['data'])) {
            // Handle single record
            if (isset($data['data']['notification_preferences'])) {
                $data['data']['notification_preferences'] = json_decode($data['data']['notification_preferences'], true);
            }
        } else {
            // Handle multiple records
            foreach ($data as &$record) {
                if (isset($record['notification_preferences'])) {
                    $record['notification_preferences'] = json_decode($record['notification_preferences'], true);
                }
            }
        }

        return $data;
    }

    /**
     * Get customer profile by user ID
     */
    public function getByUserId(int $userId)
    {
        return $this->where('user_id', $userId)->first();
    }

    /**
     * Update notification preferences
     */
    public function updateNotificationPreferences(int $userId, array $preferences)
    {
        return $this->where('user_id', $userId)
                   ->set('notification_preferences', json_encode($preferences))
                   ->update();
    }

    /**
     * Set default address
     */
    public function setDefaultAddress(int $userId, int $addressId)
    {
        return $this->where('user_id', $userId)
                   ->set('default_address_id', $addressId)
                   ->update();
    }

    /**
     * Update payment method
     */
    public function updatePaymentMethod(int $userId, string $paymentMethod)
    {
        return $this->where('user_id', $userId)
                   ->set('preferred_payment_method', $paymentMethod)
                   ->update();
    }

    /**
     * Get customer profile with user data
     */
    public function getCustomerWithUser(int $userId)
    {
        return $this->select('customer_profiles.*, users.first_name, users.last_name, users.email, users.phone, users.profile_image')
                   ->join('users', 'users.id = customer_profiles.user_id')
                   ->where('customer_profiles.user_id', $userId)
                   ->first();
    }

    /**
     * Get customers with task statistics
     */
    public function getCustomersWithStats(int $limit = 20, int $offset = 0)
    {
        $builder = $this->db->table('customer_profiles cp');
        
        return $builder->select('cp.*, u.first_name, u.last_name, u.email, u.phone, u.status, u.created_at,
                                COUNT(t.id) as total_tasks,
                                SUM(CASE WHEN t.status = "completed" THEN 1 ELSE 0 END) as completed_tasks,
                                SUM(CASE WHEN t.status = "pending" THEN 1 ELSE 0 END) as pending_tasks,
                                COALESCE(SUM(p.amount), 0) as total_spent')
                      ->join('users u', 'u.id = cp.user_id')
                      ->join('tasks t', 't.customer_id = cp.user_id', 'left')
                      ->join('payments p', 'p.task_id = t.id AND p.payment_status = "completed"', 'left')
                      ->where('u.user_type', 'customer')
                      ->groupBy('cp.id')
                      ->orderBy('u.created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->get()
                      ->getResultArray();
    }
}
