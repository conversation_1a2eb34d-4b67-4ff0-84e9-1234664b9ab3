import React from 'react'
import { X, CreditCard, Calendar, CheckCircle, Clock, XCircle, Download } from 'lucide-react'
import { format } from 'date-fns'

const PaymentModal = ({ isOpen, onClose, payment }) => {
  if (!isOpen || !payment) return null

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-success-700 bg-success-100 border-success-200'
      case 'pending':
        return 'text-warning-700 bg-warning-100 border-warning-200'
      case 'failed':
        return 'text-error-700 bg-error-100 border-error-200'
      default:
        return 'text-gray-700 bg-gray-100 border-gray-200'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return CheckCircle
      case 'pending':
        return Clock
      case 'failed':
        return XCircle
      default:
        return Clock
    }
  }

  const StatusIcon = getStatusIcon(payment.payment_status)
  const totalAmount = parseFloat(payment.amount || 0) + parseFloat(payment.tip_amount || 0)

  const downloadReceipt = () => {
    // Create a simple receipt content
    const receiptContent = `
WORKBOY PAYMENT RECEIPT
========================

Payment ID: ${payment.id}
Date: ${format(new Date(payment.created_at), 'MMM dd, yyyy HH:mm')}
Task: ${payment.task_title || 'N/A'}

PAYMENT DETAILS
---------------
Service Amount: ₹${payment.amount || 0}
Tip Amount: ₹${payment.tip_amount || 0}
Total Amount: ₹${totalAmount.toFixed(2)}

Payment Method: ${payment.payment_method || 'N/A'}
Payment Status: ${payment.payment_status}
Gateway ID: ${payment.payment_gateway_id || 'N/A'}

Thank you for using WorkBoy!
    `.trim()

    const blob = new Blob([receiptContent], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `workboy-receipt-${payment.id}.txt`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-lg w-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Payment Details
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Status */}
            <div className="text-center">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 border-2 ${getStatusColor(payment.payment_status)}`}>
                <StatusIcon className="w-8 h-8" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">
                Payment {payment.payment_status}
              </h4>
              <p className="text-3xl font-bold text-gray-900">
                ₹{totalAmount.toFixed(2)}
              </p>
            </div>

            {/* Payment Information */}
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Payment ID</span>
                <span className="text-sm text-gray-900 font-mono">{payment.id}</span>
              </div>

              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Task</span>
                <span className="text-sm text-gray-900">{payment.task_title || 'N/A'}</span>
              </div>

              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Date & Time</span>
                <span className="text-sm text-gray-900">
                  {format(new Date(payment.created_at), 'MMM dd, yyyy HH:mm')}
                </span>
              </div>

              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">Payment Method</span>
                <div className="flex items-center">
                  <CreditCard className="w-4 h-4 mr-2 text-gray-400" />
                  <span className="text-sm text-gray-900">
                    {payment.payment_method || 'Card'}
                  </span>
                </div>
              </div>

              {payment.payment_gateway_id && (
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Gateway ID</span>
                  <span className="text-sm text-gray-900 font-mono">
                    {payment.payment_gateway_id}
                  </span>
                </div>
              )}
            </div>

            {/* Amount Breakdown */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <h5 className="text-sm font-medium text-gray-900">Amount Breakdown</h5>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Service Amount</span>
                <span className="text-sm text-gray-900">₹{payment.amount || 0}</span>
              </div>

              {payment.tip_amount > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Tip Amount</span>
                  <span className="text-sm text-gray-900">₹{payment.tip_amount}</span>
                </div>
              )}

              <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                <span className="text-sm font-medium text-gray-900">Total Amount</span>
                <span className="text-sm font-bold text-gray-900">₹{totalAmount.toFixed(2)}</span>
              </div>
            </div>

            {/* Gateway Response (if available) */}
            {payment.gateway_response && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Gateway Response</h5>
                <pre className="text-xs text-gray-600 overflow-x-auto">
                  {JSON.stringify(JSON.parse(payment.gateway_response), null, 2)}
                </pre>
              </div>
            )}

            {/* Status Messages */}
            {payment.payment_status === 'failed' && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <XCircle className="w-5 h-5 text-red-600 mr-2" />
                  <span className="text-sm font-medium text-red-800">Payment Failed</span>
                </div>
                <p className="text-sm text-red-700 mt-1">
                  This payment could not be processed. Please try again or contact support if the issue persists.
                </p>
              </div>
            )}

            {payment.payment_status === 'pending' && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center">
                  <Clock className="w-5 h-5 text-yellow-600 mr-2" />
                  <span className="text-sm font-medium text-yellow-800">Payment Pending</span>
                </div>
                <p className="text-sm text-yellow-700 mt-1">
                  Your payment is being processed. You will receive a confirmation once it's complete.
                </p>
              </div>
            )}

            {payment.payment_status === 'completed' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                  <span className="text-sm font-medium text-green-800">Payment Successful</span>
                </div>
                <p className="text-sm text-green-700 mt-1">
                  Your payment has been processed successfully. Thank you for using WorkBoy!
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200">
            <button
              onClick={downloadReceipt}
              className="btn btn-outline btn-sm"
            >
              <Download className="w-4 h-4 mr-2" />
              Download Receipt
            </button>
            <button
              onClick={onClose}
              className="btn btn-primary btn-md"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PaymentModal
