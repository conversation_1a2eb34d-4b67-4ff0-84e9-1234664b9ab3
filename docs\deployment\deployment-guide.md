# Work-Boy Booking Platform - Deployment Guide

## Overview
This guide covers the deployment process for all components of the Work-Boy Booking platform.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile Apps   │    │    Web Apps     │    │   Admin Panel   │
│  (React Native) │    │   (React.js)    │    │   (React.js)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Backend API   │
                    │  (PHP CI4)      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │     MySQL       │
                    │   Database      │
                    └─────────────────┘
```

## Prerequisites

### Development Environment
- Node.js (v16+)
- PHP (v8.0+)
- Composer
- MySQL (v8.0+)
- Git

### Production Environment
- Linux server (Ubuntu 20.04+ recommended)
- Nginx or Apache
- PHP-FPM
- MySQL
- SSL certificate
- Domain name

### Third-party Services
- Firebase project (Authentication, FCM)
- Google Cloud Platform (Maps API)
- Razorpay account (Payments)

## Environment Setup

### 1. Firebase Configuration

1. Create a Firebase project at https://console.firebase.google.com
2. Enable Authentication with Email/Password, Phone, Google, Apple
3. Enable Cloud Firestore (optional for additional data)
4. Enable Cloud Messaging for push notifications
5. Generate service account key for backend
6. Download configuration files:
   - `google-services.json` (Android)
   - `GoogleService-Info.plist` (iOS)
   - `firebase-config.js` (Web)

### 2. Google Maps API

1. Create project in Google Cloud Console
2. Enable Maps JavaScript API, Places API, Geocoding API
3. Create API key with appropriate restrictions
4. Set up billing account

### 3. Razorpay Setup

1. Create Razorpay account
2. Get API keys (Key ID and Key Secret)
3. Configure webhooks for payment status updates

## Backend Deployment (PHP CodeIgniter 4)

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP and extensions
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip php8.1-gd php8.1-intl -y

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install MySQL
sudo apt install mysql-server -y
sudo mysql_secure_installation

# Install Nginx
sudo apt install nginx -y
```

### 2. Database Setup

```sql
-- Create database
CREATE DATABASE workboy_booking;

-- Create user
CREATE USER 'workboy_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON workboy_booking.* TO 'workboy_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Backend Configuration

```bash
# Clone repository
git clone <repository-url> /var/www/workboy-booking
cd /var/www/workboy-booking/backend

# Install dependencies
composer install --no-dev --optimize-autoloader

# Set permissions
sudo chown -R www-data:www-data /var/www/workboy-booking
sudo chmod -R 755 /var/www/workboy-booking
sudo chmod -R 777 /var/www/workboy-booking/backend/writable

# Configure environment
cp env .env
```

### 4. Environment Configuration (.env)

```env
# Database
database.default.hostname = localhost
database.default.database = workboy_booking
database.default.username = workboy_user
database.default.password = secure_password
database.default.DBDriver = MySQLi

# Firebase
FIREBASE_PROJECT_ID = your-project-id
FIREBASE_PRIVATE_KEY_ID = your-private-key-id
FIREBASE_PRIVATE_KEY = "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL = <EMAIL>
FIREBASE_CLIENT_ID = your-client-id

# Google Maps
GOOGLE_MAPS_API_KEY = your-google-maps-api-key

# Razorpay
RAZORPAY_KEY_ID = your-razorpay-key-id
RAZORPAY_KEY_SECRET = your-razorpay-key-secret

# JWT
JWT_SECRET = your-jwt-secret-key

# App Settings
app.baseURL = https://api.yourdomain.com
app.forceGlobalSecureRequests = true
```

### 5. Nginx Configuration

```nginx
server {
    listen 80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    
    root /var/www/workboy-booking/backend/public;
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.ht {
        deny all;
    }
}
```

### 6. Database Migration

```bash
cd /var/www/workboy-booking/backend
php spark migrate
php spark db:seed DatabaseSeeder
```

## Web Application Deployment

### 1. Build Configuration

```bash
cd /var/www/workboy-booking/web-app

# Install dependencies
npm install

# Build for production
npm run build
```

### 2. Environment Configuration (.env.production)

```env
REACT_APP_API_BASE_URL=https://api.yourdomain.com/api/v1
REACT_APP_FIREBASE_API_KEY=your-firebase-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
REACT_APP_RAZORPAY_KEY_ID=your-razorpay-key-id
```

### 3. Nginx Configuration for Web App

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    
    root /var/www/workboy-booking/web-app/build;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## Admin Panel Deployment

Similar to web app deployment, but with different domain:

```nginx
server {
    listen 443 ssl http2;
    server_name admin.yourdomain.com;
    
    # Same SSL and configuration as web app
    root /var/www/workboy-booking/admin-panel/build;
    # ... rest of configuration
}
```

## Mobile App Deployment

### Android Deployment

1. **Build Configuration**
```bash
cd mobile-app/customer-app
# or cd mobile-app/workboy-app

# Install dependencies
npm install

# Generate signed APK
cd android
./gradlew assembleRelease
```

2. **Google Play Store**
- Create developer account
- Upload APK/AAB
- Configure store listing
- Submit for review

### iOS Deployment

1. **Build Configuration**
```bash
cd mobile-app/customer-app
# or cd mobile-app/workboy-app

# Install dependencies
npm install
cd ios && pod install && cd ..

# Build for release
npx react-native run-ios --configuration Release
```

2. **App Store**
- Enroll in Apple Developer Program
- Configure certificates and provisioning profiles
- Build and archive in Xcode
- Upload to App Store Connect
- Submit for review

## SSL Certificate Setup

### Using Let's Encrypt (Certbot)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Generate certificates
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
sudo certbot --nginx -d api.yourdomain.com
sudo certbot --nginx -d admin.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Logging

### 1. Application Logs

```bash
# Backend logs
tail -f /var/www/workboy-booking/backend/writable/logs/log-*.php

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# PHP-FPM logs
tail -f /var/log/php8.1-fpm.log
```

### 2. Database Monitoring

```sql
-- Monitor active connections
SHOW PROCESSLIST;

-- Monitor performance
SHOW STATUS LIKE 'Slow_queries';
SHOW STATUS LIKE 'Connections';
```

## Backup Strategy

### 1. Database Backup

```bash
#!/bin/bash
# backup-db.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u workboy_user -p workboy_booking > /backups/db_backup_$DATE.sql
find /backups -name "db_backup_*.sql" -mtime +7 -delete
```

### 2. File Backup

```bash
#!/bin/bash
# backup-files.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backups/files_backup_$DATE.tar.gz /var/www/workboy-booking
find /backups -name "files_backup_*.tar.gz" -mtime +7 -delete
```

## Performance Optimization

### 1. PHP Optimization

```ini
; php.ini optimizations
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
opcache.validate_timestamps=0
```

### 2. MySQL Optimization

```ini
; my.cnf optimizations
innodb_buffer_pool_size=1G
innodb_log_file_size=256M
query_cache_size=64M
```

### 3. Nginx Optimization

```nginx
# Enable gzip compression
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

# Enable caching
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## Security Checklist

- [ ] SSL certificates installed and configured
- [ ] Firewall configured (UFW or iptables)
- [ ] Database access restricted to localhost
- [ ] Regular security updates applied
- [ ] Strong passwords for all accounts
- [ ] API rate limiting implemented
- [ ] Input validation and sanitization
- [ ] CORS properly configured
- [ ] File upload restrictions in place
- [ ] Error messages don't expose sensitive information

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MySQL service status
   - Verify credentials in .env file
   - Check firewall settings

2. **API 500 Errors**
   - Check PHP error logs
   - Verify file permissions
   - Check .env configuration

3. **Mobile App Build Failures**
   - Clear React Native cache
   - Check Android/iOS dependencies
   - Verify signing certificates

4. **Push Notifications Not Working**
   - Verify Firebase configuration
   - Check FCM server key
   - Test with Firebase console
