<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

/**
 * Cross-Origin Resource Sharing (CORS) Configuration
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
 */
class Cors extends BaseConfig
{
    /**
     * The default CORS configuration.
     *
     * @var array{
     *      allowedOrigins: list<string>,
     *      allowedOriginsPatterns: list<string>,
     *      supportsCredentials: bool,
     *      allowedHeaders: list<string>,
     *      exposedHeaders: list<string>,
     *      allowedMethods: list<string>,
     *      maxAge: int,
     *  }
     */
    public array $default = [
        /**
         * Origins for the `Access-Control-Allow-Origin` header.
         * Work-Boy Booking allowed origins for development and production
         */
        'allowedOrigins' => [
            'http://localhost:3000',  // Customer Web App
            'http://localhost:3001',  // Admin Panel
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
        ],

        /**
         * Origin regex patterns for the `Access-Control-Allow-Origin` header.
         * Allow React Native development and production domains
         */
        'allowedOriginsPatterns' => [
            'https://\w+\.workboybooking\.com',  // Production domains
            'http://localhost:\d+',               // Any localhost port
        ],

        /**
         * Weather to send the `Access-Control-Allow-Credentials` header.
         * Enable credentials for authentication
         */
        'supportsCredentials' => true,

        /**
         * Set headers to allow.
         * Headers needed for Work-Boy Booking API
         */
        'allowedHeaders' => [
            'Content-Type',
            'Authorization',
            'X-Requested-With',
            'Accept',
            'Origin',
            'X-API-Key',
            'X-User-Type',
        ],

        /**
         * Set headers to expose.
         * Headers that frontend can access
         */
        'exposedHeaders' => [
            'X-Total-Count',
            'X-Page-Count',
            'X-Current-Page',
            'X-Per-Page',
        ],

        /**
         * Set methods to allow.
         * All HTTP methods needed for REST API
         */
        'allowedMethods' => [
            'GET',
            'POST',
            'PUT',
            'DELETE',
            'OPTIONS',
            'PATCH',
        ],

        /**
         * Set how many seconds the results of a preflight request can be cached.
         * Cache preflight requests for 2 hours
         */
        'maxAge' => 7200,
    ];
}
