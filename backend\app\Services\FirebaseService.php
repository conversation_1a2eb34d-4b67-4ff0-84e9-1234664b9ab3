<?php

namespace App\Services;

/**
 * Firebase Service for Work-Boy Booking API
 * 
 * Handles Firebase Cloud Messaging (FCM) for push notifications
 */
class FirebaseService
{
    private $serverKey;
    private $senderId;
    private $fcmUrl;

    public function __construct()
    {
        $this->serverKey = env('FCM_SERVER_KEY');
        $this->senderId = env('FCM_SENDER_ID');
        $this->fcmUrl = 'https://fcm.googleapis.com/fcm/send';
    }

    /**
     * Send push notification to a single device
     */
    public function sendToDevice(string $deviceToken, array $notification, array $data = [])
    {
        try {
            $payload = [
                'to' => $deviceToken,
                'notification' => $notification,
                'data' => $data,
                'priority' => 'high',
                'content_available' => true,
            ];

            return $this->sendNotification($payload);

        } catch (\Exception $e) {
            log_message('error', 'FCM send to device error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send push notification to multiple devices
     */
    public function sendToMultipleDevices(array $deviceTokens, array $notification, array $data = [])
    {
        try {
            $payload = [
                'registration_ids' => $deviceTokens,
                'notification' => $notification,
                'data' => $data,
                'priority' => 'high',
                'content_available' => true,
            ];

            return $this->sendNotification($payload);

        } catch (\Exception $e) {
            log_message('error', 'FCM send to multiple devices error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send push notification to a topic
     */
    public function sendToTopic(string $topic, array $notification, array $data = [])
    {
        try {
            $payload = [
                'to' => '/topics/' . $topic,
                'notification' => $notification,
                'data' => $data,
                'priority' => 'high',
                'content_available' => true,
            ];

            return $this->sendNotification($payload);

        } catch (\Exception $e) {
            log_message('error', 'FCM send to topic error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send notification with custom payload
     */
    private function sendNotification(array $payload)
    {
        $headers = [
            'Authorization: key=' . $this->serverKey,
            'Content-Type: application/json',
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->fcmUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $responseData = json_decode($response, true);

        if ($httpCode === 200 && isset($responseData['success'])) {
            return [
                'success' => true,
                'response' => $responseData,
                'sent_count' => $responseData['success'] ?? 0,
                'failed_count' => $responseData['failure'] ?? 0,
            ];
        } else {
            return [
                'success' => false,
                'error' => $responseData['error'] ?? 'Unknown error',
                'response' => $responseData,
            ];
        }
    }

    /**
     * Subscribe device to topic
     */
    public function subscribeToTopic(array $deviceTokens, string $topic)
    {
        try {
            $url = 'https://iid.googleapis.com/iid/v1:batchAdd';
            
            $payload = [
                'to' => '/topics/' . $topic,
                'registration_tokens' => $deviceTokens,
            ];

            $headers = [
                'Authorization: key=' . $this->serverKey,
                'Content-Type: application/json',
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            return [
                'success' => $httpCode === 200,
                'response' => json_decode($response, true),
            ];

        } catch (\Exception $e) {
            log_message('error', 'FCM subscribe to topic error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Unsubscribe device from topic
     */
    public function unsubscribeFromTopic(array $deviceTokens, string $topic)
    {
        try {
            $url = 'https://iid.googleapis.com/iid/v1:batchRemove';
            
            $payload = [
                'to' => '/topics/' . $topic,
                'registration_tokens' => $deviceTokens,
            ];

            $headers = [
                'Authorization: key=' . $this->serverKey,
                'Content-Type: application/json',
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            return [
                'success' => $httpCode === 200,
                'response' => json_decode($response, true),
            ];

        } catch (\Exception $e) {
            log_message('error', 'FCM unsubscribe from topic error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send task update notification
     */
    public function sendTaskUpdateNotification(string $deviceToken, array $taskData, string $status)
    {
        $statusMessages = [
            'assigned' => 'Your task has been assigned to a Work-Boy',
            'in_progress' => 'Your Work-Boy has started working on your task',
            'completed' => 'Your task has been completed',
            'cancelled' => 'Your task has been cancelled',
        ];

        $notification = [
            'title' => 'Task Update',
            'body' => $statusMessages[$status] ?? 'Your task status has been updated',
            'icon' => 'task_icon',
            'sound' => 'default',
        ];

        $data = [
            'type' => 'task_update',
            'task_id' => (string) $taskData['id'],
            'status' => $status,
            'click_action' => 'TASK_DETAIL',
        ];

        return $this->sendToDevice($deviceToken, $notification, $data);
    }

    /**
     * Send payment notification
     */
    public function sendPaymentNotification(string $deviceToken, array $paymentData, string $status)
    {
        $statusMessages = [
            'completed' => 'Payment successful! ₹' . number_format($paymentData['amount'], 2),
            'failed' => 'Payment failed. Please try again.',
            'refunded' => 'Refund processed successfully',
        ];

        $notification = [
            'title' => 'Payment Update',
            'body' => $statusMessages[$status] ?? 'Payment status updated',
            'icon' => 'payment_icon',
            'sound' => 'default',
        ];

        $data = [
            'type' => 'payment',
            'payment_id' => (string) $paymentData['id'],
            'status' => $status,
            'click_action' => 'PAYMENT_DETAIL',
        ];

        return $this->sendToDevice($deviceToken, $notification, $data);
    }

    /**
     * Send new task notification to Work-Boys
     */
    public function sendNewTaskNotification(array $deviceTokens, array $taskData)
    {
        $notification = [
            'title' => 'New Task Available',
            'body' => $taskData['title'] . ' in ' . $taskData['city'],
            'icon' => 'new_task_icon',
            'sound' => 'default',
        ];

        $data = [
            'type' => 'new_task',
            'task_id' => (string) $taskData['id'],
            'category' => $taskData['category_name'] ?? '',
            'click_action' => 'TASK_LIST',
        ];

        return $this->sendToMultipleDevices($deviceTokens, $notification, $data);
    }

    /**
     * Send promotional notification
     */
    public function sendPromotionalNotification(string $topic, string $title, string $message, array $data = [])
    {
        $notification = [
            'title' => $title,
            'body' => $message,
            'icon' => 'promo_icon',
            'sound' => 'default',
        ];

        $notificationData = array_merge([
            'type' => 'promotion',
            'click_action' => 'MAIN_ACTIVITY',
        ], $data);

        return $this->sendToTopic($topic, $notification, $notificationData);
    }

    /**
     * Send system notification
     */
    public function sendSystemNotification(array $deviceTokens, string $title, string $message)
    {
        $notification = [
            'title' => $title,
            'body' => $message,
            'icon' => 'system_icon',
            'sound' => 'default',
        ];

        $data = [
            'type' => 'system',
            'click_action' => 'MAIN_ACTIVITY',
        ];

        return $this->sendToMultipleDevices($deviceTokens, $notification, $data);
    }

    /**
     * Validate device token format
     */
    public function validateDeviceToken(string $token): bool
    {
        // FCM tokens are typically 152+ characters long
        return strlen($token) >= 140 && preg_match('/^[a-zA-Z0-9_-]+$/', $token);
    }

    /**
     * Get notification template
     */
    public function getNotificationTemplate(string $type): array
    {
        $templates = [
            'task_assigned' => [
                'title' => 'Task Assigned',
                'body' => 'A Work-Boy has been assigned to your task',
                'icon' => 'task_icon',
            ],
            'task_completed' => [
                'title' => 'Task Completed',
                'body' => 'Your task has been completed successfully',
                'icon' => 'success_icon',
            ],
            'payment_received' => [
                'title' => 'Payment Received',
                'body' => 'You have received a new payment',
                'icon' => 'payment_icon',
            ],
            'new_review' => [
                'title' => 'New Review',
                'body' => 'You have received a new review',
                'icon' => 'review_icon',
            ],
        ];

        return $templates[$type] ?? [
            'title' => 'Notification',
            'body' => 'You have a new notification',
            'icon' => 'default_icon',
        ];
    }

    /**
     * Schedule notification (for future implementation)
     */
    public function scheduleNotification(string $deviceToken, array $notification, array $data, int $scheduleTime)
    {
        // This would require a job queue system like Redis Queue or database-based scheduling
        // For now, we'll just log the scheduled notification
        
        log_message('info', 'Notification scheduled for: ' . date('Y-m-d H:i:s', $scheduleTime));
        
        return [
            'success' => true,
            'message' => 'Notification scheduled successfully',
            'schedule_time' => $scheduleTime,
        ];
    }
}
