import React, { createContext, useContext, useEffect, useState } from 'react';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { apiService, endpoints } from '@services/apiService';
import { showToast } from '@utils/toast';

// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

const NotificationContext = createContext({});

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

export const NotificationProvider = ({ children }) => {
  const [expoPushToken, setExpoPushToken] = useState('');
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState(null);

  useEffect(() => {
    registerForPushNotificationsAsync();
    setupNotificationListeners();
    fetchNotifications();
  }, []);

  const registerForPushNotificationsAsync = async () => {
    try {
      let token;

      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;
        
        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }
        
        setPermissionStatus(finalStatus);
        
        if (finalStatus !== 'granted') {
          showToast('Failed to get push token for push notification!', 'error');
          return;
        }
        
        token = await Notifications.getExpoPushTokenAsync({
          projectId: Constants.expoConfig?.extra?.eas?.projectId,
        });
        
        setExpoPushToken(token.data);
        
        // Send token to backend
        await sendTokenToBackend(token.data);
      } else {
        showToast('Must use physical device for Push Notifications', 'warning');
      }

      return token?.data;
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      showToast('Failed to register for push notifications', 'error');
    }
  };

  const sendTokenToBackend = async (token) => {
    try {
      await apiService.post('/notifications/register-token', {
        expo_push_token: token,
        device_type: Platform.OS,
      });
    } catch (error) {
      console.error('Failed to send token to backend:', error);
    }
  };

  const setupNotificationListeners = () => {
    // Handle notification received while app is foregrounded
    const foregroundSubscription = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('Notification received in foreground:', notification);
        
        // Add to local notifications list
        setNotifications(prev => [notification.request.content, ...prev]);
        setUnreadCount(prev => prev + 1);
        
        // Show toast for foreground notifications
        showToast(
          notification.request.content.title || 'New notification',
          'info',
          {
            text2: notification.request.content.body,
          }
        );
      }
    );

    // Handle notification response (user tapped notification)
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('Notification response:', response);
        
        const data = response.notification.request.content.data;
        handleNotificationNavigation(data);
      }
    );

    return () => {
      foregroundSubscription.remove();
      responseSubscription.remove();
    };
  };

  const handleNotificationNavigation = (data) => {
    // Handle navigation based on notification data
    if (data?.screen) {
      // You can use navigation service here to navigate to specific screens
      console.log('Navigate to:', data.screen, data);
      
      // Example navigation logic:
      // if (data.screen === 'TaskDetail' && data.taskId) {
      //   navigationRef.navigate('TaskDetail', { taskId: data.taskId });
      // }
    }
  };

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(endpoints.notifications.list);
      
      if (response.success) {
        setNotifications(response.data);
        
        // Calculate unread count
        const unread = response.data.filter(notification => !notification.is_read).length;
        setUnreadCount(unread);
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      await apiService.put(endpoints.notifications.markRead(notificationId));
      
      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      showToast('Failed to mark notification as read', 'error');
    }
  };

  const markAllAsRead = async () => {
    try {
      await apiService.put(endpoints.notifications.markAllRead);
      
      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      );
      
      setUnreadCount(0);
      showToast('All notifications marked as read', 'success');
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      showToast('Failed to mark all notifications as read', 'error');
    }
  };

  const deleteNotification = async (notificationId) => {
    try {
      await apiService.delete(endpoints.notifications.delete(notificationId));
      
      // Update local state
      const notification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      
      if (notification && !notification.is_read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      showToast('Notification deleted', 'success');
    } catch (error) {
      console.error('Failed to delete notification:', error);
      showToast('Failed to delete notification', 'error');
    }
  };

  const scheduleLocalNotification = async (title, body, data = {}, trigger = null) => {
    try {
      const id = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger,
      });
      
      return id;
    } catch (error) {
      console.error('Failed to schedule notification:', error);
      showToast('Failed to schedule notification', 'error');
    }
  };

  const cancelLocalNotification = async (notificationId) => {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Failed to cancel notification:', error);
    }
  };

  const cancelAllLocalNotifications = async () => {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to cancel all notifications:', error);
    }
  };

  const updateNotificationPreferences = async (preferences) => {
    try {
      const response = await apiService.put(endpoints.notifications.preferences, preferences);
      
      if (response.success) {
        showToast('Notification preferences updated', 'success');
        return true;
      }
    } catch (error) {
      console.error('Failed to update notification preferences:', error);
      showToast('Failed to update notification preferences', 'error');
      return false;
    }
  };

  const getNotificationPreferences = async () => {
    try {
      const response = await apiService.get(endpoints.notifications.preferences);
      
      if (response.success) {
        return response.data;
      }
    } catch (error) {
      console.error('Failed to get notification preferences:', error);
    }
    
    return null;
  };

  const requestPermissions = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      setPermissionStatus(status);
      
      if (status === 'granted') {
        showToast('Notification permissions granted', 'success');
        await registerForPushNotificationsAsync();
        return true;
      } else {
        showToast('Notification permissions denied', 'error');
        return false;
      }
    } catch (error) {
      console.error('Failed to request notification permissions:', error);
      showToast('Failed to request notification permissions', 'error');
      return false;
    }
  };

  const value = {
    // State
    expoPushToken,
    notifications,
    unreadCount,
    loading,
    permissionStatus,
    
    // Methods
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    scheduleLocalNotification,
    cancelLocalNotification,
    cancelAllLocalNotifications,
    updateNotificationPreferences,
    getNotificationPreferences,
    requestPermissions,
    
    // Computed values
    hasPermission: permissionStatus === 'granted',
    hasNotifications: notifications.length > 0,
    hasUnreadNotifications: unreadCount > 0,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
