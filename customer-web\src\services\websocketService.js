import { authService } from './authService'

class WebSocketService {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 1000
    this.listeners = new Map()
    this.isConnected = false
    this.userId = null
  }

  connect() {
    const token = authService.getToken()
    if (!token) {
      console.error('No auth token available for WebSocket connection')
      return
    }

    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8080'
    
    try {
      this.ws = new WebSocket(wsUrl)
      this.setupEventListeners()
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      this.scheduleReconnect()
    }
  }

  setupEventListeners() {
    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.isConnected = true
      this.reconnectAttempts = 0
      
      // Authenticate immediately after connection
      this.authenticate()
      
      this.emit('connected')
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.handleMessage(data)
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason)
      this.isConnected = false
      this.emit('disconnected')
      
      // Attempt to reconnect unless it was a clean close
      if (event.code !== 1000) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error)
      this.emit('error', error)
    }
  }

  handleMessage(data) {
    console.log('WebSocket message received:', data)
    
    switch (data.type) {
      case 'authenticated':
        this.userId = data.user_id
        this.emit('authenticated', data)
        break
        
      case 'task_update':
        this.emit('taskUpdate', data)
        break
        
      case 'location_update':
        this.emit('locationUpdate', data)
        break
        
      case 'notification':
        this.emit('notification', data)
        break
        
      case 'workboy_status':
        this.emit('workboyStatus', data)
        break
        
      case 'error':
        console.error('WebSocket server error:', data.message)
        this.emit('error', data)
        break
        
      case 'pong':
        // Handle ping/pong for connection health
        break
        
      default:
        console.warn('Unknown WebSocket message type:', data.type)
    }
  }

  authenticate() {
    const token = authService.getToken()
    if (token && this.isConnected) {
      this.send({
        type: 'authenticate',
        token: token
      })
    }
  }

  joinRoom(roomId) {
    if (this.isConnected) {
      this.send({
        type: 'join_room',
        room_id: roomId
      })
    }
  }

  trackTask(taskId) {
    this.joinRoom(`task_${taskId}`)
  }

  trackWorkBoy(workboyId) {
    this.joinRoom(`workboy_${workboyId}`)
  }

  updateTaskStatus(taskId, status, data = {}) {
    if (this.isConnected) {
      this.send({
        type: 'task_update',
        task_id: taskId,
        status: status,
        ...data
      })
    }
  }

  sendLocationUpdate(latitude, longitude) {
    if (this.isConnected) {
      this.send({
        type: 'location_update',
        latitude: latitude,
        longitude: longitude
      })
    }
  }

  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    } else {
      console.warn('WebSocket not connected, cannot send message:', data)
    }
  }

  scheduleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect()
      }, delay)
    } else {
      console.error('Max reconnection attempts reached')
      this.emit('maxReconnectAttemptsReached')
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
    this.isConnected = false
    this.userId = null
  }

  // Event listener management
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Error in WebSocket event callback:', error)
        }
      })
    }
  }

  // Health check
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' })
      }
    }, 30000) // Send ping every 30 seconds
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  // Utility methods
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      userId: this.userId,
      reconnectAttempts: this.reconnectAttempts
    }
  }
}

// Create singleton instance
export const websocketService = new WebSocketService()

// React hook for using WebSocket in components
export const useWebSocket = () => {
  const [connectionStatus, setConnectionStatus] = React.useState(
    websocketService.getConnectionStatus()
  )

  React.useEffect(() => {
    const updateStatus = () => {
      setConnectionStatus(websocketService.getConnectionStatus())
    }

    websocketService.on('connected', updateStatus)
    websocketService.on('disconnected', updateStatus)
    websocketService.on('authenticated', updateStatus)

    return () => {
      websocketService.off('connected', updateStatus)
      websocketService.off('disconnected', updateStatus)
      websocketService.off('authenticated', updateStatus)
    }
  }, [])

  return {
    ...connectionStatus,
    connect: () => websocketService.connect(),
    disconnect: () => websocketService.disconnect(),
    send: (data) => websocketService.send(data),
    on: (event, callback) => websocketService.on(event, callback),
    off: (event, callback) => websocketService.off(event, callback),
    trackTask: (taskId) => websocketService.trackTask(taskId),
    trackWorkBoy: (workboyId) => websocketService.trackWorkBoy(workboyId),
  }
}

export default websocketService
