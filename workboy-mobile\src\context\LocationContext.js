import React, { createContext, useContext, useState, useEffect } from 'react';
import * as Location from 'expo-location';
import { Alert } from 'react-native';

const LocationContext = createContext();

export const useLocation = () => {
  const context = useContext(LocationContext);
  if (!context) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
};

export const LocationProvider = ({ children }) => {
  const [location, setLocation] = useState(null);
  const [address, setAddress] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [permissionStatus, setPermissionStatus] = useState(null);

  // Request location permissions
  const requestLocationPermission = async () => {
    try {
      setLoading(true);
      setError(null);

      // Request foreground permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      setPermissionStatus(status);

      if (status !== 'granted') {
        setError('Location permission denied');
        Alert.alert(
          'Permission Required',
          'Location access is required to show nearby tasks and provide accurate service delivery.',
          [{ text: 'OK' }]
        );
        return false;
      }

      // Request background permissions for better tracking
      const backgroundStatus = await Location.requestBackgroundPermissionsAsync();
      
      return true;
    } catch (err) {
      setError(err.message);
      console.error('Error requesting location permission:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Get current location
  const getCurrentLocation = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if we have permission
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        const hasPermission = await requestLocationPermission();
        if (!hasPermission) return null;
      }

      // Get current position
      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeout: 10000,
      });

      setLocation(currentLocation);

      // Get address from coordinates
      const addressData = await Location.reverseGeocodeAsync({
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
      });

      if (addressData.length > 0) {
        setAddress(addressData[0]);
      }

      return currentLocation;
    } catch (err) {
      setError(err.message);
      console.error('Error getting current location:', err);
      Alert.alert('Location Error', 'Unable to get your current location. Please try again.');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Watch location changes
  const startLocationTracking = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        const hasPermission = await requestLocationPermission();
        if (!hasPermission) return null;
      }

      const subscription = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 10000, // Update every 10 seconds
          distanceInterval: 10, // Update every 10 meters
        },
        (newLocation) => {
          setLocation(newLocation);
        }
      );

      return subscription;
    } catch (err) {
      setError(err.message);
      console.error('Error starting location tracking:', err);
      return null;
    }
  };

  // Get address from coordinates
  const getAddressFromCoordinates = async (latitude, longitude) => {
    try {
      const addressData = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (addressData.length > 0) {
        return addressData[0];
      }
      return null;
    } catch (err) {
      console.error('Error getting address from coordinates:', err);
      return null;
    }
  };

  // Calculate distance between two points
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers
    return distance;
  };

  // Initialize location on app start
  useEffect(() => {
    const initializeLocation = async () => {
      const { status } = await Location.getForegroundPermissionsAsync();
      setPermissionStatus(status);
      
      if (status === 'granted') {
        getCurrentLocation();
      }
    };

    initializeLocation();
  }, []);

  const value = {
    location,
    address,
    loading,
    error,
    permissionStatus,
    requestLocationPermission,
    getCurrentLocation,
    startLocationTracking,
    getAddressFromCoordinates,
    calculateDistance,
  };

  return (
    <LocationContext.Provider value={value}>
      {children}
    </LocationContext.Provider>
  );
};
