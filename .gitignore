# Work-Boy Booking Platform - Git Ignore

# Dependencies
node_modules/
vendor/

# Production builds
build/
dist/
*.tgz
*.tar.gz

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Backend specific
backend/.env
backend/writable/logs/*
backend/writable/cache/*
backend/writable/session/*
backend/writable/uploads/*
!backend/writable/logs/index.html
!backend/writable/cache/index.html
!backend/writable/session/index.html
!backend/writable/uploads/index.html

# Frontend specific
web-app/.env*
admin-panel/.env*

# Mobile app specific
mobile-app/customer-app/.env*
mobile-app/workboy-app/.env*

# React Native
mobile-app/*/node_modules/
mobile-app/*/.expo/
mobile-app/*/dist/
mobile-app/*/npm-debug.*
mobile-app/*/yarn-error.log
mobile-app/*/yarn.lock
mobile-app/*/package-lock.json

# Android
mobile-app/*/android/app/build/
mobile-app/*/android/build/
mobile-app/*/android/.gradle/
mobile-app/*/android/local.properties
mobile-app/*/android/app/release/
mobile-app/*/android/app/debug/
mobile-app/*/android/gradle.properties
mobile-app/*/android/keystores/

# iOS
mobile-app/*/ios/build/
mobile-app/*/ios/DerivedData/
mobile-app/*/ios/*.xcworkspace/xcuserdata/
mobile-app/*/ios/*.xcodeproj/xcuserdata/
mobile-app/*/ios/*.xcodeproj/project.xcworkspace/xcuserdata/
mobile-app/*/ios/Pods/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Firebase
.firebase/
firebase-debug.log
firebase-debug.*.log

# Sensitive files
*.pem
*.key
*.crt
*.p12
*.mobileprovision

# Database
*.sqlite
*.db

# Backup files
*.bak
*.backup
*.old

# Compressed files
*.zip
*.rar
*.7z

# Documentation builds
docs/_build/

# Test files
test-results/
coverage/

# Local configuration
config/local.php
config/development.php

# Upload directories
uploads/
storage/

# Cache
cache/
*.cache

# Lock files (keep package-lock.json for consistency)
yarn.lock

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
