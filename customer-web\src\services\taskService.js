import api from './api'

export const taskService = {
  // Get all tasks for the current user
  getTasks: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const url = queryString ? `/tasks?${queryString}` : '/tasks'
    return api.get(url)
  },

  // Get a specific task by ID
  getTask: async (id) => {
    return api.get(`/tasks/${id}`)
  },

  // Create a new task
  createTask: async (taskData) => {
    return api.post('/tasks', taskData)
  },

  // Update an existing task
  updateTask: async (id, taskData) => {
    return api.put(`/tasks/${id}`, taskData)
  },

  // Cancel a task
  cancelTask: async (id) => {
    return api.put(`/tasks/${id}/cancel`)
  },

  // Get task categories
  getCategories: async () => {
    return api.get('/tasks/categories')
  },

  // Get task status history
  getTaskHistory: async (id) => {
    return api.get(`/tasks/${id}/history`)
  },

  // Add a review for a completed task
  addReview: async (taskId, reviewData) => {
    return api.post(`/tasks/${taskId}/review`, reviewData)
  },

  // Get task statistics for dashboard
  getTaskStats: async () => {
    return api.get('/tasks/stats')
  },

  // Search tasks
  searchTasks: async (query) => {
    return api.get(`/tasks/search?q=${encodeURIComponent(query)}`)
  },

  // Get nearby workboys for a task
  getNearbyWorkboys: async (taskId) => {
    return api.get(`/tasks/${taskId}/nearby-workboys`)
  }
}

export default taskService
