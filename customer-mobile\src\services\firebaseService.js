import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  GoogleAuthProvider,
  signInWithCredential
} from 'firebase/auth';
import { 
  getMessaging, 
  getToken, 
  onMessage 
} from 'firebase/messaging';
import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';
import Constants from 'expo-constants';

// Complete the auth session
WebBrowser.maybeCompleteAuthSession();

// Firebase configuration
const firebaseConfig = {
  apiKey: Constants.expoConfig?.extra?.firebaseApiKey,
  authDomain: Constants.expoConfig?.extra?.firebaseAuthDomain,
  projectId: Constants.expoConfig?.extra?.firebaseProjectId,
  storageBucket: Constants.expoConfig?.extra?.firebaseStorageBucket,
  messagingSenderId: Constants.expoConfig?.extra?.firebaseMessagingSenderId,
  appId: Constants.expoConfig?.extra?.firebaseAppId,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Google Auth configuration
const googleConfig = {
  expoClientId: Constants.expoConfig?.extra?.googleExpoClientId,
  iosClientId: Constants.expoConfig?.extra?.googleIosClientId,
  androidClientId: Constants.expoConfig?.extra?.googleAndroidClientId,
  webClientId: Constants.expoConfig?.extra?.googleWebClientId,
};

export const firebaseService = {
  // Email/Password Authentication
  signInWithEmail: async (email, password) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (error) {
      console.error('Email sign in error:', error);
      throw error;
    }
  },

  createUserWithEmail: async (email, password) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (error) {
      console.error('Email sign up error:', error);
      throw error;
    }
  },

  // Google Authentication
  signInWithGoogle: async () => {
    try {
      const [request, response, promptAsync] = Google.useAuthRequest(googleConfig);

      if (response?.type === 'success') {
        const { id_token, access_token } = response.params;
        
        const credential = GoogleAuthProvider.credential(id_token, access_token);
        const userCredential = await signInWithCredential(auth, credential);
        
        return userCredential.user;
      } else {
        throw new Error('Google sign in was cancelled or failed');
      }
    } catch (error) {
      console.error('Google sign in error:', error);
      throw error;
    }
  },

  // Sign out
  signOut: async () => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  },

  // Password reset
  sendPasswordResetEmail: async (email) => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  },

  // Update profile
  updateProfile: async (user, profileData) => {
    try {
      await updateProfile(user, profileData);
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  },

  // Get current user
  getCurrentUser: () => {
    return auth.currentUser;
  },

  // Get ID token
  getIdToken: async (forceRefresh = false) => {
    try {
      const user = auth.currentUser;
      if (user) {
        return await user.getIdToken(forceRefresh);
      }
      return null;
    } catch (error) {
      console.error('Get ID token error:', error);
      throw error;
    }
  },

  // Auth state listener
  onAuthStateChanged: (callback) => {
    return auth.onAuthStateChanged(callback);
  },
};

// Push Notifications Service
export const notificationService = {
  // Request notification permissions
  requestPermissions: async () => {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        throw new Error('Permission not granted for notifications');
      }
      
      return finalStatus;
    } catch (error) {
      console.error('Notification permission error:', error);
      throw error;
    }
  },

  // Get push token
  getPushToken: async () => {
    try {
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });
      
      return token.data;
    } catch (error) {
      console.error('Get push token error:', error);
      throw error;
    }
  },

  // Configure notifications
  configureNotifications: () => {
    // Set notification handler
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });

    // Handle notification received while app is foregrounded
    const foregroundSubscription = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('Notification received in foreground:', notification);
      }
    );

    // Handle notification response (user tapped notification)
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('Notification response:', response);
        // Handle navigation based on notification data
        const data = response.notification.request.content.data;
        if (data?.screen) {
          // Navigate to specific screen
          // You can use navigation service here
        }
      }
    );

    return () => {
      foregroundSubscription.remove();
      responseSubscription.remove();
    };
  },

  // Schedule local notification
  scheduleNotification: async (title, body, data = {}, trigger = null) => {
    try {
      const id = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger,
      });
      
      return id;
    } catch (error) {
      console.error('Schedule notification error:', error);
      throw error;
    }
  },

  // Cancel notification
  cancelNotification: async (notificationId) => {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Cancel notification error:', error);
      throw error;
    }
  },

  // Cancel all notifications
  cancelAllNotifications: async () => {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Cancel all notifications error:', error);
      throw error;
    }
  },
};

export default firebaseService;
