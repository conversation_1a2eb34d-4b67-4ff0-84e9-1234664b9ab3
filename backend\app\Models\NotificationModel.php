<?php

namespace App\Models;

use CodeIgniter\Model;

class NotificationModel extends Model
{
    protected $table            = 'notifications';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'user_id',
        'title',
        'message',
        'type',
        'related_id',
        'is_read'
    ];

    // Dates
    protected $useTimestamps = false; // Only created_at, no updated_at
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';

    // Validation
    protected $validationRules = [
        'user_id'    => 'required|integer',
        'title'      => 'required|max_length[255]',
        'message'    => 'required',
        'type'       => 'required|in_list[task_update,payment,system,promotion]',
        'related_id' => 'permit_empty|integer',
        'is_read'    => 'permit_empty|in_list[0,1]',
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
        ],
        'title' => [
            'required' => 'Notification title is required',
        ],
        'message' => [
            'required' => 'Notification message is required',
        ],
        'type' => [
            'required' => 'Notification type is required',
            'in_list'  => 'Invalid notification type',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Set created_at timestamp
     */
    protected function setCreatedAt(array $data)
    {
        $data['data']['created_at'] = date('Y-m-d H:i:s');
        return $data;
    }

    /**
     * Get notifications by user ID
     */
    public function getByUserId(int $userId, bool $unreadOnly = false, int $limit = 20, int $offset = 0)
    {
        $builder = $this->where('user_id', $userId);
        
        if ($unreadOnly) {
            $builder->where('is_read', 0);
        }
        
        return $builder->orderBy('created_at', 'DESC')
                      ->findAll($limit, $offset);
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadCount(int $userId): int
    {
        return $this->where('user_id', $userId)
                   ->where('is_read', 0)
                   ->countAllResults();
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(int $notificationId, int $userId)
    {
        return $this->where('id', $notificationId)
                   ->where('user_id', $userId)
                   ->set('is_read', 1)
                   ->update();
    }

    /**
     * Mark all notifications as read for user
     */
    public function markAllAsRead(int $userId)
    {
        return $this->where('user_id', $userId)
                   ->where('is_read', 0)
                   ->set('is_read', 1)
                   ->update();
    }

    /**
     * Create task update notification
     */
    public function createTaskNotification(int $userId, string $title, string $message, int $taskId)
    {
        return $this->insert([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'task_update',
            'related_id' => $taskId,
            'is_read' => 0,
        ]);
    }

    /**
     * Create payment notification
     */
    public function createPaymentNotification(int $userId, string $title, string $message, int $paymentId)
    {
        return $this->insert([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'payment',
            'related_id' => $paymentId,
            'is_read' => 0,
        ]);
    }

    /**
     * Create system notification
     */
    public function createSystemNotification(int $userId, string $title, string $message)
    {
        return $this->insert([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'system',
            'is_read' => 0,
        ]);
    }

    /**
     * Create promotional notification
     */
    public function createPromotionalNotification(int $userId, string $title, string $message)
    {
        return $this->insert([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'promotion',
            'is_read' => 0,
        ]);
    }

    /**
     * Broadcast notification to multiple users
     */
    public function broadcastNotification(array $userIds, string $title, string $message, string $type = 'system', int $relatedId = null)
    {
        $notifications = [];
        $timestamp = date('Y-m-d H:i:s');
        
        foreach ($userIds as $userId) {
            $notifications[] = [
                'user_id' => $userId,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'related_id' => $relatedId,
                'is_read' => 0,
                'created_at' => $timestamp,
            ];
        }
        
        return $this->insertBatch($notifications);
    }

    /**
     * Get notifications by type
     */
    public function getByType(string $type, int $limit = 20, int $offset = 0)
    {
        return $this->where('type', $type)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Delete old notifications
     */
    public function deleteOldNotifications(int $daysToKeep = 90)
    {
        $cutoffDate = date('Y-m-d', strtotime("-{$daysToKeep} days"));
        
        return $this->where('created_at <', $cutoffDate)
                   ->where('is_read', 1) // Only delete read notifications
                   ->delete();
    }

    /**
     * Get notification statistics
     */
    public function getNotificationStats(int $userId = null)
    {
        $builder = $this->builder();
        
        if ($userId) {
            $builder->where('user_id', $userId);
        }

        return $builder->select('
                            COUNT(*) as total_notifications,
                            SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count,
                            SUM(CASE WHEN type = "task_update" THEN 1 ELSE 0 END) as task_notifications,
                            SUM(CASE WHEN type = "payment" THEN 1 ELSE 0 END) as payment_notifications,
                            SUM(CASE WHEN type = "system" THEN 1 ELSE 0 END) as system_notifications,
                            SUM(CASE WHEN type = "promotion" THEN 1 ELSE 0 END) as promotional_notifications
                        ')
                      ->get()
                      ->getRowArray();
    }

    /**
     * Get recent notifications for dashboard
     */
    public function getRecentNotifications(int $userId, int $limit = 5)
    {
        return $this->where('user_id', $userId)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Search notifications
     */
    public function searchNotifications(int $userId, string $query, int $limit = 20, int $offset = 0)
    {
        return $this->where('user_id', $userId)
                   ->groupStart()
                   ->like('title', $query)
                   ->orLike('message', $query)
                   ->groupEnd()
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get notifications with pagination and filters
     */
    public function getFilteredNotifications(int $userId, array $filters = [], int $limit = 20, int $offset = 0)
    {
        $builder = $this->where('user_id', $userId);
        
        if (isset($filters['type'])) {
            $builder->where('type', $filters['type']);
        }
        
        if (isset($filters['is_read'])) {
            $builder->where('is_read', $filters['is_read']);
        }
        
        if (isset($filters['date_from'])) {
            $builder->where('created_at >=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $builder->where('created_at <=', $filters['date_to']);
        }

        return $builder->orderBy('created_at', 'DESC')
                      ->findAll($limit, $offset);
    }

    /**
     * Bulk mark notifications as read
     */
    public function bulkMarkAsRead(array $notificationIds, int $userId)
    {
        return $this->whereIn('id', $notificationIds)
                   ->where('user_id', $userId)
                   ->set('is_read', 1)
                   ->update();
    }

    /**
     * Delete notification
     */
    public function deleteNotification(int $notificationId, int $userId)
    {
        return $this->where('id', $notificationId)
                   ->where('user_id', $userId)
                   ->delete();
    }

    /**
     * Get notification preferences (for future implementation)
     */
    public function getUserNotificationPreferences(int $userId)
    {
        // This would integrate with customer_profiles notification_preferences
        $customerProfileModel = new CustomerProfileModel();
        $profile = $customerProfileModel->getByUserId($userId);
        
        return $profile['notification_preferences'] ?? [
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => false,
            'task_updates' => true,
            'promotional' => true,
        ];
    }
}
