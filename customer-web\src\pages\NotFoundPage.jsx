import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Home, ArrowLeft } from 'lucide-react'

const NotFoundPage = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          {/* 404 Illustration */}
          <div className="mx-auto w-32 h-32 bg-primary-100 rounded-full flex items-center justify-center mb-8">
            <span className="text-6xl font-bold text-primary-600">404</span>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Page Not Found
          </h1>
          
          <p className="text-lg text-gray-600 mb-8">
            Sorry, we couldn't find the page you're looking for. 
            The page might have been moved, deleted, or you entered the wrong URL.
          </p>
          
          <div className="space-y-4">
            <Link
              to="/dashboard"
              className="w-full btn btn-primary btn-lg"
            >
              <Home className="w-5 h-5 mr-2" />
              Go to Dashboard
            </Link>
            
            <button
              onClick={() => window.history.back()}
              className="w-full btn btn-outline btn-lg"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Go Back
            </button>
          </div>
          
          <div className="mt-8 text-sm text-gray-500">
            <p>
              If you think this is a mistake, please{' '}
              <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-500">
                contact our support team
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NotFoundPage
