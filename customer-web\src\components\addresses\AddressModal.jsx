import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { X, MapPin, Navigation, Home, Building, Star } from 'lucide-react'
import LoadingSpinner from '../ui/LoadingSpinner'
import GoogleMapsPicker from './GoogleMapsPicker'

const AddressModal = ({ isOpen, onClose, onSubmit, address }) => {
  const [loading, setLoading] = useState(false)
  const [showMap, setShowMap] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm()

  const watchedFields = watch()

  useEffect(() => {
    if (isOpen) {
      if (address) {
        // Editing existing address
        reset({
          label: address.label || '',
          address_line_1: address.address_line_1 || '',
          address_line_2: address.address_line_2 || '',
          city: address.city || '',
          state: address.state || '',
          postal_code: address.postal_code || '',
          country: address.country || 'India',
          is_default: address.is_default || false,
        })
        
        if (address.latitude && address.longitude) {
          setSelectedLocation({
            lat: parseFloat(address.latitude),
            lng: parseFloat(address.longitude)
          })
        }
      } else {
        // Creating new address
        reset({
          label: '',
          address_line_1: '',
          address_line_2: '',
          city: '',
          state: '',
          postal_code: '',
          country: 'India',
          is_default: false,
        })
        setSelectedLocation(null)
      }
    }
  }, [isOpen, address, reset])

  const onFormSubmit = async (data) => {
    try {
      setLoading(true)
      
      const addressData = {
        ...data,
        latitude: selectedLocation?.lat || null,
        longitude: selectedLocation?.lng || null,
      }
      
      await onSubmit(addressData)
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLocationSelect = (location, addressComponents) => {
    setSelectedLocation(location)
    
    // Auto-fill address fields from Google Maps
    if (addressComponents) {
      const components = addressComponents.reduce((acc, component) => {
        const types = component.types
        if (types.includes('street_number')) {
          acc.street_number = component.long_name
        } else if (types.includes('route')) {
          acc.route = component.long_name
        } else if (types.includes('locality')) {
          acc.city = component.long_name
        } else if (types.includes('administrative_area_level_1')) {
          acc.state = component.long_name
        } else if (types.includes('postal_code')) {
          acc.postal_code = component.long_name
        } else if (types.includes('country')) {
          acc.country = component.long_name
        }
        return acc
      }, {})

      // Update form fields
      if (components.street_number && components.route) {
        setValue('address_line_1', `${components.street_number} ${components.route}`)
      } else if (components.route) {
        setValue('address_line_1', components.route)
      }
      
      if (components.city) setValue('city', components.city)
      if (components.state) setValue('state', components.state)
      if (components.postal_code) setValue('postal_code', components.postal_code)
      if (components.country) setValue('country', components.country)
    }
  }

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          }
          setSelectedLocation(location)
          setShowMap(true)
        },
        (error) => {
          console.error('Error getting location:', error)
          alert('Unable to get your current location. Please select manually on the map.')
          setShowMap(true)
        }
      )
    } else {
      alert('Geolocation is not supported by this browser.')
      setShowMap(true)
    }
  }

  const labelOptions = [
    { value: 'Home', icon: Home, color: 'text-blue-600' },
    { value: 'Office', icon: Building, color: 'text-green-600' },
    { value: 'Other', icon: MapPin, color: 'text-gray-600' },
  ]

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              {address ? 'Edit Address' : 'Add New Address'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {showMap ? (
              <div className="mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium text-gray-900">
                    Select Location on Map
                  </h4>
                  <button
                    onClick={() => setShowMap(false)}
                    className="text-sm text-primary-600 hover:text-primary-700"
                  >
                    Close Map
                  </button>
                </div>
                <GoogleMapsPicker
                  onLocationSelect={handleLocationSelect}
                  initialLocation={selectedLocation}
                />
              </div>
            ) : (
              <div className="mb-6">
                <div className="flex items-center space-x-4">
                  <button
                    type="button"
                    onClick={getCurrentLocation}
                    className="btn btn-outline btn-sm"
                  >
                    <Navigation className="w-4 h-4 mr-2" />
                    Use Current Location
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowMap(true)}
                    className="btn btn-outline btn-sm"
                  >
                    <MapPin className="w-4 h-4 mr-2" />
                    Select on Map
                  </button>
                </div>
                {selectedLocation && (
                  <p className="text-sm text-green-600 mt-2">
                    ✓ Location selected on map
                  </p>
                )}
              </div>
            )}

            <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
              {/* Address Label */}
              <div>
                <label className="form-label">Address Label</label>
                <div className="grid grid-cols-3 gap-3 mt-2">
                  {labelOptions.map((option) => (
                    <label
                      key={option.value}
                      className={`relative flex items-center justify-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                        watchedFields.label === option.value
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-300'
                      }`}
                    >
                      <input
                        type="radio"
                        value={option.value}
                        className="sr-only"
                        {...register('label')}
                      />
                      <option.icon className={`w-5 h-5 mr-2 ${option.color}`} />
                      <span className="text-sm font-medium">{option.value}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Address Line 1 */}
              <div>
                <label htmlFor="address_line_1" className="form-label">
                  Address Line 1 *
                </label>
                <input
                  id="address_line_1"
                  type="text"
                  className={`input ${errors.address_line_1 ? 'input-error' : ''}`}
                  placeholder="House/Flat number, Building name, Street"
                  {...register('address_line_1', {
                    required: 'Address line 1 is required'
                  })}
                />
                {errors.address_line_1 && (
                  <p className="form-error">{errors.address_line_1.message}</p>
                )}
              </div>

              {/* Address Line 2 */}
              <div>
                <label htmlFor="address_line_2" className="form-label">
                  Address Line 2
                </label>
                <input
                  id="address_line_2"
                  type="text"
                  className="input"
                  placeholder="Landmark, Area (Optional)"
                  {...register('address_line_2')}
                />
              </div>

              {/* City, State, Postal Code */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="city" className="form-label">
                    City *
                  </label>
                  <input
                    id="city"
                    type="text"
                    className={`input ${errors.city ? 'input-error' : ''}`}
                    placeholder="City"
                    {...register('city', {
                      required: 'City is required'
                    })}
                  />
                  {errors.city && (
                    <p className="form-error">{errors.city.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="state" className="form-label">
                    State *
                  </label>
                  <input
                    id="state"
                    type="text"
                    className={`input ${errors.state ? 'input-error' : ''}`}
                    placeholder="State"
                    {...register('state', {
                      required: 'State is required'
                    })}
                  />
                  {errors.state && (
                    <p className="form-error">{errors.state.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="postal_code" className="form-label">
                    Postal Code *
                  </label>
                  <input
                    id="postal_code"
                    type="text"
                    className={`input ${errors.postal_code ? 'input-error' : ''}`}
                    placeholder="PIN Code"
                    {...register('postal_code', {
                      required: 'Postal code is required',
                      pattern: {
                        value: /^[1-9][0-9]{5}$/,
                        message: 'Invalid postal code'
                      }
                    })}
                  />
                  {errors.postal_code && (
                    <p className="form-error">{errors.postal_code.message}</p>
                  )}
                </div>
              </div>

              {/* Country */}
              <div>
                <label htmlFor="country" className="form-label">
                  Country
                </label>
                <input
                  id="country"
                  type="text"
                  className="input"
                  {...register('country')}
                />
              </div>

              {/* Default Address */}
              <div className="flex items-center">
                <input
                  id="is_default"
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  {...register('is_default')}
                />
                <label htmlFor="is_default" className="ml-2 flex items-center text-sm text-gray-900">
                  <Star className="w-4 h-4 mr-1 text-yellow-500" />
                  Set as default address
                </label>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="btn btn-outline btn-md"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="btn btn-primary btn-md"
                >
                  {loading ? (
                    <LoadingSpinner size="sm" color="white" />
                  ) : (
                    <>
                      {address ? 'Update' : 'Save'} Address
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AddressModal
