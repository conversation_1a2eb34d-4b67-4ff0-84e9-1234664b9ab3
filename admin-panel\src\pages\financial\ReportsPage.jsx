import React from 'react'
import { Helmet } from 'react-helmet-async'
import { ChartBarIcon } from '@heroicons/react/24/outline'
import { Card, CardHeader, CardTitle, CardContent } from '@components/ui/Card'

const ReportsPage = () => {
  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Helmet>
        <title>Reports - WorkBoy Admin</title>
      </Helmet>
      
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
            <ChartBarIcon className="mr-3 h-8 w-8 text-primary-600" />
            Reports
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            View financial reports and analytics
          </p>
        </div>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Financial Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No reports available</h3>
              <p className="mt-1 text-sm text-gray-500">
                Financial reports will appear here when data is available.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default ReportsPage
