import { apiService, endpoints } from './apiService'

/**
 * Dashboard Service
 * Handles all dashboard-related API calls and data processing
 */
export const dashboardService = {
  /**
   * Get dashboard statistics
   */
  async getDashboardStats() {
    try {
      // For now, return mock data since backend might not be fully ready
      // TODO: Replace with actual API call when backend is ready
      // const response = await apiService.get(endpoints.admin.dashboard)
      
      return {
        success: true,
        data: {
          totalUsers: 1250,
          totalCustomers: 850,
          totalWorkBoys: 400,
          totalTasks: 3420,
          pendingTasks: 45,
          completedTasks: 3200,
          cancelledTasks: 175,
          totalRevenue: 125000,
          monthlyRevenue: 15000,
          pendingPayments: 2500,
          activeWorkBoys: 320,
          newUsersThisMonth: 85,
          tasksCompletedToday: 23,
          averageRating: 4.7,
          conversionRate: 0.68
        }
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error)
      throw error
    }
  },

  /**
   * Get analytics data
   */
  async getAnalytics(period = '30d') {
    try {
      // Mock analytics data
      // TODO: Replace with actual API call
      // const response = await apiService.get(`${endpoints.admin.analytics}?period=${period}`)
      
      return {
        success: true,
        data: {
          userGrowth: [
            { date: '2024-01-01', customers: 100, workboys: 50 },
            { date: '2024-01-02', customers: 105, workboys: 52 },
            { date: '2024-01-03', customers: 110, workboys: 55 },
            { date: '2024-01-04', customers: 115, workboys: 58 },
            { date: '2024-01-05', customers: 120, workboys: 60 },
            { date: '2024-01-06', customers: 125, workboys: 62 },
            { date: '2024-01-07', customers: 130, workboys: 65 }
          ],
          revenueData: [
            { month: 'Jan', revenue: 12000, tasks: 150 },
            { month: 'Feb', revenue: 15000, tasks: 180 },
            { month: 'Mar', revenue: 18000, tasks: 220 },
            { month: 'Apr', revenue: 16000, tasks: 200 },
            { month: 'May', revenue: 20000, tasks: 250 },
            { month: 'Jun', revenue: 22000, tasks: 280 }
          ],
          tasksByCategory: [
            { name: 'Cleaning', value: 35, count: 1200 },
            { name: 'Plumbing', value: 25, count: 850 },
            { name: 'Electrical', value: 20, count: 680 },
            { name: 'Gardening', value: 15, count: 510 },
            { name: 'Others', value: 5, count: 180 }
          ]
        }
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
      throw error
    }
  },

  /**
   * Get recent activities
   */
  async getRecentActivities(limit = 10) {
    try {
      // Mock recent activities
      // TODO: Replace with actual API call
      
      const activities = [
        {
          id: 1,
          type: 'user',
          title: 'New customer registered',
          description: 'John Doe joined as a customer',
          timestamp: new Date(Date.now() - 1000 * 60 * 15),
          user: 'John Doe'
        },
        {
          id: 2,
          type: 'task',
          title: 'Task completed',
          description: 'House cleaning task completed by Mike Wilson',
          timestamp: new Date(Date.now() - 1000 * 60 * 30),
          user: 'Mike Wilson'
        },
        {
          id: 3,
          type: 'payment',
          title: 'Payment processed',
          description: 'Payment of $150 processed successfully',
          timestamp: new Date(Date.now() - 1000 * 60 * 45),
          amount: 150
        },
        {
          id: 4,
          type: 'user',
          title: 'WorkBoy verified',
          description: 'Sarah Johnson KYC verification approved',
          timestamp: new Date(Date.now() - 1000 * 60 * 60),
          user: 'Sarah Johnson'
        },
        {
          id: 5,
          type: 'task',
          title: 'New task created',
          description: 'Plumbing repair task posted by Alex Brown',
          timestamp: new Date(Date.now() - 1000 * 60 * 90),
          user: 'Alex Brown'
        }
      ]

      return {
        success: true,
        data: activities.slice(0, limit)
      }
    } catch (error) {
      console.error('Failed to fetch recent activities:', error)
      throw error
    }
  },

  /**
   * Get top performing WorkBoys
   */
  async getTopWorkBoys(limit = 5) {
    try {
      // Mock top WorkBoys data
      // TODO: Replace with actual API call
      
      const workboys = [
        {
          id: 1,
          name: 'Mike Wilson',
          avatar: null,
          rating: 4.9,
          completedTasks: 156,
          earnings: 12450,
          category: 'Cleaning',
          status: 'active'
        },
        {
          id: 2,
          name: 'Sarah Johnson',
          avatar: null,
          rating: 4.8,
          completedTasks: 142,
          earnings: 11200,
          category: 'Plumbing',
          status: 'active'
        },
        {
          id: 3,
          name: 'David Chen',
          avatar: null,
          rating: 4.7,
          completedTasks: 128,
          earnings: 9800,
          category: 'Electrical',
          status: 'active'
        },
        {
          id: 4,
          name: 'Lisa Rodriguez',
          avatar: null,
          rating: 4.6,
          completedTasks: 115,
          earnings: 8900,
          category: 'Gardening',
          status: 'active'
        },
        {
          id: 5,
          name: 'James Thompson',
          avatar: null,
          rating: 4.5,
          completedTasks: 98,
          earnings: 7650,
          category: 'Carpentry',
          status: 'active'
        }
      ]

      return {
        success: true,
        data: workboys.slice(0, limit)
      }
    } catch (error) {
      console.error('Failed to fetch top WorkBoys:', error)
      throw error
    }
  }
}

export default dashboardService
