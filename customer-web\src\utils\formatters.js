/**
 * Format a date string or Date object to a readable format
 * @param {string|Date} date - The date to format
 * @param {string} locale - The locale to use (default: 'en-US')
 * @returns {string} Formatted date string
 */
export const formatDate = (date, locale = 'en-US') => {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(dateObj.getTime())) return ''
  
  return dateObj.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

/**
 * Format a time string or Date object to a readable format
 * @param {string|Date} time - The time to format
 * @param {string} locale - The locale to use (default: 'en-US')
 * @returns {string} Formatted time string
 */
export const formatTime = (time, locale = 'en-US') => {
  if (!time) return ''
  
  let timeObj
  
  if (typeof time === 'string') {
    // Handle time strings like "14:30:00" or full datetime strings
    if (time.includes(':') && !time.includes('T')) {
      // Time only string
      const today = new Date()
      const [hours, minutes] = time.split(':')
      timeObj = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes)
    } else {
      // Full datetime string
      timeObj = new Date(time)
    }
  } else {
    timeObj = time
  }
  
  if (isNaN(timeObj.getTime())) return ''
  
  return timeObj.toLocaleTimeString(locale, {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
}

/**
 * Format a date and time together
 * @param {string|Date} datetime - The datetime to format
 * @param {string} locale - The locale to use (default: 'en-US')
 * @returns {string} Formatted datetime string
 */
export const formatDateTime = (datetime, locale = 'en-US') => {
  if (!datetime) return ''
  
  const dateObj = typeof datetime === 'string' ? new Date(datetime) : datetime
  
  if (isNaN(dateObj.getTime())) return ''
  
  return dateObj.toLocaleString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
}

/**
 * Format a currency amount
 * @param {number|string} amount - The amount to format
 * @param {string} currency - The currency code (default: 'USD')
 * @param {string} locale - The locale to use (default: 'en-US')
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = 'USD', locale = 'en-US') => {
  if (amount === null || amount === undefined || amount === '') return ''
  
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (isNaN(numAmount)) return ''
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numAmount)
}

/**
 * Format a number with thousand separators
 * @param {number|string} number - The number to format
 * @param {string} locale - The locale to use (default: 'en-US')
 * @returns {string} Formatted number string
 */
export const formatNumber = (number, locale = 'en-US') => {
  if (number === null || number === undefined || number === '') return ''
  
  const numValue = typeof number === 'string' ? parseFloat(number) : number
  
  if (isNaN(numValue)) return ''
  
  return new Intl.NumberFormat(locale).format(numValue)
}

/**
 * Format a duration in minutes to a readable format
 * @param {number} minutes - Duration in minutes
 * @returns {string} Formatted duration string
 */
export const formatDuration = (minutes) => {
  if (!minutes || minutes <= 0) return ''
  
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  
  if (hours === 0) {
    return `${remainingMinutes} min${remainingMinutes !== 1 ? 's' : ''}`
  } else if (remainingMinutes === 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`
  } else {
    return `${hours}h ${remainingMinutes}m`
  }
}

/**
 * Format a relative time (e.g., "2 hours ago", "in 3 days")
 * @param {string|Date} date - The date to compare
 * @param {string|Date} baseDate - The base date to compare against (default: now)
 * @returns {string} Relative time string
 */
export const formatRelativeTime = (date, baseDate = new Date()) => {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const baseObj = typeof baseDate === 'string' ? new Date(baseDate) : baseDate
  
  if (isNaN(dateObj.getTime()) || isNaN(baseObj.getTime())) return ''
  
  const diffMs = dateObj.getTime() - baseObj.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  
  if (Math.abs(diffSeconds) < 60) {
    return 'just now'
  } else if (Math.abs(diffMinutes) < 60) {
    const unit = Math.abs(diffMinutes) === 1 ? 'minute' : 'minutes'
    return diffMinutes > 0 ? `in ${Math.abs(diffMinutes)} ${unit}` : `${Math.abs(diffMinutes)} ${unit} ago`
  } else if (Math.abs(diffHours) < 24) {
    const unit = Math.abs(diffHours) === 1 ? 'hour' : 'hours'
    return diffHours > 0 ? `in ${Math.abs(diffHours)} ${unit}` : `${Math.abs(diffHours)} ${unit} ago`
  } else {
    const unit = Math.abs(diffDays) === 1 ? 'day' : 'days'
    return diffDays > 0 ? `in ${Math.abs(diffDays)} ${unit}` : `${Math.abs(diffDays)} ${unit} ago`
  }
}

/**
 * Truncate text to a specified length
 * @param {string} text - The text to truncate
 * @param {number} maxLength - Maximum length (default: 100)
 * @param {string} suffix - Suffix to add when truncated (default: '...')
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength = 100, suffix = '...') => {
  if (!text || text.length <= maxLength) return text || ''
  
  return text.substring(0, maxLength - suffix.length) + suffix
}

/**
 * Capitalize the first letter of a string
 * @param {string} str - The string to capitalize
 * @returns {string} Capitalized string
 */
export const capitalize = (str) => {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * Format a phone number
 * @param {string} phone - The phone number to format
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phone) => {
  if (!phone) return ''
  
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Format as (XXX) XXX-XXXX for US numbers
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
  }
  
  // Return original if not a standard format
  return phone
}
