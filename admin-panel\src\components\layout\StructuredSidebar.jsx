import React, { Fragment, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon, ChevronRightIcon, ChevronDownIcon } from '@heroicons/react/24/outline'
import {
  HomeIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  CreditCardIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  TagIcon,
  BanknotesIcon,
  CurrencyDollarIcon,
  DocumentChartBarIcon,
  ArrowRightOnRectangleIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline'
import { useAuth } from '@context/AuthContext'
import { clsx } from 'clsx'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
    badge: null,
    description: 'Overview & Analytics'
  },
  {
    name: 'User Management',
    icon: UsersIcon,
    description: 'Manage customers & work-boys',
    children: [
      { name: 'Customers', href: '/users', icon: UsersIcon, badge: '2.4k' },
      { name: 'Work-Boys', href: '/workboys', icon: UserGroupIcon, badge: '156' },
      { name: 'KYC Verification', href: '/kyc-verification', icon: ShieldCheckIcon, badge: '23' },
    ],
  },
  {
    name: 'Task Management',
    icon: ClipboardDocumentListIcon,
    description: 'Tasks & Categories',
    children: [
      { name: 'All Tasks', href: '/tasks', icon: ClipboardDocumentListIcon, badge: '1.2k' },
      { name: 'Categories', href: '/categories', icon: TagIcon, badge: '12' },
    ],
  },
  {
    name: 'Financial',
    icon: CurrencyDollarIcon,
    description: 'Payments & Reports',
    children: [
      { name: 'Payments', href: '/payments', icon: CreditCardIcon, badge: '₹91k' },
      { name: 'Transactions', href: '/transactions', icon: BanknotesIcon, badge: '456' },
      { name: 'Reports', href: '/reports', icon: DocumentChartBarIcon, badge: null },
    ],
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: ChartBarIcon,
    badge: null,
    description: 'Performance Insights'
  },
  {
    name: 'Settings',
    icon: Cog6ToothIcon,
    description: 'System Configuration',
    children: [
      { name: 'General Settings', href: '/settings', icon: Cog6ToothIcon, badge: null },
      { name: 'Admin Users', href: '/admin-users', icon: ShieldCheckIcon, badge: '5' },
    ],
  },
]

export const StructuredSidebar = ({ open, setOpen }) => {
  const location = useLocation()
  const { user, logout } = useAuth()
  const [expandedItems, setExpandedItems] = useState({})

  const isActive = (href) => {
    return location.pathname === href
  }

  const isParentActive = (children) => {
    return children?.some(child => location.pathname === child.href)
  }

  const toggleExpanded = (itemName) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemName]: !prev[itemName]
    }))
  }

  // Auto-expand parent if child is active
  React.useEffect(() => {
    navigation.forEach(item => {
      if (item.children && isParentActive(item.children)) {
        setExpandedItems(prev => ({
          ...prev,
          [item.name]: true
        }))
      }
    })
  }, [location.pathname])

  const SidebarContent = () => (
    <div className="flex h-full flex-col bg-gradient-to-b from-white via-gray-50/30 to-white border-r border-gray-200/60 backdrop-blur-xl">
      {/* Logo */}
      <div className="flex h-18 shrink-0 items-center px-6 border-b border-gray-200/60 bg-gradient-to-r from-primary-50/50 to-brand-50/30">
        <div className="flex items-center group">
          <div className="relative">
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary-600 via-primary-500 to-brand-600 flex items-center justify-center shadow-lg shadow-primary-500/25 group-hover:shadow-primary-500/40 transition-all duration-300">
              <SparklesIcon className="h-5 w-5 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 h-3 w-3 bg-gradient-to-br from-accent-400 to-accent-500 rounded-full animate-pulse"></div>
          </div>
          <div className="ml-4">
            <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 via-primary-800 to-brand-700 bg-clip-text text-transparent">
              WorkBoy
            </h1>
            <p className="text-xs text-gray-500 font-medium tracking-wide">Admin Dashboard</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex flex-1 flex-col px-4 py-6 space-y-2 overflow-y-auto scrollbar-hide">
        {navigation.map((item) => (
          <div key={item.name} className="group">
            {!item.children ? (
              <Link
                to={item.href}
                className={clsx(
                  'group flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 relative overflow-hidden',
                  isActive(item.href)
                    ? 'bg-gradient-to-r from-primary-500 to-brand-500 text-white shadow-lg shadow-primary-500/25 scale-[1.02]'
                    : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-primary-50/30 hover:text-primary-700 hover:scale-[1.01]'
                )}
              >
                <div className="flex items-center">
                  <div className={clsx(
                    'p-2 rounded-lg mr-3 transition-all duration-300',
                    isActive(item.href)
                      ? 'bg-white/20 text-white'
                      : 'bg-gray-100 text-gray-500 group-hover:bg-primary-100 group-hover:text-primary-600'
                  )}>
                    <item.icon className="h-4 w-4" />
                  </div>
                  <div>
                    <div className="font-semibold">{item.name}</div>
                    {item.description && (
                      <div className={clsx(
                        'text-xs mt-0.5 transition-colors duration-300',
                        isActive(item.href) ? 'text-white/80' : 'text-gray-500 group-hover:text-primary-600/80'
                      )}>
                        {item.description}
                      </div>
                    )}
                  </div>
                </div>
                {item.badge && (
                  <span className={clsx(
                    'px-2 py-1 text-xs font-bold rounded-full transition-all duration-300',
                    isActive(item.href)
                      ? 'bg-white/20 text-white'
                      : 'bg-primary-100 text-primary-700 group-hover:bg-primary-200'
                  )}>
                    {item.badge}
                  </span>
                )}
              </Link>
            ) : (
              <div className="space-y-1">
                <button
                  onClick={() => toggleExpanded(item.name)}
                  className={clsx(
                    'w-full group flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 relative overflow-hidden',
                    isParentActive(item.children)
                      ? 'bg-gradient-to-r from-primary-50 to-brand-50/50 text-primary-700 border border-primary-200/50'
                      : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-primary-50/30 hover:text-primary-700'
                  )}
                >
                  <div className="flex items-center">
                    <div className={clsx(
                      'p-2 rounded-lg mr-3 transition-all duration-300',
                      isParentActive(item.children)
                        ? 'bg-primary-100 text-primary-600'
                        : 'bg-gray-100 text-gray-500 group-hover:bg-primary-100 group-hover:text-primary-600'
                    )}>
                      <item.icon className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-semibold">{item.name}</div>
                      {item.description && (
                        <div className={clsx(
                          'text-xs mt-0.5 transition-colors duration-300',
                          isParentActive(item.children) ? 'text-primary-600/80' : 'text-gray-500 group-hover:text-primary-600/80'
                        )}>
                          {item.description}
                        </div>
                      )}
                    </div>
                  </div>
                  <ChevronDownIcon
                    className={clsx(
                      'h-4 w-4 transition-transform duration-300',
                      expandedItems[item.name] ? 'rotate-180' : 'rotate-0',
                      isParentActive(item.children) ? 'text-primary-600' : 'text-gray-400'
                    )}
                  />
                </button>

                <Transition
                  show={expandedItems[item.name] || isParentActive(item.children)}
                  enter="transition-all duration-300 ease-out"
                  enterFrom="opacity-0 max-h-0"
                  enterTo="opacity-100 max-h-96"
                  leave="transition-all duration-300 ease-in"
                  leaveFrom="opacity-100 max-h-96"
                  leaveTo="opacity-0 max-h-0"
                >
                  <div className="ml-6 mt-2 space-y-1 border-l-2 border-gray-100 pl-4">
                    {item.children.map((subItem) => (
                      <Link
                        key={subItem.name}
                        to={subItem.href}
                        className={clsx(
                          'group flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-300',
                          isActive(subItem.href)
                            ? 'bg-gradient-to-r from-primary-500 to-brand-500 text-white shadow-md shadow-primary-500/20 scale-[1.02]'
                            : 'text-gray-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-brand-50/30 hover:text-primary-700 hover:scale-[1.01]'
                        )}
                      >
                        <div className="flex items-center">
                          {subItem.icon && (
                            <div className={clsx(
                              'p-1.5 rounded-md mr-3 transition-all duration-300',
                              isActive(subItem.href)
                                ? 'bg-white/20 text-white'
                                : 'bg-gray-100 text-gray-500 group-hover:bg-primary-100 group-hover:text-primary-600'
                            )}>
                              <subItem.icon className="h-3.5 w-3.5" />
                            </div>
                          )}
                          <span>{subItem.name}</span>
                        </div>
                        {subItem.badge && (
                          <span className={clsx(
                            'px-2 py-0.5 text-xs font-bold rounded-full transition-all duration-300',
                            isActive(subItem.href)
                              ? 'bg-white/20 text-white'
                              : 'bg-primary-100 text-primary-700 group-hover:bg-primary-200'
                          )}>
                            {subItem.badge}
                          </span>
                        )}
                      </Link>
                    ))}
                  </div>
                </Transition>
              </div>
            )}
          </div>
        ))}
      </nav>

      {/* User Profile */}
      <div className="border-t border-gray-200/60 p-4 bg-gradient-to-r from-gray-50/50 to-primary-50/20">
        <div className="flex items-center group">
          <div className="relative">
            <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-primary-600 via-primary-500 to-brand-600 flex items-center justify-center shadow-lg shadow-primary-500/25 group-hover:shadow-primary-500/40 transition-all duration-300">
              <span className="text-white font-bold text-lg">
                {user?.name?.charAt(0) || 'A'}
              </span>
            </div>
            <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-gradient-to-br from-success-400 to-success-500 rounded-full border-2 border-white animate-pulse"></div>
          </div>
          <div className="ml-4 flex-1">
            <p className="text-sm font-bold text-gray-900 group-hover:text-primary-700 transition-colors duration-300">
              {user?.name || 'Admin User'}
            </p>
            <p className="text-xs text-gray-500 font-medium">
              {user?.email || '<EMAIL>'}
            </p>
            <div className="flex items-center mt-1">
              <div className="h-2 w-2 bg-success-400 rounded-full mr-2 animate-pulse"></div>
              <span className="text-xs text-success-600 font-medium">Online</span>
            </div>
          </div>
          <button
            onClick={logout}
            className="p-2 rounded-lg text-gray-400 hover:text-danger-600 hover:bg-danger-50 transition-all duration-300 group-hover:scale-110"
            title="Sign out"
          >
            <ArrowRightOnRectangleIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5 text-white"
                      onClick={() => setOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <SidebarContent />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <SidebarContent />
      </div>
    </>
  )
}
