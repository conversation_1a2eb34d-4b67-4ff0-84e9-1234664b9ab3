import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Avatar, Card, List, Button, Divider } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@context/AuthContext';
import { colors, spacing, typography } from '@constants/theme';

const ProfileScreen = ({ navigation }) => {
  const { user, userProfile, logout, loading } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate refresh - in real app, you'd fetch updated profile data
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: logout,
        },
      ]
    );
  };

  const profileMenuItems = [
    {
      title: 'Edit Profile',
      icon: 'person-outline',
      onPress: () => navigation.navigate('EditProfile'),
    },
    {
      title: 'My Addresses',
      icon: 'location-outline',
      onPress: () => navigation.navigate('Addresses'),
    },
    {
      title: 'Payment Methods',
      icon: 'card-outline',
      onPress: () => navigation.navigate('Payments'),
    },
    {
      title: 'Notifications',
      icon: 'notifications-outline',
      onPress: () => navigation.navigate('Notifications'),
    },
    {
      title: 'Settings',
      icon: 'settings-outline',
      onPress: () => navigation.navigate('Settings'),
    },
    {
      title: 'Help & Support',
      icon: 'help-circle-outline',
      onPress: () => navigation.navigate('Help'),
    },
  ];

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Profile Header */}
        <Card style={styles.profileCard}>
          <Card.Content style={styles.profileContent}>
            <View style={styles.avatarContainer}>
              <Avatar.Text
                size={80}
                label={getInitials(userProfile?.first_name, userProfile?.last_name)}
                style={styles.avatar}
                labelStyle={styles.avatarLabel}
              />
              <TouchableOpacity
                style={styles.editAvatarButton}
                onPress={() => {
                  Alert.alert('Change Photo', 'Photo change functionality would be implemented here.');
                }}
              >
                <Ionicons name="camera" size={16} color={colors.white} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.profileInfo}>
              <Text style={styles.userName}>
                {userProfile?.first_name} {userProfile?.last_name}
              </Text>
              <Text style={styles.userEmail}>{userProfile?.email}</Text>
              <Text style={styles.userPhone}>{userProfile?.phone}</Text>
            </View>
          </Card.Content>
        </Card>

        {/* Profile Stats */}
        <Card style={styles.statsCard}>
          <Card.Content>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>12</Text>
                <Text style={styles.statLabel}>Completed Tasks</Text>
              </View>
              <Divider style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>4.8</Text>
                <Text style={styles.statLabel}>Average Rating</Text>
              </View>
              <Divider style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>3</Text>
                <Text style={styles.statLabel}>Active Tasks</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Menu Items */}
        <Card style={styles.menuCard}>
          <Card.Content style={styles.menuContent}>
            {profileMenuItems.map((item, index) => (
              <React.Fragment key={item.title}>
                <List.Item
                  title={item.title}
                  left={(props) => (
                    <List.Icon
                      {...props}
                      icon={({ size, color }) => (
                        <Ionicons name={item.icon} size={size} color={color} />
                      )}
                    />
                  )}
                  right={(props) => (
                    <List.Icon {...props} icon="chevron-forward" />
                  )}
                  onPress={item.onPress}
                  style={styles.menuItem}
                />
                {index < profileMenuItems.length - 1 && (
                  <Divider style={styles.menuDivider} />
                )}
              </React.Fragment>
            ))}
          </Card.Content>
        </Card>

        {/* Logout Button */}
        <Button
          mode="outlined"
          onPress={handleLogout}
          loading={loading}
          disabled={loading}
          style={styles.logoutButton}
          contentStyle={styles.logoutButtonContent}
          labelStyle={styles.logoutButtonLabel}
          icon={({ size, color }) => (
            <Ionicons name="log-out-outline" size={size} color={color} />
          )}
        >
          Logout
        </Button>

        {/* App Version */}
        <Text style={styles.versionText}>Version 1.0.0</Text>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  profileCard: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  profileContent: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: spacing.md,
  },
  avatar: {
    backgroundColor: colors.primary[600],
  },
  avatarLabel: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.bold,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: colors.primary[600],
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    alignItems: 'center',
  },
  userName: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  userEmail: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    marginBottom: spacing.xs,
  },
  userPhone: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  statsCard: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.bold,
    color: colors.primary[600],
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: colors.gray[200],
  },
  menuCard: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  menuContent: {
    paddingVertical: spacing.xs,
  },
  menuItem: {
    paddingVertical: spacing.sm,
  },
  menuDivider: {
    marginLeft: spacing.xl,
  },
  logoutButton: {
    marginBottom: spacing.md,
    borderColor: colors.error[500],
  },
  logoutButtonContent: {
    paddingVertical: spacing.sm,
  },
  logoutButtonLabel: {
    color: colors.error[500],
    fontFamily: typography.fontFamily.medium,
  },
  versionText: {
    textAlign: 'center',
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    marginTop: spacing.md,
  },
});

export default ProfileScreen;
