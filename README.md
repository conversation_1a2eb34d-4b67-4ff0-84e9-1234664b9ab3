# Work-Boy Booking Platform

A comprehensive platform connecting busy professionals with trusted individuals (Work-Boys) for personal tasks and errands.

## 🏗️ Project Structure

```
work-boy-booking/
├── backend/                    # PHP CodeIgniter 4 API
│   ├── app/
│   ├── public/
│   ├── writable/
│   └── composer.json
├── web-app/                    # React.js Customer Web App
│   ├── src/
│   ├── public/
│   └── package.json
├── mobile-app/                 # React Native Apps
│   ├── customer-app/           # Customer Mobile App
│   └── workboy-app/           # Work-Boy Mobile App
├── admin-panel/               # Admin Dashboard
│   ├── src/
│   └── package.json
├── shared/                    # Shared utilities and types
│   ├── types/
│   └── utils/
└── docs/                      # Documentation
    ├── api/
    ├── deployment/
    └── user-guides/
```

## 🎯 User Roles

- **Customer**: Task bookers who need services
- **Work-Boy**: Service providers who complete tasks
- **Admin**: Platform operators and managers

## 🛠️ Tech Stack

### Frontend
- **Mobile Apps**: React Native (iOS & Android)
- **Web App**: React.js + TailwindCSS
- **Admin Panel**: React.js + TailwindCSS

### Backend
- **API**: PHP CodeIgniter 4
- **Database**: MySQL
- **Authentication**: Firebase Auth
- **Real-time**: WebSocket

### Services & APIs
- **Maps**: Google Maps API
- **Payments**: Razorpay
- **Push Notifications**: Firebase Cloud Messaging

## 🚀 Getting Started

### Prerequisites
- Node.js (v16+)
- PHP (v8.0+)
- Composer
- MySQL
- React Native CLI
- Android Studio / Xcode

### Development Setup

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd work-boy-booking
   ```

2. **Backend Setup**
   ```bash
   cd backend
   composer install
   cp env .env
   # Configure database and Firebase credentials
   php spark migrate
   php spark serve
   ```

3. **Web App Setup**
   ```bash
   cd web-app
   npm install
   npm start
   ```

4. **Mobile Apps Setup**
   ```bash
   cd mobile-app/customer-app
   npm install
   npx react-native run-android # or run-ios
   ```

## 📱 Features Overview

### Customer Features
- ✅ Multi-platform authentication (Email, Phone, Google, Apple)
- ✅ Task booking with categories (Grocery, Delivery, Cleaning, Pet Care, Custom)
- ✅ Real-time task tracking with GPS
- ✅ In-app payments with Razorpay
- ✅ Rating and review system
- ✅ Task history and re-booking

### Work-Boy Features
- ✅ KYC verification and approval
- ✅ Task feed and acceptance system
- ✅ Navigation and task completion
- ✅ Earnings tracking and withdrawal
- ✅ Customer feedback system

### Admin Features
- ✅ User and task management
- ✅ Real-time platform monitoring
- ✅ Revenue analytics and reporting
- ✅ Dispute resolution system

## 🔐 Security & Compliance

- JWT/Firebase authentication
- Role-based access control
- GDPR/PII compliance
- PCI-DSS payment security

## 📊 Development Phases

1. **Phase 1**: Project Setup & Backend API
2. **Phase 2**: Customer Web Application
3. **Phase 3**: Mobile Applications
4. **Phase 4**: Admin Panel
5. **Phase 5**: Integration & Testing

## 🎨 Design Guidelines

- Clean, modern, mobile-first UI
- Color scheme: Light blue, soft green, white background
- Rounded corners and prominent CTA buttons
- Intuitive icons for task categories

## 📝 License

[Add your license here]

## 🤝 Contributing

[Add contribution guidelines]
