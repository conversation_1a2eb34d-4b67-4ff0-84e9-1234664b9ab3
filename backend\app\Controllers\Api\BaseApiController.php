<?php

namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * Base API Controller for Work-Boy Booking Platform
 * 
 * This controller provides common functionality for all API endpoints
 * including authentication, response formatting, and error handling.
 */
class BaseApiController extends ResourceController
{
    use ResponseTrait;

    /**
     * Current authenticated user
     */
    protected $currentUser = null;

    /**
     * JWT secret key
     */
    protected $jwtSecret;

    /**
     * Response format
     */
    protected $format = 'json';

    public function __construct()
    {
        $this->jwtSecret = env('JWT_SECRET', 'your-default-secret-key');
    }

    /**
     * Standard API response format
     * 
     * @param mixed $data Response data
     * @param string $message Response message
     * @param int $statusCode HTTP status code
     * @param array $meta Additional metadata
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    protected function apiResponse($data = null, string $message = 'Success', int $statusCode = 200, array $meta = [])
    {
        $response = [
            'success' => $statusCode >= 200 && $statusCode < 300,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return $this->respond($response, $statusCode);
    }

    /**
     * Error response format
     * 
     * @param string $message Error message
     * @param int $statusCode HTTP status code
     * @param array $errors Detailed errors
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    protected function errorResponse(string $message = 'Error', int $statusCode = 400, array $errors = [])
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return $this->respond($response, $statusCode);
    }

    /**
     * Validation error response
     * 
     * @param array $errors Validation errors
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    protected function validationErrorResponse(array $errors)
    {
        return $this->errorResponse('Validation failed', 422, $errors);
    }

    /**
     * Unauthorized response
     * 
     * @param string $message Error message
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    protected function unauthorizedResponse(string $message = 'Unauthorized')
    {
        return $this->errorResponse($message, 401);
    }

    /**
     * Forbidden response
     * 
     * @param string $message Error message
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    protected function forbiddenResponse(string $message = 'Forbidden')
    {
        return $this->errorResponse($message, 403);
    }

    /**
     * Not found response
     * 
     * @param string $message Error message
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    protected function notFoundResponse(string $message = 'Resource not found')
    {
        return $this->errorResponse($message, 404);
    }

    /**
     * Server error response
     * 
     * @param string $message Error message
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    protected function serverErrorResponse(string $message = 'Internal server error')
    {
        return $this->errorResponse($message, 500);
    }

    /**
     * Get JWT token from request header
     * 
     * @return string|null
     */
    protected function getJWTFromRequest()
    {
        $header = $this->request->getHeader('Authorization');
        
        if ($header && !empty($header->getValue())) {
            $authHeader = $header->getValue();
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }

    /**
     * Verify JWT token and get user data
     * 
     * @param string $token JWT token
     * @return array|null User data or null if invalid
     */
    protected function verifyJWT(string $token)
    {
        try {
            $decoded = JWT::decode($token, new Key($this->jwtSecret, 'HS256'));
            return (array) $decoded;
        } catch (\Exception $e) {
            log_message('error', 'JWT verification failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate JWT token for user
     * 
     * @param array $userData User data to encode
     * @param int $expireTime Token expiration time (default: 24 hours)
     * @return string JWT token
     */
    protected function generateJWT(array $userData, int $expireTime = null)
    {
        $expireTime = $expireTime ?? (time() + (int) env('JWT_EXPIRE_TIME', 86400));
        
        $payload = array_merge($userData, [
            'iat' => time(),
            'exp' => $expireTime,
        ]);

        return JWT::encode($payload, $this->jwtSecret, 'HS256');
    }

    /**
     * Get current authenticated user
     * 
     * @return array|null
     */
    protected function getCurrentUser()
    {
        return $this->currentUser;
    }

    /**
     * Set current authenticated user
     * 
     * @param array $user User data
     */
    protected function setCurrentUser(array $user)
    {
        $this->currentUser = $user;
    }

    /**
     * Check if user has required role
     * 
     * @param string $requiredRole Required role
     * @return bool
     */
    protected function hasRole(string $requiredRole): bool
    {
        if (!$this->currentUser) {
            return false;
        }

        return isset($this->currentUser['user_type']) && 
               $this->currentUser['user_type'] === $requiredRole;
    }

    /**
     * Validate request data
     * 
     * @param array $rules Validation rules
     * @param array $data Data to validate (optional, uses request data if not provided)
     * @return bool|array True if valid, array of errors if invalid
     */
    protected function validateRequest(array $rules, array $data = null)
    {
        $validation = \Config\Services::validation();
        
        $data = $data ?? $this->request->getJSON(true) ?? $this->request->getPost();
        
        if (!$validation->setRules($rules)->run($data)) {
            return $validation->getErrors();
        }
        
        return true;
    }

    /**
     * Get pagination parameters from request
     * 
     * @return array
     */
    protected function getPaginationParams(): array
    {
        $page = max(1, (int) $this->request->getGet('page') ?: 1);
        $limit = min(100, max(1, (int) $this->request->getGet('limit') ?: 20));
        $offset = ($page - 1) * $limit;

        return [
            'page' => $page,
            'limit' => $limit,
            'offset' => $offset,
        ];
    }

    /**
     * Create pagination metadata
     * 
     * @param int $total Total records
     * @param int $page Current page
     * @param int $limit Records per page
     * @return array
     */
    protected function createPaginationMeta(int $total, int $page, int $limit): array
    {
        $totalPages = ceil($total / $limit);
        
        return [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => $total,
            'total_pages' => $totalPages,
            'has_next' => $page < $totalPages,
            'has_prev' => $page > 1,
        ];
    }

    /**
     * Log API activity
     * 
     * @param string $action Action performed
     * @param array $data Additional data to log
     */
    protected function logActivity(string $action, array $data = [])
    {
        $logData = [
            'user_id' => $this->currentUser['id'] ?? null,
            'action' => $action,
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'timestamp' => date('Y-m-d H:i:s'),
            'data' => $data,
        ];

        log_message('info', 'API Activity: ' . json_encode($logData));
    }
}
