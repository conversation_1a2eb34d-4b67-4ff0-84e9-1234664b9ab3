import React from 'react'
import { clsx } from 'clsx'
import { LoadingSpinner } from './LoadingSpinner'

export const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  className = '',
  type = 'button',
  icon: Icon,
  iconPosition = 'left',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 transform active:scale-95 relative overflow-hidden'

  const variantClasses = {
    primary: 'bg-gradient-to-r from-primary-600 to-primary-700 text-white hover:from-primary-700 hover:to-primary-800 focus:ring-primary-500 disabled:from-primary-300 disabled:to-primary-300 shadow-md hover:shadow-lg',
    secondary: 'bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-900 hover:from-secondary-200 hover:to-secondary-300 focus:ring-secondary-500 disabled:from-secondary-50 disabled:to-secondary-50 border border-secondary-200',
    outline: 'border-2 border-primary-300 bg-white text-primary-700 hover:bg-primary-50 hover:border-primary-400 focus:ring-primary-500 disabled:bg-gray-50 disabled:border-gray-200 disabled:text-gray-400',
    ghost: 'text-secondary-700 hover:bg-secondary-100 focus:ring-secondary-500 hover:text-secondary-900',
    danger: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 focus:ring-red-500 disabled:from-red-300 disabled:to-red-300 shadow-md hover:shadow-lg',
    success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 focus:ring-green-500 disabled:from-green-300 disabled:to-green-300 shadow-md hover:shadow-lg',
    accent: 'bg-gradient-to-r from-accent-600 to-accent-700 text-white hover:from-accent-700 hover:to-accent-800 focus:ring-accent-500 disabled:from-accent-300 disabled:to-accent-300 shadow-md hover:shadow-lg'
  }
  
  const sizeClasses = {
    xs: 'px-2.5 py-1.5 text-xs',
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  }

  const isDisabled = disabled || loading
  const spinnerColor = ['primary', 'danger', 'success', 'accent'].includes(variant) ? 'white' : 'gray'

  return (
    <button
      type={type}
      disabled={isDisabled}
      className={clsx(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        isDisabled && 'cursor-not-allowed opacity-60 transform-none',
        className
      )}
      {...props}
    >
      {/* Shimmer effect for primary buttons */}
      {(variant === 'primary' || variant === 'accent') && !isDisabled && (
        <div className="absolute inset-0 -top-px overflow-hidden rounded-lg">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:animate-shimmer" />
        </div>
      )}

      <div className="relative flex items-center justify-center">
        {loading && (
          <LoadingSpinner
            size="sm"
            color={spinnerColor}
            className="mr-2"
          />
        )}

        {Icon && !loading && iconPosition === 'left' && (
          <Icon className={clsx('h-4 w-4', children && 'mr-2')} />
        )}

        {children}

        {Icon && !loading && iconPosition === 'right' && (
          <Icon className={clsx('h-4 w-4', children && 'ml-2')} />
        )}
      </div>
    </button>
  )
}

export default Button
