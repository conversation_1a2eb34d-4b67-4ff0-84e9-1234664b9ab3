import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@context/AuthContext'
import { LoadingSpinner } from '@components/ui/LoadingSpinner'

export const ProtectedRoute = ({ children, requiredPermission = null }) => {
  const { isAuthenticated, loading, user } = useAuth()
  const location = useLocation()

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!isAuthenticated) {
    // Redirect to login page with return url
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // Check for specific permission if required
  if (requiredPermission && !hasPermission(user, requiredPermission)) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
          <p className="mt-2 text-gray-600">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    )
  }

  return children
}

// Helper function to check permissions
const hasPermission = (user, permission) => {
  if (!user) return false
  
  // Super admin has all permissions
  if (user.role === 'super_admin') return true
  
  // Check if user has the specific permission
  return user.permissions?.includes(permission) || false
}
