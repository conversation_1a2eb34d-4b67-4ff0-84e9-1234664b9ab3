<?php

namespace App\Models;

use CodeIgniter\Model;

class TaskStatusHistoryModel extends Model
{
    protected $table            = 'task_status_history';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'task_id',
        'status',
        'notes',
        'location_lat',
        'location_lng',
        'created_by'
    ];

    // Dates
    protected $useTimestamps = false; // Only created_at, no updated_at
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';

    // Validation
    protected $validationRules = [
        'task_id'      => 'required|integer',
        'status'       => 'required|in_list[pending,assigned,in_progress,completed,cancelled]',
        'notes'        => 'permit_empty',
        'location_lat' => 'permit_empty|decimal',
        'location_lng' => 'permit_empty|decimal',
        'created_by'   => 'permit_empty|integer',
    ];

    protected $validationMessages = [
        'task_id' => [
            'required' => 'Task ID is required',
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list'  => 'Invalid status value',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setCreatedAt'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Set created_at timestamp
     */
    protected function setCreatedAt(array $data)
    {
        $data['data']['created_at'] = date('Y-m-d H:i:s');
        return $data;
    }

    /**
     * Get status history for a task
     */
    public function getTaskHistory(int $taskId)
    {
        return $this->select('task_status_history.*, users.first_name, users.last_name')
                   ->join('users', 'users.id = task_status_history.created_by', 'left')
                   ->where('task_id', $taskId)
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }

    /**
     * Get latest status for a task
     */
    public function getLatestStatus(int $taskId)
    {
        return $this->where('task_id', $taskId)
                   ->orderBy('created_at', 'DESC')
                   ->first();
    }

    /**
     * Add status update with location
     */
    public function addStatusWithLocation(int $taskId, string $status, float $lat = null, float $lng = null, string $notes = null, int $createdBy = null)
    {
        return $this->insert([
            'task_id' => $taskId,
            'status' => $status,
            'notes' => $notes,
            'location_lat' => $lat,
            'location_lng' => $lng,
            'created_by' => $createdBy,
        ]);
    }

    /**
     * Get status timeline for multiple tasks
     */
    public function getTasksTimeline(array $taskIds)
    {
        return $this->select('task_status_history.*, users.first_name, users.last_name, tasks.title as task_title')
                   ->join('users', 'users.id = task_status_history.created_by', 'left')
                   ->join('tasks', 'tasks.id = task_status_history.task_id')
                   ->whereIn('task_id', $taskIds)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get status changes by date range
     */
    public function getStatusChangesByDateRange(string $startDate, string $endDate, string $status = null)
    {
        $builder = $this->builder();
        
        $builder->where('created_at >=', $startDate . ' 00:00:00')
                ->where('created_at <=', $endDate . ' 23:59:59');

        if ($status) {
            $builder->where('status', $status);
        }

        return $builder->orderBy('created_at', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get task completion statistics
     */
    public function getCompletionStats(int $workboyId = null, string $dateFrom = null, string $dateTo = null)
    {
        $builder = $this->db->table('task_status_history tsh');
        
        $builder->select('
                    COUNT(CASE WHEN tsh.status = "completed" THEN 1 END) as completed_count,
                    COUNT(CASE WHEN tsh.status = "cancelled" THEN 1 END) as cancelled_count,
                    AVG(TIMESTAMPDIFF(HOUR, t.created_at, tsh.created_at)) as avg_completion_hours
                ')
                ->join('tasks t', 't.id = tsh.task_id')
                ->where('tsh.status IN ("completed", "cancelled")');

        if ($workboyId) {
            $builder->where('t.workboy_id', $workboyId);
        }

        if ($dateFrom) {
            $builder->where('tsh.created_at >=', $dateFrom);
        }

        if ($dateTo) {
            $builder->where('tsh.created_at <=', $dateTo);
        }

        return $builder->get()->getRowArray();
    }

    /**
     * Get location tracking data for a task
     */
    public function getLocationTracking(int $taskId)
    {
        return $this->select('location_lat, location_lng, created_at, notes')
                   ->where('task_id', $taskId)
                   ->where('location_lat IS NOT NULL')
                   ->where('location_lng IS NOT NULL')
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }

    /**
     * Get status distribution
     */
    public function getStatusDistribution(string $dateFrom = null, string $dateTo = null)
    {
        $builder = $this->builder();
        
        if ($dateFrom) {
            $builder->where('created_at >=', $dateFrom);
        }

        if ($dateTo) {
            $builder->where('created_at <=', $dateTo);
        }

        return $builder->select('status, COUNT(*) as count')
                      ->groupBy('status')
                      ->orderBy('count', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get daily status changes
     */
    public function getDailyStatusChanges(int $days = 30)
    {
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        
        return $this->select('DATE(created_at) as date, status, COUNT(*) as count')
                   ->where('created_at >=', $startDate)
                   ->groupBy('DATE(created_at), status')
                   ->orderBy('date', 'ASC')
                   ->orderBy('status', 'ASC')
                   ->findAll();
    }

    /**
     * Get Work-Boy activity timeline
     */
    public function getWorkBoyActivity(int $workboyId, int $limit = 50)
    {
        return $this->select('task_status_history.*, tasks.title as task_title, tasks.customer_id,
                             customers.first_name as customer_first_name, customers.last_name as customer_last_name')
                   ->join('tasks', 'tasks.id = task_status_history.task_id')
                   ->join('users customers', 'customers.id = tasks.customer_id')
                   ->where('tasks.workboy_id', $workboyId)
                   ->orderBy('task_status_history.created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get customer activity timeline
     */
    public function getCustomerActivity(int $customerId, int $limit = 50)
    {
        return $this->select('task_status_history.*, tasks.title as task_title, tasks.workboy_id,
                             workboys.first_name as workboy_first_name, workboys.last_name as workboy_last_name')
                   ->join('tasks', 'tasks.id = task_status_history.task_id')
                   ->join('users workboys', 'workboys.id = tasks.workboy_id', 'left')
                   ->where('tasks.customer_id', $customerId)
                   ->orderBy('task_status_history.created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Clean old status history (for maintenance)
     */
    public function cleanOldHistory(int $daysToKeep = 365)
    {
        $cutoffDate = date('Y-m-d', strtotime("-{$daysToKeep} days"));
        
        return $this->where('created_at <', $cutoffDate)
                   ->delete();
    }
}
