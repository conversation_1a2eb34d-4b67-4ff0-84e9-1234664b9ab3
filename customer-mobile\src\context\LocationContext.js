import React, { createContext, useContext, useEffect, useState } from 'react';
import * as Location from 'expo-location';
import { Alert } from 'react-native';
import { showToast } from '@utils/toast';

const LocationContext = createContext({});

export const useLocation = () => {
  const context = useContext(LocationContext);
  if (!context) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
};

export const LocationProvider = ({ children }) => {
  const [location, setLocation] = useState(null);
  const [address, setAddress] = useState(null);
  const [loading, setLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState(null);

  useEffect(() => {
    checkLocationPermission();
  }, []);

  const checkLocationPermission = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      setPermissionStatus(status);
      
      if (status === 'granted') {
        getCurrentLocation();
      }
    } catch (error) {
      console.error('Error checking location permission:', error);
    }
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setPermissionStatus(status);
      
      if (status === 'granted') {
        showToast('Location permission granted', 'success');
        getCurrentLocation();
        return true;
      } else {
        showToast('Location permission denied', 'error');
        return false;
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      showToast('Failed to request location permission', 'error');
      return false;
    }
  };

  const getCurrentLocation = async () => {
    try {
      setLoading(true);
      
      // Check if location services are enabled
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        Alert.alert(
          'Location Services Disabled',
          'Please enable location services to use this feature.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => Location.enableNetworkProviderAsync() }
          ]
        );
        return null;
      }

      // Get current position
      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeout: 15000,
        maximumAge: 10000,
      });

      setLocation(currentLocation);

      // Reverse geocode to get address
      const addressResult = await Location.reverseGeocodeAsync({
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
      });

      if (addressResult.length > 0) {
        setAddress(addressResult[0]);
      }

      return currentLocation;
    } catch (error) {
      console.error('Error getting current location:', error);
      
      if (error.code === 'E_LOCATION_TIMEOUT') {
        showToast('Location request timed out. Please try again.', 'error');
      } else if (error.code === 'E_LOCATION_UNAVAILABLE') {
        showToast('Location services are unavailable.', 'error');
      } else {
        showToast('Failed to get current location', 'error');
      }
      
      return null;
    } finally {
      setLoading(false);
    }
  };

  const geocodeAddress = async (addressString) => {
    try {
      const geocoded = await Location.geocodeAsync(addressString);
      return geocoded;
    } catch (error) {
      console.error('Error geocoding address:', error);
      showToast('Failed to find location for the address', 'error');
      return [];
    }
  };

  const reverseGeocode = async (latitude, longitude) => {
    try {
      const addressResult = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });
      return addressResult;
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      showToast('Failed to get address for location', 'error');
      return [];
    }
  };

  const watchLocation = async (callback, options = {}) => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        throw new Error('Location permission not granted');
      }

      const subscription = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000,
          distanceInterval: 10,
          ...options,
        },
        callback
      );

      return subscription;
    } catch (error) {
      console.error('Error watching location:', error);
      showToast('Failed to start location tracking', 'error');
      return null;
    }
  };

  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in kilometers
    return distance;
  };

  const formatAddress = (addressObj) => {
    if (!addressObj) return '';
    
    const parts = [
      addressObj.name,
      addressObj.street,
      addressObj.streetNumber,
      addressObj.district,
      addressObj.city,
      addressObj.region,
      addressObj.postalCode,
      addressObj.country,
    ].filter(Boolean);
    
    return parts.join(', ');
  };

  const isLocationPermissionGranted = () => {
    return permissionStatus === 'granted';
  };

  const openLocationSettings = async () => {
    try {
      await Location.enableNetworkProviderAsync();
    } catch (error) {
      console.error('Error opening location settings:', error);
      Alert.alert(
        'Settings',
        'Please enable location services in your device settings.',
        [{ text: 'OK' }]
      );
    }
  };

  const value = {
    // State
    location,
    address,
    loading,
    permissionStatus,
    
    // Methods
    requestLocationPermission,
    getCurrentLocation,
    geocodeAddress,
    reverseGeocode,
    watchLocation,
    calculateDistance,
    formatAddress,
    isLocationPermissionGranted,
    openLocationSettings,
    
    // Computed values
    hasLocation: !!location,
    hasPermission: permissionStatus === 'granted',
    coordinates: location ? {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
    } : null,
  };

  return (
    <LocationContext.Provider value={value}>
      {children}
    </LocationContext.Provider>
  );
};
