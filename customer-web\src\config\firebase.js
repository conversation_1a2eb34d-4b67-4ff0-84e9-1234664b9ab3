import { initializeApp } from 'firebase/app'
import { getAuth, connectAuthEmulator, GoogleAuthProvider } from 'firebase/auth'
import { getMessaging, getToken, onMessage } from 'firebase/messaging'
import mockAuthService, { MockGoogleAuthProvider } from '../services/mockAuthService'

// Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "demo-api-key",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "demo-project.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "demo-project",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "demo-project.appspot.com",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "123456789012",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:123456789012:web:demo",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-DEMO"
}

// Check if we're in development with placeholder credentials
const isUsingPlaceholderCredentials =
  firebaseConfig.apiKey.includes('Placeholder') ||
  firebaseConfig.apiKey === 'demo-api-key' ||
  firebaseConfig.projectId.includes('dev') ||
  firebaseConfig.projectId === 'demo-project'

// Initialize Firebase
let app = null
try {
  app = initializeApp(firebaseConfig)
  if (isUsingPlaceholderCredentials) {
    console.warn('⚠️  Using placeholder Firebase credentials. Authentication will not work properly.')
    console.warn('📝 Please configure real Firebase credentials in .env file for full functionality.')
  }
} catch (error) {
  console.error('Firebase initialization failed:', error)
  console.warn('🔧 Please check your Firebase configuration in .env file')
}

// Initialize Firebase Authentication and get a reference to the service
export let auth = null
export let googleProvider = null

try {
  if (app && !isUsingPlaceholderCredentials) {
    auth = getAuth(app)
    googleProvider = new GoogleAuthProvider()
  } else {
    // Use mock auth service for development
    console.log('🔧 Using mock authentication service for development')
    auth = mockAuthService
    googleProvider = new MockGoogleAuthProvider()
  }
} catch (error) {
  console.error('Firebase Auth initialization failed, falling back to mock service:', error)
  auth = mockAuthService
  googleProvider = new MockGoogleAuthProvider()
}

// Initialize Firebase Cloud Messaging and get a reference to the service
let messaging = null
try {
  if (app && typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    messaging = getMessaging(app)
  }
} catch (error) {
  console.warn('Firebase messaging not supported:', error)
}

// Connect to Auth emulator in development
if (import.meta.env.DEV && auth && !auth._delegate?.emulator && !isUsingPlaceholderCredentials) {
  try {
    connectAuthEmulator(auth, 'http://localhost:9099')
  } catch (error) {
    console.warn('Auth emulator connection failed:', error)
  }
}

// Request notification permission and get FCM token
export const requestNotificationPermission = async () => {
  if (!messaging) {
    console.warn('Messaging not supported')
    return null
  }

  try {
    const permission = await Notification.requestPermission()
    
    if (permission === 'granted') {
      const token = await getToken(messaging, {
        vapidKey: import.meta.env.VITE_FIREBASE_VAPID_KEY
      })
      
      if (token) {
        console.log('FCM Token:', token)
        return token
      } else {
        console.log('No registration token available.')
        return null
      }
    } else {
      console.log('Unable to get permission to notify.')
      return null
    }
  } catch (error) {
    console.error('An error occurred while retrieving token:', error)
    return null
  }
}

// Listen for foreground messages
export const onMessageListener = () => {
  if (!messaging) {
    return Promise.reject('Messaging not supported')
  }

  return new Promise((resolve) => {
    onMessage(messaging, (payload) => {
      console.log('Message received in foreground:', payload)
      resolve(payload)
    })
  })
}

// Export Firebase app instance
export default app
