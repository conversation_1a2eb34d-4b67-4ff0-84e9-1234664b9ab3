import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  StatusBar,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  IconButton,
  Menu,
  Chip,
  Button,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNotification } from '@context/NotificationContext';
import { colors, spacing, typography, borderRadius } from '@constants/theme';
import { showToast } from '@utils/toast';
import { formatDistanceToNow } from 'date-fns';

const NotificationsScreen = ({ navigation }) => {
  const {
    notifications,
    unreadCount,
    loading,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
  } = useNotification();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [menuVisible, setMenuVisible] = useState(false);

  useEffect(() => {
    fetchNotifications();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchNotifications();
    setRefreshing(false);
  };

  const handleMarkAllRead = async () => {
    if (unreadCount === 0) {
      showToast('No unread notifications', 'info');
      return;
    }

    Alert.alert(
      'Mark All as Read',
      'Are you sure you want to mark all notifications as read?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Mark All Read',
          onPress: markAllAsRead,
        },
      ]
    );
  };

  const handleDeleteNotification = (notificationId) => {
    Alert.alert(
      'Delete Notification',
      'Are you sure you want to delete this notification?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteNotification(notificationId),
        },
      ]
    );
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'task_update':
        return 'clipboard-outline';
      case 'payment':
        return 'card-outline';
      case 'review':
        return 'star-outline';
      case 'promotion':
        return 'gift-outline';
      case 'system':
      default:
        return 'information-circle-outline';
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case 'task_update':
        return colors.primary[500];
      case 'payment':
        return colors.success[500];
      case 'review':
        return colors.warning[500];
      case 'promotion':
        return colors.purple[500];
      case 'system':
      default:
        return colors.gray[500];
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (selectedFilter === 'all') return true;
    if (selectedFilter === 'unread') return !notification.is_read;
    if (selectedFilter === 'read') return notification.is_read;
    return notification.type === selectedFilter;
  });

  const filterOptions = [
    { value: 'all', label: 'All' },
    { value: 'unread', label: 'Unread' },
    { value: 'read', label: 'Read' },
    { value: 'task_update', label: 'Task Updates' },
    { value: 'payment', label: 'Payments' },
    { value: 'review', label: 'Reviews' },
    { value: 'promotion', label: 'Promotions' },
  ];

  const renderNotificationItem = ({ item }) => (
    <Card 
      style={[
        styles.notificationCard,
        !item.is_read && styles.unreadCard
      ]}
      onPress={() => {
        if (!item.is_read) {
          markAsRead(item.id);
        }
        // Handle navigation based on notification type
        if (item.data?.screen) {
          navigation.navigate(item.data.screen, item.data.params || {});
        }
      }}
    >
      <Card.Content style={styles.notificationContent}>
        <View style={styles.notificationHeader}>
          <View style={styles.notificationInfo}>
            <View style={styles.notificationTitleRow}>
              <View style={[
                styles.notificationIcon,
                { backgroundColor: `${getNotificationColor(item.type)}20` }
              ]}>
                <Ionicons
                  name={getNotificationIcon(item.type)}
                  size={20}
                  color={getNotificationColor(item.type)}
                />
              </View>
              
              <View style={styles.notificationTextContainer}>
                <Text style={[
                  styles.notificationTitle,
                  !item.is_read && styles.unreadText
                ]} numberOfLines={2}>
                  {item.title}
                </Text>
                
                <Text style={styles.notificationBody} numberOfLines={3}>
                  {item.body || item.message}
                </Text>
                
                <View style={styles.notificationMeta}>
                  <Text style={styles.notificationTime}>
                    {formatDistanceToNow(new Date(item.created_at), { addSuffix: true })}
                  </Text>
                  
                  <Chip
                    mode="outlined"
                    style={[
                      styles.typeChip,
                      { borderColor: getNotificationColor(item.type) }
                    ]}
                    textStyle={[
                      styles.typeChipText,
                      { color: getNotificationColor(item.type) }
                    ]}
                  >
                    {item.type.replace('_', ' ')}
                  </Chip>
                </View>
              </View>
            </View>
          </View>
          
          <View style={styles.notificationActions}>
            {!item.is_read && (
              <View style={styles.unreadIndicator} />
            )}
            
            <IconButton
              icon="delete-outline"
              size={20}
              iconColor={colors.gray[500]}
              onPress={() => handleDeleteNotification(item.id)}
            />
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="notifications-outline" size={64} color={colors.gray[400]} />
      <Text style={styles.emptyTitle}>
        {selectedFilter === 'unread' ? 'No unread notifications' : 'No notifications'}
      </Text>
      <Text style={styles.emptySubtitle}>
        {selectedFilter === 'unread' 
          ? 'You\'re all caught up!'
          : 'New notifications will appear here when you receive them'}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={colors.gray[600]}
            onPress={() => navigation.goBack()}
          />
          <View>
            <Text style={styles.headerTitle}>Notifications</Text>
            {unreadCount > 0 && (
              <Text style={styles.headerSubtitle}>
                {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
              </Text>
            )}
          </View>
        </View>
        
        <View style={styles.headerRight}>
          {unreadCount > 0 && (
            <Button
              mode="text"
              onPress={handleMarkAllRead}
              labelStyle={styles.markAllReadButton}
            >
              Mark All Read
            </Button>
          )}
          
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
              <IconButton
                icon="filter-outline"
                size={24}
                iconColor={colors.gray[600]}
                onPress={() => setMenuVisible(true)}
              />
            }
          >
            {filterOptions.map((option) => (
              <Menu.Item
                key={option.value}
                onPress={() => {
                  setSelectedFilter(option.value);
                  setMenuVisible(false);
                }}
                title={option.label}
                leadingIcon={selectedFilter === option.value ? 'check' : undefined}
              />
            ))}
          </Menu>
        </View>
      </View>

      {/* Filter Indicator */}
      {selectedFilter !== 'all' && (
        <View style={styles.filterIndicator}>
          <Chip
            mode="outlined"
            onClose={() => setSelectedFilter('all')}
            style={styles.filterChip}
            textStyle={styles.filterChipText}
          >
            {filterOptions.find(f => f.value === selectedFilter)?.label}
          </Chip>
        </View>
      )}

      {/* Notifications List */}
      <FlatList
        data={filteredNotifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
  },
  headerSubtitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  markAllReadButton: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.primary[600],
  },
  filterIndicator: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
  },
  filterChip: {
    alignSelf: 'flex-start',
    backgroundColor: colors.primary[50],
    borderColor: colors.primary[200],
  },
  filterChipText: {
    color: colors.primary[700],
    fontSize: typography.fontSize.sm,
  },
  listContainer: {
    padding: spacing.lg,
  },
  notificationCard: {
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
    elevation: 1,
  },
  unreadCard: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary[500],
    backgroundColor: colors.primary[25],
  },
  notificationContent: {
    paddingVertical: spacing.md,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  notificationInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  notificationTitleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  notificationIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
    marginTop: spacing.xs,
  },
  notificationTextContainer: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  unreadText: {
    fontFamily: typography.fontFamily.semiBold,
  },
  notificationBody: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    lineHeight: typography.lineHeight.sm,
    marginBottom: spacing.sm,
  },
  notificationMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  notificationTime: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[500],
  },
  typeChip: {
    backgroundColor: colors.transparent,
    height: 24,
  },
  typeChipText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.medium,
    textTransform: 'capitalize',
  },
  notificationActions: {
    alignItems: 'center',
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary[500],
    marginBottom: spacing.sm,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing['4xl'],
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
    paddingHorizontal: spacing.xl,
  },
});

export default NotificationsScreen;
