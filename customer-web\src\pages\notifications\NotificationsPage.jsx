import React, { useState, useEffect } from 'react'
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Trash2, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  AlertCircle,
  CreditCard,
  ClipboardList,
  Star,
  Gift
} from 'lucide-react'
import DashboardLayout from '../../components/layout/DashboardLayout'
import LoadingSpinner from '../../components/ui/LoadingSpinner'
import NotificationSettings from '../../components/notifications/NotificationSettings'
import { apiHelpers, endpoints } from '../../config/api'
import { format, formatDistanceToNow } from 'date-fns'
import toast from 'react-hot-toast'

const NotificationsPage = () => {
  const [notifications, setNotifications] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedType, setSelectedType] = useState('')
  const [selectedRead, setSelectedRead] = useState('')
  const [settingsOpen, setSettingsOpen] = useState(false)
  const [markingAllRead, setMarkingAllRead] = useState(false)

  useEffect(() => {
    fetchNotifications()
  }, [selectedType, selectedRead])

  const fetchNotifications = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      if (selectedType) params.append('type', selectedType)
      if (selectedRead !== '') params.append('is_read', selectedRead)

      const response = await apiHelpers.get(`${endpoints.notifications.list}?${params.toString()}`)
      
      if (response.success) {
        setNotifications(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (notificationId) => {
    try {
      await apiHelpers.put(endpoints.notifications.markRead(notificationId))
      
      setNotifications(notifications.map(notification =>
        notification.id === notificationId
          ? { ...notification, is_read: 1 }
          : notification
      ))
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
      toast.error('Failed to mark notification as read')
    }
  }

  const markAllAsRead = async () => {
    try {
      setMarkingAllRead(true)
      await apiHelpers.put(endpoints.notifications.markAllRead)
      
      setNotifications(notifications.map(notification => ({
        ...notification,
        is_read: 1
      })))
      
      toast.success('All notifications marked as read')
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
      toast.error('Failed to mark all notifications as read')
    } finally {
      setMarkingAllRead(false)
    }
  }

  const deleteNotification = async (notificationId) => {
    if (!window.confirm('Are you sure you want to delete this notification?')) {
      return
    }

    try {
      await apiHelpers.delete(endpoints.notifications.delete(notificationId))
      
      setNotifications(notifications.filter(notification => 
        notification.id !== notificationId
      ))
      
      toast.success('Notification deleted')
    } catch (error) {
      console.error('Failed to delete notification:', error)
      toast.error('Failed to delete notification')
    }
  }

  const clearFilters = () => {
    setSelectedType('')
    setSelectedRead('')
  }

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'task_update':
        return ClipboardList
      case 'payment':
        return CreditCard
      case 'review':
        return Star
      case 'promotion':
        return Gift
      case 'system':
      default:
        return AlertCircle
    }
  }

  const getNotificationColor = (type) => {
    switch (type) {
      case 'task_update':
        return 'text-blue-600 bg-blue-100'
      case 'payment':
        return 'text-green-600 bg-green-100'
      case 'review':
        return 'text-yellow-600 bg-yellow-100'
      case 'promotion':
        return 'text-purple-600 bg-purple-100'
      case 'system':
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const typeOptions = [
    { value: '', label: 'All Types' },
    { value: 'task_update', label: 'Task Updates' },
    { value: 'payment', label: 'Payments' },
    { value: 'review', label: 'Reviews' },
    { value: 'promotion', label: 'Promotions' },
    { value: 'system', label: 'System' },
  ]

  const readOptions = [
    { value: '', label: 'All' },
    { value: '0', label: 'Unread' },
    { value: '1', label: 'Read' },
  ]

  const unreadCount = notifications.filter(n => !n.is_read).length

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
            <p className="mt-1 text-sm text-gray-500">
              Stay updated with your tasks and activities
              {unreadCount > 0 && (
                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  {unreadCount} unread
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-3 mt-4 sm:mt-0">
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                disabled={markingAllRead}
                className="btn btn-outline btn-sm"
              >
                {markingAllRead ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <>
                    <CheckCheck className="w-4 h-4 mr-2" />
                    Mark All Read
                  </>
                )}
              </button>
            )}
            <button
              onClick={() => setSettingsOpen(true)}
              className="btn btn-outline btn-sm"
            >
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="type" className="form-label">
                Type
              </label>
              <select
                id="type"
                className="input"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
              >
                {typeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="read" className="form-label">
                Status
              </label>
              <select
                id="read"
                className="input"
                value={selectedRead}
                onChange={(e) => setSelectedRead(e.target.value)}
              >
                {readOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="btn btn-outline btn-md w-full"
              >
                <Filter className="w-4 h-4 mr-2" />
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Notifications List */}
        <div className="card">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : notifications.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {notifications.map((notification) => {
                const NotificationIcon = getNotificationIcon(notification.type)
                const isUnread = !notification.is_read
                
                return (
                  <div 
                    key={notification.id} 
                    className={`p-6 transition-colors ${
                      isUnread ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-start space-x-4">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getNotificationColor(notification.type)}`}>
                        <NotificationIcon className="w-5 h-5" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className={`text-sm font-medium ${isUnread ? 'text-gray-900' : 'text-gray-700'}`}>
                              {notification.title}
                            </h3>
                            <p className={`text-sm mt-1 ${isUnread ? 'text-gray-700' : 'text-gray-500'}`}>
                              {notification.message}
                            </p>
                            <div className="flex items-center space-x-4 mt-2">
                              <span className="text-xs text-gray-500">
                                {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                              </span>
                              <span className="badge badge-secondary text-xs">
                                {notification.type.replace('_', ' ')}
                              </span>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2 ml-4">
                            {isUnread && (
                              <button
                                onClick={() => markAsRead(notification.id)}
                                className="p-1 text-gray-400 hover:text-blue-600 rounded"
                                title="Mark as read"
                              >
                                <Check className="w-4 h-4" />
                              </button>
                            )}
                            <button
                              onClick={() => deleteNotification(notification.id)}
                              className="p-1 text-gray-400 hover:text-red-600 rounded"
                              title="Delete notification"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Bell className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No notifications found
              </h3>
              <p className="text-gray-500">
                {selectedType || selectedRead !== ''
                  ? 'Try adjusting your filters to see more notifications.'
                  : 'You\'re all caught up! New notifications will appear here.'}
              </p>
            </div>
          )}
        </div>

        {/* Tips Card */}
        <div className="card p-6 bg-blue-50 border-blue-200">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <Bell className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-blue-900 mb-1">
                Stay Updated
              </h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Enable push notifications to get instant updates</li>
                <li>• Check your notification settings to customize what you receive</li>
                <li>• Mark notifications as read to keep your inbox organized</li>
                <li>• Important updates about your tasks will always be sent</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Notification Settings Modal */}
      <NotificationSettings
        isOpen={settingsOpen}
        onClose={() => setSettingsOpen(false)}
      />
    </DashboardLayout>
  )
}

export default NotificationsPage
