import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Paragraph,
  Avatar,
  Button,
  Divider,
  List,
  Switch,
  ActivityIndicator,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '@context/AuthContext';
import { showToast } from '@utils/toast';

const ProfileScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [locationEnabled, setLocationEnabled] = useState(true);
  const { user, logout, updateProfile } = useAuth();

  useEffect(() => {
    loadProfileData();
  }, []);

  const loadProfileData = async () => {
    try {
      setLoading(true);
      // Load user profile data
      // This would typically fetch from API
    } catch (error) {
      console.error('Error loading profile:', error);
      showToast('error', 'Error', 'Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProfileData();
    setRefreshing(false);
  };

  const handleEditProfile = () => {
    navigation.navigate('EditProfile');
  };

  const handleDocuments = () => {
    navigation.navigate('Documents');
  };

  const handleAvailability = () => {
    navigation.navigate('Availability');
  };

  const handleSettings = () => {
    navigation.navigate('Settings');
  };

  const handleHelp = () => {
    navigation.navigate('Help');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              showToast('success', 'Logged Out', 'You have been successfully logged out');
            } catch (error) {
              showToast('error', 'Error', 'Failed to logout');
            }
          },
        },
      ]
    );
  };

  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getVerificationStatus = () => {
    if (user?.isVerified) return { text: 'Verified', color: '#10b981' };
    if (user?.verificationPending) return { text: 'Pending', color: '#f59e0b' };
    return { text: 'Not Verified', color: '#ef4444' };
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#10b981" />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const verificationStatus = getVerificationStatus();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Header */}
        <Card style={styles.profileCard}>
          <Card.Content style={styles.profileContent}>
            <View style={styles.avatarContainer}>
              {user?.avatar ? (
                <Avatar.Image size={80} source={{ uri: user.avatar }} />
              ) : (
                <Avatar.Text
                  size={80}
                  label={getInitials(user?.name || user?.firstName + ' ' + user?.lastName)}
                  style={styles.avatar}
                />
              )}
              <TouchableOpacity style={styles.editAvatarButton}>
                <Ionicons name="camera" size={16} color="#ffffff" />
              </TouchableOpacity>
            </View>

            <View style={styles.profileInfo}>
              <Title style={styles.userName}>
                {user?.name || `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'User'}
              </Title>
              <Paragraph style={styles.userEmail}>{user?.email}</Paragraph>
              
              <View style={styles.verificationBadge}>
                <Ionicons
                  name={user?.isVerified ? 'checkmark-circle' : 'alert-circle'}
                  size={16}
                  color={verificationStatus.color}
                />
                <Text style={[styles.verificationText, { color: verificationStatus.color }]}>
                  {verificationStatus.text}
                </Text>
              </View>

              <Button
                mode="outlined"
                onPress={handleEditProfile}
                style={styles.editButton}
                labelStyle={styles.editButtonText}
              >
                Edit Profile
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Stats Card */}
        <Card style={styles.statsCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Your Stats</Title>
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user?.completedTasks || 0}</Text>
                <Text style={styles.statLabel}>Tasks Completed</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user?.rating || '0.0'}</Text>
                <Text style={styles.statLabel}>Rating</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>${user?.totalEarnings || '0.00'}</Text>
                <Text style={styles.statLabel}>Total Earned</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Account Section */}
        <Card style={styles.menuCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Account</Title>
            
            <List.Item
              title="Documents & Verification"
              description="Upload required documents"
              left={props => <List.Icon {...props} icon="file-document" color="#10b981" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleDocuments}
              style={styles.menuItem}
            />

            <List.Item
              title="Availability"
              description="Set your working hours"
              left={props => <List.Icon {...props} icon="clock" color="#3b82f6" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleAvailability}
              style={styles.menuItem}
            />

            <List.Item
              title="Payment Methods"
              description="Manage withdrawal methods"
              left={props => <List.Icon {...props} icon="credit-card" color="#8b5cf6" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => navigation.navigate('PaymentMethods')}
              style={styles.menuItem}
            />
          </Card.Content>
        </Card>

        {/* Settings Section */}
        <Card style={styles.menuCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Settings</Title>
            
            <List.Item
              title="Push Notifications"
              description="Receive task alerts"
              left={props => <List.Icon {...props} icon="bell" color="#f59e0b" />}
              right={() => (
                <Switch
                  value={notificationsEnabled}
                  onValueChange={setNotificationsEnabled}
                  color="#10b981"
                />
              )}
              style={styles.menuItem}
            />

            <List.Item
              title="Location Services"
              description="Allow location access"
              left={props => <List.Icon {...props} icon="map-marker" color="#ef4444" />}
              right={() => (
                <Switch
                  value={locationEnabled}
                  onValueChange={setLocationEnabled}
                  color="#10b981"
                />
              )}
              style={styles.menuItem}
            />

            <List.Item
              title="App Settings"
              description="Preferences and privacy"
              left={props => <List.Icon {...props} icon="cog" color="#6b7280" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleSettings}
              style={styles.menuItem}
            />
          </Card.Content>
        </Card>

        {/* Support Section */}
        <Card style={styles.menuCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Support</Title>
            
            <List.Item
              title="Help & FAQ"
              description="Get help and answers"
              left={props => <List.Icon {...props} icon="help-circle" color="#10b981" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleHelp}
              style={styles.menuItem}
            />

            <List.Item
              title="Contact Support"
              description="Get in touch with us"
              left={props => <List.Icon {...props} icon="message" color="#3b82f6" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => navigation.navigate('ContactSupport')}
              style={styles.menuItem}
            />

            <List.Item
              title="Rate the App"
              description="Share your feedback"
              left={props => <List.Icon {...props} icon="star" color="#f59e0b" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {/* Handle app rating */}}
              style={styles.menuItem}
            />
          </Card.Content>
        </Card>

        {/* Logout Button */}
        <Card style={styles.logoutCard}>
          <Card.Content>
            <Button
              mode="outlined"
              onPress={handleLogout}
              style={styles.logoutButton}
              labelStyle={styles.logoutButtonText}
              icon="logout"
            >
              Logout
            </Button>
          </Card.Content>
        </Card>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  scrollView: {
    flex: 1,
  },
  profileCard: {
    margin: 20,
    borderRadius: 16,
  },
  profileContent: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatar: {
    backgroundColor: '#10b981',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#10b981',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    alignItems: 'center',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 12,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  verificationText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  editButton: {
    borderColor: '#10b981',
    paddingHorizontal: 24,
  },
  editButtonText: {
    color: '#10b981',
    fontWeight: '600',
  },
  statsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  menuCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  menuItem: {
    paddingVertical: 8,
  },
  logoutCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
  },
  logoutButton: {
    borderColor: '#ef4444',
  },
  logoutButtonText: {
    color: '#ef4444',
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 20,
  },
});

export default ProfileScreen;
