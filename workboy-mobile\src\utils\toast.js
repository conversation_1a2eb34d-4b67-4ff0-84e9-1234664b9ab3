import Toast from 'react-native-toast-message';

/**
 * Utility functions for showing toast messages throughout the app
 */

// Success toast
export const showSuccessToast = (title, message, options = {}) => {
  Toast.show({
    type: 'success',
    text1: title,
    text2: message,
    position: 'top',
    visibilityTime: 4000,
    autoHide: true,
    topOffset: 60,
    ...options,
  });
};

// Error toast
export const showErrorToast = (title, message, options = {}) => {
  Toast.show({
    type: 'error',
    text1: title,
    text2: message,
    position: 'top',
    visibilityTime: 5000,
    autoHide: true,
    topOffset: 60,
    ...options,
  });
};

// Info toast
export const showInfoToast = (title, message, options = {}) => {
  Toast.show({
    type: 'info',
    text1: title,
    text2: message,
    position: 'top',
    visibilityTime: 4000,
    autoHide: true,
    topOffset: 60,
    ...options,
  });
};

// Warning toast
export const showWarningToast = (title, message, options = {}) => {
  Toast.show({
    type: 'warning',
    text1: title,
    text2: message,
    position: 'top',
    visibilityTime: 4500,
    autoHide: true,
    topOffset: 60,
    ...options,
  });
};

// Task notification toast
export const showTaskNotification = (title, message, options = {}) => {
  Toast.show({
    type: 'taskNotification',
    text1: title,
    text2: message,
    position: 'top',
    visibilityTime: 6000,
    autoHide: true,
    topOffset: 60,
    ...options,
  });
};

// Generic toast function (main export for backward compatibility)
export const showToast = (type, title, message, options = {}) => {
  switch (type) {
    case 'success':
      showSuccessToast(title, message, options);
      break;
    case 'error':
      showErrorToast(title, message, options);
      break;
    case 'info':
      showInfoToast(title, message, options);
      break;
    case 'warning':
      showWarningToast(title, message, options);
      break;
    case 'task':
      showTaskNotification(title, message, options);
      break;
    default:
      Toast.show({
        type: type || 'info',
        text1: title,
        text2: message,
        position: 'top',
        visibilityTime: 4000,
        autoHide: true,
        topOffset: 60,
        ...options,
      });
  }
};

// Hide all toasts
export const hideToast = () => {
  Toast.hide();
};

// Convenience functions for common use cases
export const toast = {
  success: showSuccessToast,
  error: showErrorToast,
  info: showInfoToast,
  warning: showWarningToast,
  task: showTaskNotification,
  hide: hideToast,
  show: showToast,
};

// Task-specific toast messages
export const taskToasts = {
  accepted: (taskTitle) => 
    showSuccessToast(
      'Task Accepted!', 
      `You've successfully accepted "${taskTitle}". Head to the location to start.`
    ),
  
  started: (taskTitle) => 
    showInfoToast(
      'Task Started', 
      `"${taskTitle}" is now in progress. Good luck!`
    ),
  
  completed: (taskTitle, earnings) => 
    showSuccessToast(
      'Task Completed!', 
      `"${taskTitle}" completed successfully. You earned $${earnings}!`
    ),
  
  cancelled: (taskTitle, reason) => 
    showWarningToast(
      'Task Cancelled', 
      `"${taskTitle}" has been cancelled. ${reason ? `Reason: ${reason}` : ''}`
    ),
  
  failed: (taskTitle, error) => 
    showErrorToast(
      'Task Failed', 
      `Failed to update "${taskTitle}". ${error || 'Please try again.'}`
    ),
  
  locationRequired: () => 
    showWarningToast(
      'Location Required', 
      'Please enable location services to accept tasks.'
    ),
  
  networkError: () => 
    showErrorToast(
      'Network Error', 
      'Unable to connect to server. Please check your internet connection.'
    ),
  
  unauthorized: () => 
    showErrorToast(
      'Session Expired', 
      'Your session has expired. Please log in again.'
    ),
  
  newTaskAvailable: (taskCount) => 
    showTaskNotification(
      'New Tasks Available!', 
      `${taskCount} new ${taskCount === 1 ? 'task' : 'tasks'} available in your area.`
    ),
  
  paymentReceived: (amount) => 
    showSuccessToast(
      'Payment Received!', 
      `$${amount} has been added to your account.`
    ),
  
  ratingReceived: (rating, taskTitle) => 
    showInfoToast(
      'Rating Received', 
      `You received a ${rating}-star rating for "${taskTitle}".`
    ),
};

// Auth-specific toast messages
export const authToasts = {
  loginSuccess: (userName) => 
    showSuccessToast(
      'Welcome back!', 
      `Hello ${userName}, you're successfully logged in.`
    ),
  
  loginError: (error) => 
    showErrorToast(
      'Login Failed', 
      error || 'Please check your credentials and try again.'
    ),
  
  registerSuccess: () => 
    showSuccessToast(
      'Account Created!', 
      'Welcome to WorkBoy! Please complete your profile to start working.'
    ),
  
  registerError: (error) => 
    showErrorToast(
      'Registration Failed', 
      error || 'Please try again later.'
    ),
  
  logoutSuccess: () => 
    showInfoToast(
      'Logged Out', 
      'You have been successfully logged out.'
    ),
  
  passwordResetSent: (email) => 
    showSuccessToast(
      'Reset Email Sent', 
      `Password reset instructions sent to ${email}.`
    ),
  
  passwordResetError: (error) => 
    showErrorToast(
      'Reset Failed', 
      error || 'Failed to send reset email. Please try again.'
    ),
  
  profileUpdated: () => 
    showSuccessToast(
      'Profile Updated', 
      'Your profile has been successfully updated.'
    ),
  
  profileError: (error) => 
    showErrorToast(
      'Update Failed', 
      error || 'Failed to update profile. Please try again.'
    ),
};

// Location-specific toast messages
export const locationToasts = {
  permissionGranted: () => 
    showSuccessToast(
      'Location Access Granted', 
      'You can now see tasks in your area.'
    ),
  
  permissionDenied: () => 
    showWarningToast(
      'Location Access Denied', 
      'Location access is required to show nearby tasks.'
    ),
  
  locationError: () => 
    showErrorToast(
      'Location Error', 
      'Unable to get your current location. Please try again.'
    ),
  
  locationUpdated: (address) => 
    showInfoToast(
      'Location Updated', 
      address ? `Current location: ${address}` : 'Your location has been updated.'
    ),
};

// Export default for convenience
export default toast;
