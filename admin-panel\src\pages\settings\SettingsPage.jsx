import { useState } from 'react'
import { Helmet } from 'react-helmet-async'
import {
  Cog6ToothIcon,
  BellIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  UserIcon,
  KeyIcon,
  EnvelopeIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline'
import PremiumCard, { PremiumCardHeader, PremiumCardTitle, PremiumCardContent } from '../../components/ui/PremiumCard'
import PremiumButton from '../../components/ui/PremiumButton'
import PremiumInput from '../../components/ui/PremiumInput'

const SettingsPage = () => {
  const [settings, setSettings] = useState({
    general: {
      appName: 'WorkBoy',
      appDescription: 'Professional Home Services Platform',
      timezone: 'Asia/Kolkata',
      language: 'en',
      currency: 'INR'
    },
    notifications: {
      emailNotifications: true,
      smsNotifications: true,
      pushNotifications: true,
      taskUpdates: true,
      paymentAlerts: true,
      systemAlerts: true
    },
    payment: {
      razorpayKey: 'rzp_test_***',
      stripeKey: 'sk_test_***',
      paytmKey: 'merchant_***',
      commissionRate: 15,
      minimumPayout: 500
    },
    security: {
      twoFactorAuth: true,
      sessionTimeout: 30,
      passwordPolicy: 'strong',
      apiRateLimit: 1000
    }
  })

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
  }

  const handleSave = () => {
    // Save settings logic here
    console.log('Saving settings:', settings)
  }

  return (
    <div className="space-y-6 animate-fade-in-up">
      <Helmet>
        <title>Settings & Configuration - WorkBoy Admin</title>
      </Helmet>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-black bg-gradient-to-r from-gray-900 via-primary-800 to-brand-700 bg-clip-text text-transparent">
            Settings & Configuration
          </h1>
          <p className="text-gray-600 mt-2 font-medium">Manage application settings and system configuration</p>
        </div>
        <div className="flex space-x-3">
          <PremiumButton variant="outline">
            Reset to Default
          </PremiumButton>
          <PremiumButton variant="primary" onClick={handleSave}>
            Save Changes
          </PremiumButton>
        </div>
      </div>

      {/* General Settings */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={Cog6ToothIcon}>
            General Settings
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <PremiumInput
              label="Application Name"
              value={settings.general.appName}
              onChange={(e) => handleSettingChange('general', 'appName', e.target.value)}
              variant="premium"
            />
            <PremiumInput
              label="Application Description"
              value={settings.general.appDescription}
              onChange={(e) => handleSettingChange('general', 'appDescription', e.target.value)}
              variant="premium"
            />
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2">Timezone</label>
              <select
                value={settings.general.timezone}
                onChange={(e) => handleSettingChange('general', 'timezone', e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 bg-gray-50/50 rounded-xl text-sm focus:outline-none focus:bg-white focus:border-primary-300 focus:ring-2 focus:ring-primary-500/20 hover:bg-white hover:border-gray-300 transition-all duration-300"
              >
                <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
                <option value="UTC">UTC</option>
                <option value="America/New_York">America/New_York (EST)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2">Default Currency</label>
              <select
                value={settings.general.currency}
                onChange={(e) => handleSettingChange('general', 'currency', e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 bg-gray-50/50 rounded-xl text-sm focus:outline-none focus:bg-white focus:border-primary-300 focus:ring-2 focus:ring-primary-500/20 hover:bg-white hover:border-gray-300 transition-all duration-300"
              >
                <option value="INR">Indian Rupee (₹)</option>
                <option value="USD">US Dollar ($)</option>
                <option value="EUR">Euro (€)</option>
              </select>
            </div>
          </div>
        </PremiumCardContent>
      </PremiumCard>

      {/* Notification Settings */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={BellIcon}>
            Notification Settings
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/50 to-primary-50/20 rounded-xl">
                <div className="flex items-center space-x-3">
                  <EnvelopeIcon className="h-5 w-5 text-primary-600" />
                  <div>
                    <div className="font-bold text-gray-900">Email Notifications</div>
                    <div className="text-sm text-gray-500">Receive updates via email</div>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.notifications.emailNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'emailNotifications', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/50 to-primary-50/20 rounded-xl">
                <div className="flex items-center space-x-3">
                  <DevicePhoneMobileIcon className="h-5 w-5 text-primary-600" />
                  <div>
                    <div className="font-bold text-gray-900">SMS Notifications</div>
                    <div className="text-sm text-gray-500">Receive updates via SMS</div>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.notifications.smsNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'smsNotifications', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/50 to-primary-50/20 rounded-xl">
                <div className="flex items-center space-x-3">
                  <BellIcon className="h-5 w-5 text-primary-600" />
                  <div>
                    <div className="font-bold text-gray-900">Push Notifications</div>
                    <div className="text-sm text-gray-500">Browser push notifications</div>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.notifications.pushNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'pushNotifications', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
            </div>
          </div>
        </PremiumCardContent>
      </PremiumCard>

      {/* Payment Settings */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={CurrencyDollarIcon}>
            Payment Configuration
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <PremiumInput
              label="Razorpay API Key"
              value={settings.payment.razorpayKey}
              onChange={(e) => handleSettingChange('payment', 'razorpayKey', e.target.value)}
              variant="premium"
              type="password"
            />
            <PremiumInput
              label="Stripe Secret Key"
              value={settings.payment.stripeKey}
              onChange={(e) => handleSettingChange('payment', 'stripeKey', e.target.value)}
              variant="premium"
              type="password"
            />
            <PremiumInput
              label="Commission Rate (%)"
              value={settings.payment.commissionRate}
              onChange={(e) => handleSettingChange('payment', 'commissionRate', e.target.value)}
              variant="premium"
              type="number"
            />
            <PremiumInput
              label="Minimum Payout (₹)"
              value={settings.payment.minimumPayout}
              onChange={(e) => handleSettingChange('payment', 'minimumPayout', e.target.value)}
              variant="premium"
              type="number"
            />
          </div>
        </PremiumCardContent>
      </PremiumCard>

      {/* Security Settings */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={ShieldCheckIcon}>
            Security & Access Control
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-bold text-gray-700 mb-2">Session Timeout (minutes)</label>
                <select
                  value={settings.security.sessionTimeout}
                  onChange={(e) => handleSettingChange('security', 'sessionTimeout', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 bg-gray-50/50 rounded-xl text-sm focus:outline-none focus:bg-white focus:border-primary-300 focus:ring-2 focus:ring-primary-500/20 hover:bg-white hover:border-gray-300 transition-all duration-300"
                >
                  <option value={15}>15 minutes</option>
                  <option value={30}>30 minutes</option>
                  <option value={60}>1 hour</option>
                  <option value={120}>2 hours</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-bold text-gray-700 mb-2">Password Policy</label>
                <select
                  value={settings.security.passwordPolicy}
                  onChange={(e) => handleSettingChange('security', 'passwordPolicy', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 bg-gray-50/50 rounded-xl text-sm focus:outline-none focus:bg-white focus:border-primary-300 focus:ring-2 focus:ring-primary-500/20 hover:bg-white hover:border-gray-300 transition-all duration-300"
                >
                  <option value="basic">Basic (8+ characters)</option>
                  <option value="strong">Strong (8+ chars, numbers, symbols)</option>
                  <option value="very_strong">Very Strong (12+ chars, mixed case, numbers, symbols)</option>
                </select>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/50 to-primary-50/20 rounded-xl">
              <div className="flex items-center space-x-3">
                <KeyIcon className="h-5 w-5 text-primary-600" />
                <div>
                  <div className="font-bold text-gray-900">Two-Factor Authentication</div>
                  <div className="text-sm text-gray-500">Require 2FA for admin access</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.security.twoFactorAuth}
                  onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>
          </div>
        </PremiumCardContent>
      </PremiumCard>

      {/* System Status */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={ChartBarIcon}>
            System Status
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-gradient-to-r from-success-50 to-success-100/50 rounded-xl">
              <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-success-500 to-success-600 flex items-center justify-center shadow-lg shadow-success-500/25">
                <CloudIcon className="h-6 w-6 text-white" />
              </div>
              <div className="font-bold text-gray-900">Database</div>
              <div className="text-sm text-success-600 font-medium">Operational</div>
              <div className="text-xs text-gray-500 mt-1">99.9% uptime</div>
            </div>

            <div className="text-center p-6 bg-gradient-to-r from-success-50 to-success-100/50 rounded-xl">
              <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-success-500 to-success-600 flex items-center justify-center shadow-lg shadow-success-500/25">
                <GlobeAltIcon className="h-6 w-6 text-white" />
              </div>
              <div className="font-bold text-gray-900">API Services</div>
              <div className="text-sm text-success-600 font-medium">Operational</div>
              <div className="text-xs text-gray-500 mt-1">Response time: 120ms</div>
            </div>

            <div className="text-center p-6 bg-gradient-to-r from-warning-50 to-warning-100/50 rounded-xl">
              <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-warning-500 to-warning-600 flex items-center justify-center shadow-lg shadow-warning-500/25">
                <ExclamationTriangleIcon className="h-6 w-6 text-white" />
              </div>
              <div className="font-bold text-gray-900">Payment Gateway</div>
              <div className="text-sm text-warning-600 font-medium">Maintenance</div>
              <div className="text-xs text-gray-500 mt-1">Scheduled: 2:00 AM</div>
            </div>
          </div>
        </PremiumCardContent>
      </PremiumCard>
    </div>
  )
}

export default SettingsPage
