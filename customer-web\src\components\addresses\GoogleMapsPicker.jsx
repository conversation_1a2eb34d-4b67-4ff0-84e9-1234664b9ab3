import React, { useState, useEffect, useRef } from 'react'
import { MapPin, Search } from 'lucide-react'
import LoadingSpinner from '../ui/LoadingSpinner'

const GoogleMapsPicker = ({ onLocationSelect, initialLocation }) => {
  const [map, setMap] = useState(null)
  const [marker, setMarker] = useState(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedLocation, setSelectedLocation] = useState(initialLocation)
  const mapRef = useRef(null)
  const searchInputRef = useRef(null)

  // Default location (Delhi, India)
  const defaultLocation = { lat: 28.6139, lng: 77.2090 }

  useEffect(() => {
    loadGoogleMaps()
  }, [])

  useEffect(() => {
    if (map && initialLocation) {
      updateMapLocation(initialLocation)
    }
  }, [map, initialLocation])

  const loadGoogleMaps = () => {
    // Check if Google Maps is already loaded
    if (window.google && window.google.maps) {
      initializeMap()
      return
    }

    // Load Google Maps script
    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY}&libraries=places`
    script.async = true
    script.defer = true
    script.onload = initializeMap
    script.onerror = () => {
      console.error('Failed to load Google Maps')
      setLoading(false)
    }
    document.head.appendChild(script)
  }

  const initializeMap = () => {
    try {
      const mapOptions = {
        center: selectedLocation || defaultLocation,
        zoom: 15,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: false,
      }

      const mapInstance = new window.google.maps.Map(mapRef.current, mapOptions)
      setMap(mapInstance)

      // Create marker
      const markerInstance = new window.google.maps.Marker({
        position: selectedLocation || defaultLocation,
        map: mapInstance,
        draggable: true,
        title: 'Selected Location'
      })
      setMarker(markerInstance)

      // Add click listener to map
      mapInstance.addListener('click', (event) => {
        const location = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng()
        }
        updateMapLocation(location)
        reverseGeocode(location)
      })

      // Add drag listener to marker
      markerInstance.addListener('dragend', (event) => {
        const location = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng()
        }
        updateMapLocation(location)
        reverseGeocode(location)
      })

      // Initialize Places Autocomplete
      if (searchInputRef.current) {
        const autocomplete = new window.google.maps.places.Autocomplete(
          searchInputRef.current,
          {
            types: ['address'],
            componentRestrictions: { country: 'IN' }
          }
        )

        autocomplete.addListener('place_changed', () => {
          const place = autocomplete.getPlace()
          if (place.geometry) {
            const location = {
              lat: place.geometry.location.lat(),
              lng: place.geometry.location.lng()
            }
            updateMapLocation(location)
            
            // Pass address components to parent
            if (onLocationSelect) {
              onLocationSelect(location, place.address_components)
            }
          }
        })
      }

      setLoading(false)
    } catch (error) {
      console.error('Error initializing map:', error)
      setLoading(false)
    }
  }

  const updateMapLocation = (location) => {
    setSelectedLocation(location)
    
    if (map) {
      map.setCenter(location)
    }
    
    if (marker) {
      marker.setPosition(location)
    }
  }

  const reverseGeocode = (location) => {
    if (!window.google || !window.google.maps) return

    const geocoder = new window.google.maps.Geocoder()
    geocoder.geocode({ location }, (results, status) => {
      if (status === 'OK' && results[0]) {
        if (onLocationSelect) {
          onLocationSelect(location, results[0].address_components)
        }
      }
    })
  }

  const handleSearchSubmit = (e) => {
    e.preventDefault()
    if (!searchQuery.trim() || !window.google) return

    const geocoder = new window.google.maps.Geocoder()
    geocoder.geocode({ address: searchQuery }, (results, status) => {
      if (status === 'OK' && results[0]) {
        const location = {
          lat: results[0].geometry.location.lat(),
          lng: results[0].geometry.location.lng()
        }
        updateMapLocation(location)
        
        if (onLocationSelect) {
          onLocationSelect(location, results[0].address_components)
        }
      } else {
        alert('Location not found. Please try a different search term.')
      }
    })
  }

  const handleConfirmLocation = () => {
    if (selectedLocation && onLocationSelect) {
      reverseGeocode(selectedLocation)
    }
  }

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <form onSubmit={handleSearchSubmit} className="flex space-x-2">
        <div className="flex-1 relative">
          <input
            ref={searchInputRef}
            type="text"
            className="input pl-10"
            placeholder="Search for a location..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
        </div>
        <button
          type="submit"
          className="btn btn-primary btn-md"
        >
          Search
        </button>
      </form>

      {/* Map Container */}
      <div className="relative">
        <div
          ref={mapRef}
          className="w-full h-96 rounded-lg border border-gray-300"
        />
        
        {loading && (
          <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <LoadingSpinner size="lg" />
              <p className="mt-2 text-sm text-gray-600">Loading map...</p>
            </div>
          </div>
        )}

        {!loading && !window.google && (
          <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600">
                Unable to load Google Maps. Please check your internet connection.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <MapPin className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-1">How to select location:</p>
            <ul className="space-y-1">
              <li>• Click anywhere on the map to place a marker</li>
              <li>• Drag the marker to adjust the position</li>
              <li>• Use the search bar to find a specific address</li>
              <li>• The address fields will be auto-filled when you select a location</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Confirm Button */}
      {selectedLocation && (
        <div className="flex justify-center">
          <button
            onClick={handleConfirmLocation}
            className="btn btn-primary btn-md"
          >
            <MapPin className="w-4 h-4 mr-2" />
            Confirm This Location
          </button>
        </div>
      )}
    </div>
  )
}

export default GoogleMapsPicker
