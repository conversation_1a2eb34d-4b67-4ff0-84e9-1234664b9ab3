import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { Card, List, Switch, Divider } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@constants/theme';

const SettingsScreen = ({ navigation }) => {
  const [settings, setSettings] = useState({
    notifications: true,
    emailNotifications: false,
    locationServices: true,
    darkMode: false,
    autoAcceptTasks: false,
  });

  const handleToggle = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const settingsGroups = [
    {
      title: 'Notifications',
      items: [
        {
          title: 'Push Notifications',
          description: 'Receive notifications about task updates',
          key: 'notifications',
          type: 'switch',
        },
        {
          title: 'Email Notifications',
          description: 'Receive email updates about your tasks',
          key: 'emailNotifications',
          type: 'switch',
        },
      ],
    },
    {
      title: 'Privacy & Security',
      items: [
        {
          title: 'Location Services',
          description: 'Allow app to access your location',
          key: 'locationServices',
          type: 'switch',
        },
        {
          title: 'Privacy Policy',
          description: 'View our privacy policy',
          type: 'navigation',
          onPress: () => Alert.alert('Privacy Policy', 'Privacy policy would be displayed here.'),
        },
        {
          title: 'Terms of Service',
          description: 'View terms and conditions',
          type: 'navigation',
          onPress: () => Alert.alert('Terms of Service', 'Terms of service would be displayed here.'),
        },
      ],
    },
    {
      title: 'App Preferences',
      items: [
        {
          title: 'Dark Mode',
          description: 'Use dark theme',
          key: 'darkMode',
          type: 'switch',
        },
        {
          title: 'Language',
          description: 'English',
          type: 'navigation',
          onPress: () => Alert.alert('Language', 'Language selection would be implemented here.'),
        },
      ],
    },
    {
      title: 'Account',
      items: [
        {
          title: 'Delete Account',
          description: 'Permanently delete your account',
          type: 'navigation',
          onPress: () => {
            Alert.alert(
              'Delete Account',
              'Are you sure you want to delete your account? This action cannot be undone.',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Delete', style: 'destructive', onPress: () => {
                  Alert.alert('Account Deleted', 'Your account has been deleted.');
                }},
              ]
            );
          },
          dangerous: true,
        },
      ],
    },
  ];

  const renderSettingItem = (item) => {
    if (item.type === 'switch') {
      return (
        <List.Item
          key={item.key}
          title={item.title}
          description={item.description}
          left={(props) => (
            <List.Icon
              {...props}
              icon={({ size, color }) => (
                <Ionicons name="settings-outline" size={size} color={color} />
              )}
            />
          )}
          right={() => (
            <Switch
              value={settings[item.key]}
              onValueChange={() => handleToggle(item.key)}
              color={colors.primary[600]}
            />
          )}
          style={styles.listItem}
        />
      );
    }

    return (
      <List.Item
        key={item.title}
        title={item.title}
        description={item.description}
        left={(props) => (
          <List.Icon
            {...props}
            icon={({ size, color }) => (
              <Ionicons 
                name={item.dangerous ? "warning-outline" : "chevron-forward-outline"} 
                size={size} 
                color={item.dangerous ? colors.error[500] : color} 
              />
            )}
          />
        )}
        right={(props) => (
          <List.Icon {...props} icon="chevron-forward" />
        )}
        onPress={item.onPress}
        style={styles.listItem}
        titleStyle={item.dangerous ? { color: colors.error[500] } : undefined}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>Settings</Text>

        {settingsGroups.map((group, groupIndex) => (
          <Card key={group.title} style={styles.card}>
            <Card.Content style={styles.cardContent}>
              <Text style={styles.groupTitle}>{group.title}</Text>
              {group.items.map((item, itemIndex) => (
                <React.Fragment key={item.title}>
                  {renderSettingItem(item)}
                  {itemIndex < group.items.length - 1 && (
                    <Divider style={styles.divider} />
                  )}
                </React.Fragment>
              ))}
            </Card.Content>
          </Card>
        ))}

        {/* App Version */}
        <Text style={styles.versionText}>Version 1.0.0</Text>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.lg,
  },
  card: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  cardContent: {
    paddingVertical: spacing.xs,
  },
  groupTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[700],
    marginBottom: spacing.sm,
    marginTop: spacing.sm,
  },
  listItem: {
    paddingVertical: spacing.sm,
  },
  divider: {
    marginLeft: spacing.xl,
  },
  versionText: {
    textAlign: 'center',
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    marginTop: spacing.lg,
  },
});

export default SettingsScreen;
