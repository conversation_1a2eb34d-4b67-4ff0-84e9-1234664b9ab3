#--------------------------------------------------------------------
# Work-Boy Booking API - Environment Configuration
#--------------------------------------------------------------------

#--------------------------------------------------------------------
# ENVIRONMENT
#--------------------------------------------------------------------

CI_ENVIRONMENT = development

#--------------------------------------------------------------------
# APP
#--------------------------------------------------------------------

app.baseURL = 'http://localhost:8080'
app.forceGlobalSecureRequests = false
app.CSPEnabled = false

#--------------------------------------------------------------------
# DATABASE
#--------------------------------------------------------------------

database.default.hostname = localhost
database.default.database = workboy_booking
database.default.username = root
database.default.password =
database.default.DBDriver = MySQLi
database.default.DBPrefix =
database.default.port = 3306
database.default.charset = utf8mb4
database.default.DBCollat = utf8mb4_general_ci

#--------------------------------------------------------------------
# ENCRYPTION
#--------------------------------------------------------------------

encryption.key = hex2bin:1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

#--------------------------------------------------------------------
# SESSION
#--------------------------------------------------------------------

session.driver = 'CodeIgniter\Session\Handlers\FileHandler'
session.savePath = null

#--------------------------------------------------------------------
# LOGGER
#--------------------------------------------------------------------

logger.threshold = 4

#--------------------------------------------------------------------
# FIREBASE CONFIGURATION
#--------------------------------------------------------------------

FIREBASE_PROJECT_ID = workboy-booking-dev
FIREBASE_PRIVATE_KEY_ID = your-private-key-id
FIREBASE_PRIVATE_KEY = "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL = <EMAIL>
FIREBASE_CLIENT_ID = your-client-id
FIREBASE_AUTH_URI = https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI = https://oauth2.googleapis.com/token

#--------------------------------------------------------------------
# GOOGLE MAPS API
#--------------------------------------------------------------------

GOOGLE_MAPS_API_KEY = your-google-maps-api-key

#--------------------------------------------------------------------
# RAZORPAY CONFIGURATION
#--------------------------------------------------------------------

RAZORPAY_KEY_ID = your-razorpay-key-id
RAZORPAY_KEY_SECRET = your-razorpay-key-secret
RAZORPAY_WEBHOOK_SECRET = your-razorpay-webhook-secret

#--------------------------------------------------------------------
# JWT CONFIGURATION
#--------------------------------------------------------------------

JWT_SECRET = your-jwt-secret-key-here
JWT_EXPIRE_TIME = 86400

#--------------------------------------------------------------------
# CORS CONFIGURATION
#--------------------------------------------------------------------

CORS_ALLOWED_ORIGINS = http://localhost:3000,http://localhost:3001
CORS_ALLOWED_METHODS = GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS = Content-Type,Authorization,X-Requested-With

#--------------------------------------------------------------------
# FILE UPLOAD CONFIGURATION
#--------------------------------------------------------------------

MAX_FILE_SIZE = 5242880
ALLOWED_FILE_TYPES = jpg,jpeg,png,pdf,doc,docx

#--------------------------------------------------------------------
# NOTIFICATION CONFIGURATION
#--------------------------------------------------------------------

FCM_SERVER_KEY = your-fcm-server-key
FCM_SENDER_ID = your-fcm-sender-id

#--------------------------------------------------------------------
# EMAIL CONFIGURATION (Optional)
#--------------------------------------------------------------------

# email.fromEmail = <EMAIL>
# email.fromName = Work-Boy Booking
# email.SMTPHost = smtp.gmail.com
# email.SMTPUser = <EMAIL>
# email.SMTPPass = your-app-password
# email.SMTPPort = 587
# email.SMTPCrypto = tls
