<?php

namespace App\Controllers\Api\V1;

use App\Controllers\Api\BaseApiController;
use App\Models\CustomerProfileModel;
use App\Models\AddressModel;
use App\Models\TaskModel;
use App\Models\PaymentModel;

/**
 * Customer Controller for Work-Boy Booking API
 * 
 * Handles customer-specific operations including dashboard, addresses, and preferences
 */
class CustomerController extends BaseApiController
{
    protected $customerProfileModel;
    protected $addressModel;
    protected $taskModel;
    protected $paymentModel;

    public function __construct()
    {
        parent::__construct();
        $this->customerProfileModel = new CustomerProfileModel();
        $this->addressModel = new AddressModel();
        $this->taskModel = new TaskModel();
        $this->paymentModel = new PaymentModel();
    }

    /**
     * Get customer dashboard
     * GET /api/v1/customer/dashboard
     */
    public function dashboard()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $customerId = $currentUser['id'];

            // Get recent tasks
            $recentTasks = $this->taskModel->select('tasks.*, task_categories.name as category_name, task_categories.icon as category_icon')
                                         ->join('task_categories', 'task_categories.id = tasks.category_id', 'left')
                                         ->where('tasks.customer_id', $customerId)
                                         ->orderBy('tasks.created_at', 'DESC')
                                         ->limit(5)
                                         ->findAll();

            // Get task statistics
            $taskStats = $this->taskModel->getTaskStats(['customer_id' => $customerId]);

            // Get upcoming tasks
            $upcomingTasks = $this->taskModel->getUpcomingTasks($customerId, 'customer', 3);

            // Get payment statistics
            $paymentStats = $this->paymentModel->getPaymentStats(['customer_id' => $customerId]);

            // Get addresses count
            $addressesCount = $this->addressModel->where('user_id', $customerId)->countAllResults();

            $dashboardData = [
                'recent_tasks' => $recentTasks,
                'upcoming_tasks' => $upcomingTasks,
                'task_stats' => [
                    'total_tasks' => $taskStats['total_tasks'] ?? 0,
                    'pending_tasks' => $taskStats['pending_tasks'] ?? 0,
                    'in_progress_tasks' => $taskStats['in_progress_tasks'] ?? 0,
                    'completed_tasks' => $taskStats['completed_tasks'] ?? 0,
                    'cancelled_tasks' => $taskStats['cancelled_tasks'] ?? 0,
                ],
                'payment_stats' => [
                    'total_spent' => $paymentStats['total_revenue'] ?? 0,
                    'total_payments' => $paymentStats['total_payments'] ?? 0,
                    'total_tips' => $paymentStats['total_tips'] ?? 0,
                ],
                'addresses_count' => $addressesCount,
            ];

            return $this->apiResponse($dashboardData, 'Dashboard data retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Customer dashboard error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve dashboard data');
        }
    }

    /**
     * Get customer addresses
     * GET /api/v1/customer/addresses
     */
    public function getAddresses()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $addresses = $this->addressModel->getByUserId($currentUser['id']);

            return $this->apiResponse($addresses, 'Addresses retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get addresses error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve addresses');
        }
    }

    /**
     * Create new address
     * POST /api/v1/customer/addresses
     */
    public function createAddress()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $rules = [
                'label'          => 'permit_empty|max_length[50]',
                'address_line_1' => 'required|max_length[255]',
                'address_line_2' => 'permit_empty|max_length[255]',
                'city'           => 'required|max_length[100]',
                'state'          => 'required|max_length[100]',
                'postal_code'    => 'required|max_length[20]',
                'country'        => 'permit_empty|max_length[100]',
                'latitude'       => 'permit_empty|decimal',
                'longitude'      => 'permit_empty|decimal',
                'is_default'     => 'permit_empty|in_list[0,1]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Validate postal code format
            if (!$this->addressModel->validatePostalCode($data['postal_code'])) {
                return $this->errorResponse('Invalid postal code format', 400);
            }

            $addressData = [
                'user_id'        => $currentUser['id'],
                'label'          => $data['label'] ?? null,
                'address_line_1' => $data['address_line_1'],
                'address_line_2' => $data['address_line_2'] ?? null,
                'city'           => $data['city'],
                'state'          => $data['state'],
                'postal_code'    => $data['postal_code'],
                'country'        => $data['country'] ?? 'India',
                'latitude'       => $data['latitude'] ?? null,
                'longitude'      => $data['longitude'] ?? null,
                'is_default'     => $data['is_default'] ?? false,
            ];

            $addressId = $this->addressModel->insert($addressData);

            if (!$addressId) {
                return $this->serverErrorResponse('Failed to create address');
            }

            $address = $this->addressModel->find($addressId);

            $this->logActivity('address_created', ['address_id' => $addressId]);

            return $this->apiResponse($address, 'Address created successfully', 201);

        } catch (\Exception $e) {
            log_message('error', 'Create address error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to create address');
        }
    }

    /**
     * Update address
     * PUT /api/v1/customer/addresses/{id}
     */
    public function updateAddress($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $address = $this->addressModel->where('id', $id)
                                        ->where('user_id', $currentUser['id'])
                                        ->first();

            if (!$address) {
                return $this->notFoundResponse('Address not found');
            }

            $rules = [
                'label'          => 'permit_empty|max_length[50]',
                'address_line_1' => 'permit_empty|max_length[255]',
                'address_line_2' => 'permit_empty|max_length[255]',
                'city'           => 'permit_empty|max_length[100]',
                'state'          => 'permit_empty|max_length[100]',
                'postal_code'    => 'permit_empty|max_length[20]',
                'country'        => 'permit_empty|max_length[100]',
                'latitude'       => 'permit_empty|decimal',
                'longitude'      => 'permit_empty|decimal',
                'is_default'     => 'permit_empty|in_list[0,1]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Validate postal code format if provided
            if (isset($data['postal_code']) && !$this->addressModel->validatePostalCode($data['postal_code'])) {
                return $this->errorResponse('Invalid postal code format', 400);
            }

            $updateData = [];
            $allowedFields = ['label', 'address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country', 'latitude', 'longitude', 'is_default'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return $this->errorResponse('No valid fields to update', 400);
            }

            $updated = $this->addressModel->update($id, $updateData);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to update address');
            }

            $updatedAddress = $this->addressModel->find($id);

            $this->logActivity('address_updated', [
                'address_id' => $id,
                'updated_fields' => array_keys($updateData)
            ]);

            return $this->apiResponse($updatedAddress, 'Address updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Update address error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to update address');
        }
    }

    /**
     * Delete address
     * DELETE /api/v1/customer/addresses/{id}
     */
    public function deleteAddress($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $result = $this->addressModel->safeDelete($id, $currentUser['id']);

            if (!$result['success']) {
                return $this->errorResponse($result['message'], 400);
            }

            $this->logActivity('address_deleted', ['address_id' => $id]);

            return $this->apiResponse(null, 'Address deleted successfully');

        } catch (\Exception $e) {
            log_message('error', 'Delete address error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to delete address');
        }
    }

    /**
     * Update customer preferences
     * PUT /api/v1/customer/preferences
     */
    public function updatePreferences()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'customer') {
                return $this->forbiddenResponse('Customer access required');
            }

            $rules = [
                'preferred_payment_method'  => 'permit_empty|in_list[card,wallet,upi]',
                'default_address_id'        => 'permit_empty|integer',
                'notification_preferences'  => 'permit_empty|valid_json',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Verify default address belongs to customer if provided
            if (isset($data['default_address_id'])) {
                $address = $this->addressModel->where('id', $data['default_address_id'])
                                            ->where('user_id', $currentUser['id'])
                                            ->first();

                if (!$address) {
                    return $this->errorResponse('Invalid default address', 400);
                }
            }

            $updateData = [];
            $allowedFields = ['preferred_payment_method', 'default_address_id', 'notification_preferences'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    if ($field === 'notification_preferences' && is_array($data[$field])) {
                        $updateData[$field] = json_encode($data[$field]);
                    } else {
                        $updateData[$field] = $data[$field];
                    }
                }
            }

            if (empty($updateData)) {
                return $this->errorResponse('No valid fields to update', 400);
            }

            $updated = $this->customerProfileModel->where('user_id', $currentUser['id'])
                                                 ->set($updateData)
                                                 ->update();

            if (!$updated) {
                return $this->serverErrorResponse('Failed to update preferences');
            }

            // Get updated profile
            $profile = $this->customerProfileModel->getByUserId($currentUser['id']);

            $this->logActivity('customer_preferences_updated', [
                'updated_fields' => array_keys($updateData)
            ]);

            return $this->apiResponse($profile, 'Preferences updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Update preferences error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to update preferences');
        }
    }
}
