# Work-Boy Booking Platform - Development Workflow

## Getting Started

### Prerequisites
- Node.js (v16+)
- PHP (v8.0+)
- Composer
- MySQL (v8.0+)
- React Native CLI
- Android Studio / Xcode
- Git

### Initial Setup

1. **Clone the Repository**
```bash
git clone <repository-url>
cd work-boy-booking
```

2. **Install Root Dependencies**
```bash
npm install
```

3. **Setup Backend**
```bash
cd backend
composer install
cp env .env
# Configure your .env file with database and API credentials
php spark migrate
php spark db:seed DatabaseSeeder
```

4. **Setup Web Applications**
```bash
# Customer Web App
cd ../web-app
npm install

# Admin Panel
cd ../admin-panel
npm install
```

5. **Setup Mobile Applications**
```bash
# Customer Mobile App
cd ../mobile-app/customer-app
npm install

# Work-Boy Mobile App
cd ../workboy-app
npm install
```

## Development Commands

### Start All Services
```bash
# From root directory
npm run dev
```

This will start:
- Backend API server (http://localhost:8080)
- Customer Web App (http://localhost:3000)
- Admin Panel (http://localhost:3001)

### Individual Services

```bash
# Backend only
npm run dev:backend

# Web app only
npm run dev:web

# Admin panel only
npm run dev:admin

# Customer mobile app
npm run dev:customer-mobile

# Work-Boy mobile app
npm run dev:workboy-mobile
```

## Project Structure

```
work-boy-booking/
├── backend/                    # PHP CodeIgniter 4 API
│   ├── app/
│   │   ├── Controllers/        # API Controllers
│   │   ├── Models/            # Database Models
│   │   ├── Filters/           # Authentication & CORS
│   │   ├── Config/            # Configuration files
│   │   └── Database/          # Migrations & Seeds
│   ├── public/                # Public assets
│   └── writable/              # Logs & cache
├── web-app/                   # Customer Web Application
│   ├── src/
│   │   ├── components/        # Reusable components
│   │   ├── pages/            # Page components
│   │   ├── hooks/            # Custom React hooks
│   │   ├── services/         # API services
│   │   ├── utils/            # Utility functions
│   │   └── styles/           # CSS/Tailwind styles
│   └── public/               # Static assets
├── admin-panel/              # Admin Dashboard
│   ├── src/
│   │   ├── components/       # Admin components
│   │   ├── pages/           # Admin pages
│   │   ├── services/        # Admin API services
│   │   └── utils/           # Admin utilities
│   └── public/              # Admin static assets
├── mobile-app/              # React Native Applications
│   ├── customer-app/        # Customer Mobile App
│   │   ├── src/
│   │   │   ├── screens/     # Screen components
│   │   │   ├── components/  # Mobile components
│   │   │   ├── navigation/  # Navigation setup
│   │   │   ├── services/    # Mobile API services
│   │   │   └── utils/       # Mobile utilities
│   │   ├── android/         # Android specific files
│   │   └── ios/            # iOS specific files
│   └── workboy-app/        # Work-Boy Mobile App
│       └── src/            # Similar structure to customer-app
├── shared/                 # Shared code across applications
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Shared utility functions
└── docs/                  # Documentation
    ├── api/              # API documentation
    ├── deployment/       # Deployment guides
    └── user-guides/      # User documentation
```

## Development Guidelines

### Code Style

1. **TypeScript/JavaScript**
   - Use TypeScript for all new code
   - Follow ESLint and Prettier configurations
   - Use meaningful variable and function names
   - Add JSDoc comments for complex functions

2. **PHP**
   - Follow PSR-12 coding standards
   - Use type hints where possible
   - Add PHPDoc comments for methods
   - Use CodeIgniter 4 conventions

3. **CSS/Styling**
   - Use TailwindCSS utility classes
   - Create custom components for repeated patterns
   - Follow mobile-first responsive design
   - Use consistent color scheme and spacing

### Git Workflow

1. **Branch Naming**
   - `feature/feature-name` - New features
   - `bugfix/bug-description` - Bug fixes
   - `hotfix/critical-fix` - Critical production fixes
   - `chore/task-description` - Maintenance tasks

2. **Commit Messages**
   ```
   type(scope): description
   
   Examples:
   feat(auth): add Google OAuth integration
   fix(api): resolve payment gateway timeout issue
   docs(readme): update installation instructions
   style(web): improve mobile responsiveness
   ```

3. **Pull Request Process**
   - Create feature branch from `develop`
   - Make changes and test thoroughly
   - Create pull request to `develop`
   - Request code review
   - Merge after approval and CI passes

### Testing Strategy

1. **Backend Testing**
```bash
cd backend
./vendor/bin/phpunit
```

2. **Frontend Testing**
```bash
# Web app
cd web-app
npm test

# Admin panel
cd admin-panel
npm test
```

3. **Mobile Testing**
```bash
# Customer app
cd mobile-app/customer-app
npm test

# Work-Boy app
cd mobile-app/workboy-app
npm test
```

### API Development

1. **Creating New Endpoints**
   - Add route in `backend/app/Config/Routes.php`
   - Create controller method
   - Add model methods if needed
   - Update API documentation
   - Write tests

2. **Database Changes**
   - Create migration file
   - Update model if needed
   - Update seeder if needed
   - Test migration up and down

3. **Authentication**
   - Use Firebase JWT tokens
   - Implement role-based access control
   - Add proper error handling

### Frontend Development

1. **Component Structure**
```jsx
// Example component structure
import React from 'react';
import { ComponentProps } from '../types';

interface Props extends ComponentProps {
  // Component-specific props
}

const ComponentName: React.FC<Props> = ({ ...props }) => {
  // Component logic
  
  return (
    <div className="component-styles">
      {/* Component JSX */}
    </div>
  );
};

export default ComponentName;
```

2. **State Management**
   - Use React Context for global state
   - Use local state for component-specific data
   - Implement proper error boundaries

3. **API Integration**
   - Use custom hooks for API calls
   - Implement proper loading states
   - Handle errors gracefully
   - Cache data when appropriate

### Mobile Development

1. **Navigation**
   - Use React Navigation v6
   - Implement proper deep linking
   - Handle navigation state properly

2. **Platform-Specific Code**
   - Use Platform.OS for platform checks
   - Implement platform-specific components when needed
   - Test on both iOS and Android

3. **Performance**
   - Optimize images and assets
   - Use FlatList for large lists
   - Implement proper memory management

## Environment Configuration

### Backend (.env)
```env
# Database
database.default.hostname = localhost
database.default.database = workboy_booking_dev
database.default.username = root
database.default.password = 

# Firebase (Development)
FIREBASE_PROJECT_ID = workboy-booking-dev
FIREBASE_PRIVATE_KEY_ID = your-dev-key-id
# ... other Firebase config

# APIs (Development)
GOOGLE_MAPS_API_KEY = your-dev-maps-key
RAZORPAY_KEY_ID = your-dev-razorpay-key
RAZORPAY_KEY_SECRET = your-dev-razorpay-secret
```

### Frontend (.env.development)
```env
REACT_APP_API_BASE_URL=http://localhost:8080/api/v1
REACT_APP_FIREBASE_API_KEY=your-dev-firebase-key
REACT_APP_GOOGLE_MAPS_API_KEY=your-dev-maps-key
REACT_APP_RAZORPAY_KEY_ID=your-dev-razorpay-key
```

## Debugging

### Backend Debugging
- Enable debug mode in `.env`: `CI_ENVIRONMENT = development`
- Check logs in `backend/writable/logs/`
- Use Xdebug for step-through debugging

### Frontend Debugging
- Use React Developer Tools
- Check browser console for errors
- Use Redux DevTools for state debugging

### Mobile Debugging
- Use React Native Debugger
- Enable remote debugging in development
- Use Flipper for advanced debugging

## Performance Monitoring

### Backend
- Monitor API response times
- Check database query performance
- Monitor memory usage

### Frontend
- Use Lighthouse for performance audits
- Monitor bundle sizes
- Check Core Web Vitals

### Mobile
- Monitor app startup time
- Check memory usage
- Monitor crash reports

## Deployment Process

1. **Development → Staging**
   - Merge feature branches to `develop`
   - Deploy to staging environment
   - Run integration tests
   - QA testing

2. **Staging → Production**
   - Merge `develop` to `main`
   - Create release tag
   - Deploy to production
   - Monitor for issues

## Troubleshooting

### Common Issues

1. **Backend not starting**
   - Check PHP version and extensions
   - Verify database connection
   - Check file permissions

2. **Frontend build failures**
   - Clear node_modules and reinstall
   - Check for TypeScript errors
   - Verify environment variables

3. **Mobile app crashes**
   - Check native dependencies
   - Clear React Native cache
   - Verify platform-specific configurations

### Getting Help

1. Check existing documentation
2. Search through project issues
3. Ask team members
4. Create detailed issue reports

## Best Practices

1. **Security**
   - Never commit sensitive data
   - Use environment variables
   - Validate all inputs
   - Implement proper authentication

2. **Performance**
   - Optimize database queries
   - Minimize bundle sizes
   - Use proper caching strategies
   - Optimize images and assets

3. **Maintainability**
   - Write clean, readable code
   - Add proper documentation
   - Use consistent naming conventions
   - Implement proper error handling

4. **Testing**
   - Write unit tests for critical functions
   - Test edge cases
   - Perform integration testing
   - Test on multiple devices/browsers
