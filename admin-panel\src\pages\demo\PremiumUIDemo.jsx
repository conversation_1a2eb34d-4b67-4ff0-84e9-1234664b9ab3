import React, { useState } from 'react'
import { Helmet } from 'react-helmet-async'
import {
  SparklesIcon,
  HeartIcon,
  StarIcon,
  BoltIcon,
  FireIcon,
  RocketLaunchIcon,
  GiftIcon,
  TrophyIcon,
  MagnifyingGlassIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
} from '@heroicons/react/24/outline'
import PremiumButton from '../../components/ui/PremiumButton'
import PremiumCard, { 
  PremiumCardHeader, 
  PremiumCardTitle, 
  PremiumCardContent, 
  PremiumCardFooter 
} from '../../components/ui/PremiumCard'
import PremiumInput, { PremiumTextarea } from '../../components/ui/PremiumInput'

export const PremiumUIDemo = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: ''
  })

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="space-y-8 animate-fade-in-up">
      <Helmet>
        <title>Premium UI Demo - WorkBoy Admin</title>
      </Helmet>

      {/* Header */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/10 via-brand-500/5 to-accent-600/10 rounded-3xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl border border-gray-200/60 p-8 shadow-xl">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-3xl bg-gradient-to-br from-primary-600 via-primary-500 to-brand-600 shadow-xl shadow-primary-500/30 mb-6">
              <SparklesIcon className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-4xl font-black bg-gradient-to-r from-gray-900 via-primary-800 to-brand-700 bg-clip-text text-transparent mb-4">
              Premium UI Components
            </h1>
            <p className="text-xl text-gray-600 font-medium max-w-2xl mx-auto">
              Experience the next generation of admin panel design with our premium components
            </p>
          </div>
        </div>
      </div>

      {/* Premium Buttons */}
      <PremiumCard gradient hover>
        <PremiumCardHeader>
          <PremiumCardTitle icon={BoltIcon}>Premium Buttons</PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-4">
              <h4 className="font-bold text-gray-900">Primary Variants</h4>
              <div className="space-y-3">
                <PremiumButton variant="primary" icon={RocketLaunchIcon}>
                  Launch Project
                </PremiumButton>
                <PremiumButton variant="primary" size="lg" glow>
                  Premium Action
                </PremiumButton>
                <PremiumButton variant="primary" loading>
                  Processing...
                </PremiumButton>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-bold text-gray-900">Color Variants</h4>
              <div className="space-y-3">
                <PremiumButton variant="success" icon={TrophyIcon}>
                  Success
                </PremiumButton>
                <PremiumButton variant="warning" icon={FireIcon}>
                  Warning
                </PremiumButton>
                <PremiumButton variant="danger" icon={HeartIcon}>
                  Danger
                </PremiumButton>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-bold text-gray-900">Style Variants</h4>
              <div className="space-y-3">
                <PremiumButton variant="outline" icon={GiftIcon}>
                  Outline
                </PremiumButton>
                <PremiumButton variant="ghost" icon={StarIcon}>
                  Ghost
                </PremiumButton>
                <PremiumButton variant="secondary" size="sm">
                  Secondary
                </PremiumButton>
              </div>
            </div>
          </div>
        </PremiumCardContent>
      </PremiumCard>

      {/* Premium Cards */}
      <PremiumCard gradient hover>
        <PremiumCardHeader>
          <PremiumCardTitle icon={SparklesIcon}>Premium Cards</PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <PremiumCard shadow="lg" hover glow animation="fade-in-left">
              <div className="text-center p-4">
                <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-primary-500 to-brand-600 flex items-center justify-center shadow-lg shadow-primary-500/25">
                  <StarIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Premium Feature</h3>
                <p className="text-gray-600">Enhanced functionality with premium styling</p>
              </div>
            </PremiumCard>
            
            <PremiumCard shadow="xl" hover gradient animation="fade-in-up">
              <div className="text-center p-4">
                <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-success-500 to-success-600 flex items-center justify-center shadow-lg shadow-success-500/25">
                  <TrophyIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Award Winning</h3>
                <p className="text-gray-600">Best in class design and user experience</p>
              </div>
            </PremiumCard>
            
            <PremiumCard shadow="premium" hover backdrop animation="fade-in-right">
              <div className="text-center p-4">
                <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center shadow-lg shadow-accent-500/25">
                  <FireIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">High Performance</h3>
                <p className="text-gray-600">Optimized for speed and efficiency</p>
              </div>
            </PremiumCard>
          </div>
        </PremiumCardContent>
      </PremiumCard>

      {/* Premium Form */}
      <PremiumCard gradient hover>
        <PremiumCardHeader>
          <PremiumCardTitle icon={UserIcon}>Premium Form Components</PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="max-w-2xl mx-auto">
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <PremiumInput
                  label="Full Name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  icon={UserIcon}
                  variant="premium"
                  placeholder="Enter your full name"
                />
                <PremiumInput
                  label="Email Address"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  icon={EnvelopeIcon}
                  variant="premium"
                  placeholder="Enter your email"
                />
              </div>
              
              <PremiumInput
                label="Phone Number"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                icon={PhoneIcon}
                variant="premium"
                placeholder="Enter your phone number"
              />
              
              <PremiumTextarea
                label="Message"
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                variant="premium"
                placeholder="Enter your message here..."
                rows={4}
              />
              
              <div className="flex justify-end space-x-4">
                <PremiumButton variant="outline">
                  Cancel
                </PremiumButton>
                <PremiumButton variant="primary" icon={RocketLaunchIcon}>
                  Submit Form
                </PremiumButton>
              </div>
            </form>
          </div>
        </PremiumCardContent>
      </PremiumCard>
    </div>
  )
}

export default PremiumUIDemo
