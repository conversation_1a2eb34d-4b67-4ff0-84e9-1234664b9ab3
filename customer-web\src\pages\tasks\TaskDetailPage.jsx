import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useLoading } from '../../contexts/LoadingContext'
import Layout from '../../components/layout/Layout'
import { taskService } from '../../services/taskService'
import { formatDate, formatTime, formatCurrency } from '../../utils/formatters'

const TaskDetailPage = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { setLoading } = useLoading()
  
  const [task, setTask] = useState(null)
  const [error, setError] = useState('')

  useEffect(() => {
    loadTask()
  }, [id])

  const loadTask = async () => {
    setLoading(true)
    try {
      const response = await taskService.getTask(id)
      setTask(response.data)
    } catch (error) {
      console.error('Error loading task:', error)
      setError('Failed to load task details')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-blue-100 text-blue-800',
      in_progress: 'bg-purple-100 text-purple-800',
      completed: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  const getStatusText = (status) => {
    const texts = {
      pending: 'Pending',
      confirmed: 'Confirmed',
      in_progress: 'In Progress',
      completed: 'Completed',
      cancelled: 'Cancelled'
    }
    return texts[status] || status
  }

  const handleCancelTask = async () => {
    if (!window.confirm('Are you sure you want to cancel this task?')) return
    
    setLoading(true)
    try {
      await taskService.cancelTask(id)
      setTask(prev => ({ ...prev, status: 'cancelled' }))
    } catch (error) {
      console.error('Error cancelling task:', error)
      setError('Failed to cancel task')
    } finally {
      setLoading(false)
    }
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600">{error}</p>
            <button
              onClick={() => navigate('/tasks')}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
            >
              Back to Tasks
            </button>
          </div>
        </div>
      </Layout>
    )
  }

  if (!task) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="bg-white rounded-lg shadow-soft p-6">
              <div className="h-8 bg-gray-200 rounded mb-4"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-soft p-6">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{task.title}</h1>
              <div className="flex items-center gap-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(task.status)}`}>
                  {getStatusText(task.status)}
                </span>
                <span className="text-gray-600">Task #{task.id}</span>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-primary-600">
                {formatCurrency(task.total_amount || task.estimated_amount)}
              </p>
              <p className="text-gray-600 text-sm">
                {task.total_amount ? 'Final Amount' : 'Estimated Amount'}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={() => navigate('/tasks')}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              ← Back to Tasks
            </button>
            {task.status === 'pending' && (
              <button
                onClick={handleCancelTask}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Cancel Task
              </button>
            )}
          </div>
        </div>

        {/* Task Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow-soft p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Task Information</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Category</label>
                <p className="text-gray-900">{task.category?.name}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Description</label>
                <p className="text-gray-900">{task.description}</p>
              </div>
              
              {task.special_instructions && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Special Instructions</label>
                  <p className="text-gray-900">{task.special_instructions}</p>
                </div>
              )}
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Estimated Duration</label>
                <p className="text-gray-900">{task.estimated_duration} minutes</p>
              </div>
            </div>
          </div>

          {/* Scheduling & Location */}
          <div className="bg-white rounded-lg shadow-soft p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Schedule & Location</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Scheduled Date</label>
                <p className="text-gray-900">{formatDate(task.scheduled_date)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Scheduled Time</label>
                <p className="text-gray-900">{formatTime(task.scheduled_time)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Service Address</label>
                <div className="text-gray-900">
                  <p>{task.address?.street_address}</p>
                  <p>{task.address?.city}, {task.address?.state} {task.address?.postal_code}</p>
                  {task.address?.country && <p>{task.address.country}</p>}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Workboy Information */}
        {task.workboy && (
          <div className="bg-white rounded-lg shadow-soft p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Assigned Workboy</h2>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-primary-600 font-semibold text-lg">
                  {task.workboy.first_name?.[0]}{task.workboy.last_name?.[0]}
                </span>
              </div>
              <div>
                <p className="font-medium text-gray-900">
                  {task.workboy.first_name} {task.workboy.last_name}
                </p>
                <p className="text-gray-600">{task.workboy.email}</p>
                {task.workboy.phone && (
                  <p className="text-gray-600">{task.workboy.phone}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Task Timeline */}
        {task.status_history && task.status_history.length > 0 && (
          <div className="bg-white rounded-lg shadow-soft p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Task Timeline</h2>
            <div className="space-y-4">
              {task.status_history.map((history, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className={`w-3 h-3 rounded-full mt-1 ${getStatusColor(history.status).replace('text-', 'bg-').replace('100', '500')}`}></div>
                  <div>
                    <p className="font-medium text-gray-900">{getStatusText(history.status)}</p>
                    <p className="text-gray-600 text-sm">
                      {formatDate(history.created_at)} at {formatTime(history.created_at)}
                    </p>
                    {history.notes && (
                      <p className="text-gray-700 text-sm mt-1">{history.notes}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Payment Information */}
        {task.payment && (
          <div className="bg-white rounded-lg shadow-soft p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Payment Status</label>
                <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                  task.payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                  task.payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {task.payment.status}
                </span>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                <p className="text-gray-900">{task.payment.payment_method}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Amount</label>
                <p className="text-gray-900">{formatCurrency(task.payment.amount)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Transaction ID</label>
                <p className="text-gray-900 font-mono text-sm">{task.payment.transaction_id}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}

export default TaskDetailPage
