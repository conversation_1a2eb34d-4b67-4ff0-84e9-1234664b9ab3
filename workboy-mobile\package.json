{"name": "workboy-workboy-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^6.3.20", "axios": "^1.6.2", "date-fns": "^2.30.0", "expo": "~53.0.0", "expo-async-storage": "@react-native-async-storage/async-storage", "expo-blur": "~14.1.5", "expo-camera": "~16.1.10", "expo-constants": "~17.1.7", "expo-device": "~7.1.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-notifications": "~0.31.4", "expo-router": "~5.1.3", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-hook-form": "^7.48.2", "react-native": "0.79.5", "react-native-animatable": "^1.4.0", "react-native-background-job": "^2.3.1", "react-native-background-timer": "^2.4.1", "react-native-chart-kit": "^6.12.0", "react-native-collapsible": "^1.6.1", "react-native-document-picker": "^9.1.1", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.24.0", "react-native-image-viewing": "^0.2.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-maps": "1.20.1", "react-native-maps-directions": "^1.9.0", "react-native-modal": "^13.0.1", "react-native-paper": "^5.12.3", "react-native-progress": "^5.0.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.2.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-share": "^10.0.2", "react-native-skeleton-placeholder": "^5.2.4", "react-native-step-indicator": "^1.0.3", "react-native-super-grid": "^5.0.0", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.2.0", "react-native-vector-icons": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "^5.1.3"}, "private": true}