{"expo": {"name": "WorkBoy Partner", "slug": "workboy-partner", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#10b981"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.workboy.partner", "buildNumber": "1.0.0", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs access to your location to show nearby tasks and provide accurate service delivery.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs access to your location to track your work progress and provide location-based services.", "NSLocationAlwaysUsageDescription": "This app needs access to your location even when closed to track work progress and notify customers of your arrival.", "NSCameraUsageDescription": "This app needs access to your camera to take photos for task documentation and profile verification.", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to select images for task documentation and profile setup.", "NSMicrophoneUsageDescription": "This app needs access to your microphone for voice notes and communication features.", "UIBackgroundModes": ["location", "background-processing", "background-fetch"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#10b981"}, "package": "com.workboy.partner", "versionCode": 1, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "RECORD_AUDIO", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK", "FOREGROUND_SERVICE", "SYSTEM_ALERT_WINDOW"], "googleServicesFile": "./google-services.json"}, "plugins": ["expo-router", ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#10b981", "sounds": ["./assets/notification-sound.wav"]}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow WorkBoy Partner to use your location to show nearby tasks and provide accurate service delivery.", "locationAlwaysPermission": "Allow WorkBoy Partner to track your location even when the app is closed to provide location-based services.", "locationWhenInUsePermission": "Allow WorkBoy Partner to use your location to show nearby tasks and provide accurate service delivery."}], ["expo-camera", {"cameraPermission": "Allow WorkBoy Partner to access your camera to take photos for task documentation and profile verification."}], ["expo-image-picker", {"photosPermission": "Allow WorkBoy Partner to access your photo library to select images for task documentation and profile setup."}], "expo-font", "expo-web-browser", "expo-secure-store"], "scheme": "workboy-partner", "extra": {"router": {"origin": false}, "eas": {"projectId": "your-eas-project-id-workboy"}}, "owner": "workboy-team"}}