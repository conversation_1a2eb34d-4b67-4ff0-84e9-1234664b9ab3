import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { showToast } from '@utils/toast';

// API Configuration
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:8080/api/v1' 
  : 'https://your-production-api.com/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await SecureStore.getItemAsync('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    // Return the data directly for successful responses
    return response.data;
  },
  async (error) => {
    console.error('API Error:', error);
    
    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - clear auth and redirect to login
          showToast('Session expired. Please login again.', 'error');
          await SecureStore.deleteItemAsync('authToken');
          // You might want to trigger a logout action here
          break;
          
        case 403:
          showToast('Access denied. You don\'t have permission to perform this action.', 'error');
          break;
          
        case 404:
          showToast('Resource not found.', 'error');
          break;
          
        case 422:
          // Validation errors
          if (data.errors) {
            const errorMessages = Object.values(data.errors).flat();
            errorMessages.forEach(message => showToast(message, 'error'));
          } else {
            showToast(data.message || 'Validation error occurred.', 'error');
          }
          break;
          
        case 429:
          showToast('Too many requests. Please try again later.', 'error');
          break;
          
        case 500:
          showToast('Server error. Please try again later.', 'error');
          break;
          
        default:
          showToast(data.message || 'An unexpected error occurred.', 'error');
      }
      
      return Promise.reject(error.response.data);
    } else if (error.request) {
      // Network error
      showToast('Network error. Please check your connection.', 'error');
      return Promise.reject({ message: 'Network error' });
    } else {
      // Other error
      showToast('An unexpected error occurred.', 'error');
      return Promise.reject({ message: error.message });
    }
  }
);

// API endpoints
export const endpoints = {
  // Authentication
  auth: {
    register: '/auth/register',
    login: '/auth/login',
    profile: '/auth/profile',
    logout: '/auth/logout',
    refreshToken: '/auth/refresh-token',
  },
  
  // Tasks
  tasks: {
    list: '/tasks',
    create: '/tasks',
    detail: (id) => `/tasks/${id}`,
    update: (id) => `/tasks/${id}`,
    delete: (id) => `/tasks/${id}`,
    categories: '/tasks/categories',
    updateStatus: (id) => `/tasks/${id}/status`,
  },
  
  // Customer
  customer: {
    dashboard: '/customer/dashboard',
    addresses: '/customer/addresses',
    createAddress: '/customer/addresses',
    updateAddress: (id) => `/customer/addresses/${id}`,
    deleteAddress: (id) => `/customer/addresses/${id}`,
    preferences: '/customer/preferences',
  },
  
  // Payments
  payments: {
    create: '/payments/create',
    confirm: '/payments/confirm',
    history: '/payments/history',
  },
  
  // Reviews
  reviews: {
    create: '/reviews',
    workboy: (id) => `/reviews/workboy/${id}`,
    customer: '/reviews/customer',
  },
  
  // Notifications
  notifications: {
    list: '/notifications',
    markRead: (id) => `/notifications/${id}/read`,
    markAllRead: '/notifications/read-all',
    delete: (id) => `/notifications/${id}`,
    preferences: '/notifications/preferences',
  },
};

// API service methods
export const apiService = {
  // GET request
  get: async (url, config = {}) => {
    try {
      return await api.get(url, config);
    } catch (error) {
      throw error;
    }
  },
  
  // POST request
  post: async (url, data = {}, config = {}) => {
    try {
      return await api.post(url, data, config);
    } catch (error) {
      throw error;
    }
  },
  
  // PUT request
  put: async (url, data = {}, config = {}) => {
    try {
      return await api.put(url, data, config);
    } catch (error) {
      throw error;
    }
  },
  
  // DELETE request
  delete: async (url, config = {}) => {
    try {
      return await api.delete(url, config);
    } catch (error) {
      throw error;
    }
  },
  
  // Upload file
  uploadFile: async (url, file, onProgress = null) => {
    const formData = new FormData();
    formData.append('file', {
      uri: file.uri,
      type: file.type,
      name: file.name || 'file',
    });
    
    try {
      return await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            onProgress(percentCompleted);
          }
        },
      });
    } catch (error) {
      throw error;
    }
  },
  
  // Upload multiple files
  uploadFiles: async (url, files, onProgress = null) => {
    const formData = new FormData();
    
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, {
        uri: file.uri,
        type: file.type,
        name: file.name || `file_${index}`,
      });
    });
    
    try {
      return await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            onProgress(percentCompleted);
          }
        },
      });
    } catch (error) {
      throw error;
    }
  },
};

export default apiService;
