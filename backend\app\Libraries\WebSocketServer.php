<?php

namespace App\Libraries;

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;
use App\Models\UserModel;
use App\Models\TaskModel;
use CodeIgniter\Database\Config;

class WebSocketServer implements MessageComponentInterface
{
    protected $clients;
    protected $userConnections;
    protected $db;
    
    public function __construct()
    {
        $this->clients = new \SplObjectStorage;
        $this->userConnections = [];
        $this->db = \Config\Database::connect();
    }

    public function onOpen(ConnectionInterface $conn)
    {
        // Store the new connection
        $this->clients->attach($conn);
        
        echo "New connection! ({$conn->resourceId})\n";
    }

    public function onMessage(ConnectionInterface $from, $msg)
    {
        $data = json_decode($msg, true);
        
        if (!$data || !isset($data['type'])) {
            $this->sendError($from, 'Invalid message format');
            return;
        }

        switch ($data['type']) {
            case 'authenticate':
                $this->handleAuthentication($from, $data);
                break;
                
            case 'join_room':
                $this->handleJoinRoom($from, $data);
                break;
                
            case 'location_update':
                $this->handleLocationUpdate($from, $data);
                break;
                
            case 'task_update':
                $this->handleTaskUpdate($from, $data);
                break;
                
            case 'ping':
                $this->sendMessage($from, ['type' => 'pong']);
                break;
                
            default:
                $this->sendError($from, 'Unknown message type');
        }
    }

    public function onClose(ConnectionInterface $conn)
    {
        // Remove the connection
        $this->clients->detach($conn);
        
        // Remove from user connections
        foreach ($this->userConnections as $userId => $connections) {
            if (($key = array_search($conn, $connections)) !== false) {
                unset($this->userConnections[$userId][$key]);
                if (empty($this->userConnections[$userId])) {
                    unset($this->userConnections[$userId]);
                }
                break;
            }
        }
        
        echo "Connection {$conn->resourceId} has disconnected\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e)
    {
        echo "An error has occurred: {$e->getMessage()}\n";
        $conn->close();
    }

    protected function handleAuthentication(ConnectionInterface $conn, $data)
    {
        if (!isset($data['token'])) {
            $this->sendError($conn, 'Token required');
            return;
        }

        // Verify JWT token
        $userId = $this->verifyToken($data['token']);
        if (!$userId) {
            $this->sendError($conn, 'Invalid token');
            return;
        }

        // Store user connection
        if (!isset($this->userConnections[$userId])) {
            $this->userConnections[$userId] = [];
        }
        $this->userConnections[$userId][] = $conn;
        
        // Store user ID in connection
        $conn->userId = $userId;
        
        $this->sendMessage($conn, [
            'type' => 'authenticated',
            'user_id' => $userId
        ]);
        
        echo "User {$userId} authenticated\n";
    }

    protected function handleJoinRoom(ConnectionInterface $conn, $data)
    {
        if (!isset($conn->userId)) {
            $this->sendError($conn, 'Not authenticated');
            return;
        }

        $roomId = $data['room_id'] ?? null;
        if (!$roomId) {
            $this->sendError($conn, 'Room ID required');
            return;
        }

        // Store room information
        if (!isset($conn->rooms)) {
            $conn->rooms = [];
        }
        $conn->rooms[] = $roomId;
        
        $this->sendMessage($conn, [
            'type' => 'joined_room',
            'room_id' => $roomId
        ]);
        
        echo "User {$conn->userId} joined room {$roomId}\n";
    }

    protected function handleLocationUpdate(ConnectionInterface $conn, $data)
    {
        if (!isset($conn->userId)) {
            $this->sendError($conn, 'Not authenticated');
            return;
        }

        $latitude = $data['latitude'] ?? null;
        $longitude = $data['longitude'] ?? null;
        
        if (!$latitude || !$longitude) {
            $this->sendError($conn, 'Location coordinates required');
            return;
        }

        // Update location in database
        $this->updateUserLocation($conn->userId, $latitude, $longitude);
        
        // Broadcast location to relevant users (customers tracking this work-boy)
        $this->broadcastLocationUpdate($conn->userId, $latitude, $longitude);
        
        echo "Location updated for user {$conn->userId}\n";
    }

    protected function handleTaskUpdate(ConnectionInterface $conn, $data)
    {
        if (!isset($conn->userId)) {
            $this->sendError($conn, 'Not authenticated');
            return;
        }

        $taskId = $data['task_id'] ?? null;
        $status = $data['status'] ?? null;
        
        if (!$taskId || !$status) {
            $this->sendError($conn, 'Task ID and status required');
            return;
        }

        // Verify user has permission to update this task
        if (!$this->canUpdateTask($conn->userId, $taskId)) {
            $this->sendError($conn, 'Permission denied');
            return;
        }

        // Update task status in database
        $this->updateTaskStatus($taskId, $status, $data);
        
        // Broadcast task update to relevant users
        $this->broadcastTaskUpdate($taskId, $status, $data);
        
        echo "Task {$taskId} updated by user {$conn->userId}\n";
    }

    protected function verifyToken($token)
    {
        try {
            // Implement JWT token verification
            // This should match your existing JWT implementation
            $key = getenv('JWT_SECRET_KEY');
            $decoded = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key($key, 'HS256'));
            return $decoded->user_id ?? null;
        } catch (\Exception $e) {
            return null;
        }
    }

    protected function updateUserLocation($userId, $latitude, $longitude)
    {
        $builder = $this->db->table('users');
        $builder->where('id', $userId);
        $builder->update([
            'current_latitude' => $latitude,
            'current_longitude' => $longitude,
            'location_updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    protected function broadcastLocationUpdate($userId, $latitude, $longitude)
    {
        // Find customers who are tracking this work-boy
        $builder = $this->db->table('tasks');
        $builder->select('customer_id');
        $builder->where('workboy_id', $userId);
        $builder->whereIn('status', ['assigned', 'in_progress']);
        $query = $builder->get();
        
        $customerIds = array_column($query->getResultArray(), 'customer_id');
        
        $message = [
            'type' => 'location_update',
            'workboy_id' => $userId,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'timestamp' => time()
        ];
        
        foreach ($customerIds as $customerId) {
            $this->sendToUser($customerId, $message);
        }
    }

    protected function canUpdateTask($userId, $taskId)
    {
        $builder = $this->db->table('tasks');
        $builder->where('id', $taskId);
        $builder->where('workboy_id', $userId);
        $query = $builder->get();
        
        return $query->getNumRows() > 0;
    }

    protected function updateTaskStatus($taskId, $status, $data)
    {
        $updateData = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // Add additional data based on status
        switch ($status) {
            case 'started':
                $updateData['started_at'] = date('Y-m-d H:i:s');
                break;
            case 'completed':
                $updateData['completed_at'] = date('Y-m-d H:i:s');
                if (isset($data['completion_notes'])) {
                    $updateData['completion_notes'] = $data['completion_notes'];
                }
                break;
        }
        
        $builder = $this->db->table('tasks');
        $builder->where('id', $taskId);
        $builder->update($updateData);
    }

    protected function broadcastTaskUpdate($taskId, $status, $data)
    {
        // Get task details
        $builder = $this->db->table('tasks');
        $builder->where('id', $taskId);
        $query = $builder->get();
        $task = $query->getRowArray();
        
        if (!$task) return;
        
        $message = [
            'type' => 'task_update',
            'task_id' => $taskId,
            'status' => $status,
            'data' => $data,
            'timestamp' => time()
        ];
        
        // Send to customer
        $this->sendToUser($task['customer_id'], $message);
        
        // Send to work-boy
        $this->sendToUser($task['workboy_id'], $message);
        
        // Send to admin users
        $this->broadcastToAdmins($message);
    }

    protected function sendToUser($userId, $message)
    {
        if (isset($this->userConnections[$userId])) {
            foreach ($this->userConnections[$userId] as $conn) {
                $this->sendMessage($conn, $message);
            }
        }
    }

    protected function broadcastToAdmins($message)
    {
        // Get admin user IDs
        $builder = $this->db->table('users');
        $builder->select('id');
        $builder->where('user_type', 'admin');
        $query = $builder->get();
        
        $adminIds = array_column($query->getResultArray(), 'id');
        
        foreach ($adminIds as $adminId) {
            $this->sendToUser($adminId, $message);
        }
    }

    protected function sendMessage(ConnectionInterface $conn, $message)
    {
        $conn->send(json_encode($message));
    }

    protected function sendError(ConnectionInterface $conn, $error)
    {
        $this->sendMessage($conn, [
            'type' => 'error',
            'message' => $error
        ]);
    }

    public static function start($port = 8080)
    {
        $server = IoServer::factory(
            new HttpServer(
                new WsServer(
                    new WebSocketServer()
                )
            ),
            $port
        );

        echo "WebSocket server started on port {$port}\n";
        $server->run();
    }
}
