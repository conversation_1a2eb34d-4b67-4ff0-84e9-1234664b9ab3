import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Linking,
  Alert,
} from 'react-native';
import { Card, List, Button, Searchbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@constants/theme';

const HelpScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = React.useState('');

  const faqItems = [
    {
      question: 'How do I book a service?',
      answer: 'You can book a service by browsing available Work-Boys in your area and selecting the one that best fits your needs.',
    },
    {
      question: 'How do I cancel a booking?',
      answer: 'You can cancel a booking up to 2 hours before the scheduled time through the task details screen.',
    },
    {
      question: 'How do I pay for services?',
      answer: 'Payment is processed automatically through your selected payment method after the service is completed.',
    },
    {
      question: 'What if I\'m not satisfied with the service?',
      answer: 'You can rate and review the Work-Boy after service completion. For serious issues, please contact our support team.',
    },
    {
      question: 'How do I change my profile information?',
      answer: 'Go to Profile > Edit Profile to update your personal information, contact details, and preferences.',
    },
  ];

  const supportOptions = [
    {
      title: 'Contact Support',
      description: 'Get help from our support team',
      icon: 'headset-outline',
      onPress: () => {
        Alert.alert(
          'Contact Support',
          'Choose how you\'d like to contact us:',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Email', onPress: () => Linking.openURL('mailto:<EMAIL>') },
            { text: 'Phone', onPress: () => Linking.openURL('tel:+1234567890') },
          ]
        );
      },
    },
    {
      title: 'Report a Problem',
      description: 'Report issues with the app or services',
      icon: 'bug-outline',
      onPress: () => {
        Alert.alert('Report Problem', 'Problem reporting functionality would be implemented here.');
      },
    },
    {
      title: 'Feedback',
      description: 'Share your thoughts and suggestions',
      icon: 'chatbubble-outline',
      onPress: () => {
        Alert.alert('Feedback', 'Feedback form would be displayed here.');
      },
    },
    {
      title: 'Rate the App',
      description: 'Rate WorkBoy on the App Store',
      icon: 'star-outline',
      onPress: () => {
        Alert.alert('Rate App', 'This would redirect to the app store for rating.');
      },
    },
  ];

  const filteredFAQ = faqItems.filter(item =>
    item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>Help & Support</Text>

        {/* Search */}
        <Searchbar
          placeholder="Search help topics..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />

        {/* Quick Actions */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Get Help</Text>
            {supportOptions.map((option, index) => (
              <React.Fragment key={option.title}>
                <List.Item
                  title={option.title}
                  description={option.description}
                  left={(props) => (
                    <List.Icon
                      {...props}
                      icon={({ size, color }) => (
                        <Ionicons name={option.icon} size={size} color={color} />
                      )}
                    />
                  )}
                  right={(props) => (
                    <List.Icon {...props} icon="chevron-forward" />
                  )}
                  onPress={option.onPress}
                  style={styles.listItem}
                />
                {index < supportOptions.length - 1 && (
                  <View style={styles.divider} />
                )}
              </React.Fragment>
            ))}
          </Card.Content>
        </Card>

        {/* FAQ Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
            {filteredFAQ.length > 0 ? (
              filteredFAQ.map((item, index) => (
                <React.Fragment key={item.question}>
                  <List.Accordion
                    title={item.question}
                    titleStyle={styles.faqQuestion}
                    left={(props) => (
                      <List.Icon
                        {...props}
                        icon={({ size, color }) => (
                          <Ionicons name="help-circle-outline" size={size} color={color} />
                        )}
                      />
                    )}
                  >
                    <List.Item
                      title={item.answer}
                      titleNumberOfLines={0}
                      titleStyle={styles.faqAnswer}
                    />
                  </List.Accordion>
                  {index < filteredFAQ.length - 1 && (
                    <View style={styles.divider} />
                  )}
                </React.Fragment>
              ))
            ) : (
              <Text style={styles.noResults}>
                No help topics found for "{searchQuery}"
              </Text>
            )}
          </Card.Content>
        </Card>

        {/* Emergency Contact */}
        <Card style={styles.emergencyCard}>
          <Card.Content>
            <View style={styles.emergencyHeader}>
              <Ionicons name="warning" size={24} color={colors.error[500]} />
              <Text style={styles.emergencyTitle}>Emergency?</Text>
            </View>
            <Text style={styles.emergencyText}>
              For urgent safety concerns during a service, contact emergency services immediately.
            </Text>
            <Button
              mode="contained"
              onPress={() => Linking.openURL('tel:911')}
              style={styles.emergencyButton}
              buttonColor={colors.error[500]}
              icon={({ size, color }) => (
                <Ionicons name="call" size={size} color={color} />
              )}
            >
              Call 911
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.lg,
  },
  searchbar: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  card: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.md,
  },
  listItem: {
    paddingVertical: spacing.sm,
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginLeft: spacing.xl,
  },
  faqQuestion: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[900],
  },
  faqAnswer: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    lineHeight: 22,
  },
  noResults: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: spacing.lg,
  },
  emergencyCard: {
    backgroundColor: colors.error[50],
    borderColor: colors.error[200],
    borderWidth: 1,
  },
  emergencyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  emergencyTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.error[700],
    marginLeft: spacing.sm,
  },
  emergencyText: {
    fontSize: typography.fontSize.base,
    color: colors.error[700],
    marginBottom: spacing.md,
    lineHeight: 22,
  },
  emergencyButton: {
    marginTop: spacing.sm,
  },
});

export default HelpScreen;
