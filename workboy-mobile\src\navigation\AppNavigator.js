import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuth } from '@context/AuthContext';

// Auth Screens
import OnboardingScreen from '@screens/auth/OnboardingScreen';
import LoginScreen from '@screens/auth/LoginScreen';
import RegisterScreen from '@screens/auth/RegisterScreen';
import ForgotPasswordScreen from '@screens/auth/ForgotPasswordScreen';
import KYCScreen from '@screens/auth/KYCScreen';

// Main App Navigation
import MainTabNavigator from './MainTabNavigator';

// Loading Screen
import LoadingScreen from '@screens/LoadingScreen';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const { user, loading, isFirstLaunch, userProfile } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        gestureDirection: 'horizontal',
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      {user ? (
        // User is signed in
        <>
          {userProfile?.kyc_status !== 'approved' ? (
            // KYC not completed or approved
            <Stack.Screen name="KYC" component={KYCScreen} />
          ) : (
            // User is verified and can access main app
            <Stack.Screen name="Main" component={MainTabNavigator} />
          )}
        </>
      ) : (
        // User is not signed in
        <>
          {isFirstLaunch && (
            <Stack.Screen name="Onboarding" component={OnboardingScreen} />
          )}
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="Register" component={RegisterScreen} />
          <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
        </>
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
