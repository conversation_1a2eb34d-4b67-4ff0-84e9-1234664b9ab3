import React from 'react'
import { clsx } from 'clsx'

export const PremiumCard = ({
  children,
  className = '',
  padding = 'default',
  shadow = 'default',
  hover = true,
  gradient = false,
  glow = false,
  border = true,
  backdrop = true,
  animation = 'fade-in-up',
  ...props
}) => {
  const paddingClasses = {
    none: '',
    xs: 'p-3',
    sm: 'p-4',
    default: 'p-6',
    lg: 'p-8',
    xl: 'p-10'
  }

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    default: 'shadow-card',
    md: 'shadow-medium',
    lg: 'shadow-large',
    xl: 'shadow-xl',
    premium: 'shadow-premium'
  }

  const animationClasses = {
    none: '',
    'fade-in': 'animate-fade-in',
    'fade-in-up': 'animate-fade-in-up',
    'fade-in-down': 'animate-fade-in-down',
    'fade-in-left': 'animate-fade-in-left',
    'fade-in-right': 'animate-fade-in-right',
    'scale-in': 'animate-scale-in',
    'bounce-in': 'animate-bounce-in'
  }

  return (
    <div className="group relative overflow-hidden">
      {/* Background gradient overlay */}
      {gradient && (
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-brand-50/20 to-accent-50/30 rounded-2xl opacity-50 group-hover:opacity-70 transition-opacity duration-300"></div>
      )}
      
      {/* Main card */}
      <div
        className={clsx(
          'relative transition-all duration-300',
          backdrop ? 'bg-white/90 backdrop-blur-sm' : 'bg-white',
          'rounded-2xl',
          border && 'border border-gray-200/60',
          paddingClasses[padding],
          shadowClasses[shadow],
          hover && 'hover:shadow-xl hover:-translate-y-1 hover:border-gray-300/60 cursor-pointer',
          glow && 'hover:shadow-glow',
          animationClasses[animation],
          className
        )}
        {...props}
      >
        {children}
        
        {/* Shimmer effect on hover */}
        {hover && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 pointer-events-none"></div>
        )}
      </div>
    </div>
  )
}

export const PremiumCardHeader = ({ children, className = '', ...props }) => (
  <div className={clsx('mb-6 pb-4 border-b border-gray-200/60', className)} {...props}>
    {children}
  </div>
)

export const PremiumCardTitle = ({ children, className = '', icon: Icon, ...props }) => (
  <div className="flex items-center space-x-3">
    {Icon && (
      <div className="p-2 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 shadow-lg shadow-primary-500/25">
        <Icon className="h-5 w-5 text-white" />
      </div>
    )}
    <h3 className={clsx('text-lg font-bold text-gray-900 group-hover:text-primary-700 transition-colors duration-300', className)} {...props}>
      {children}
    </h3>
  </div>
)

export const PremiumCardContent = ({ children, className = '', ...props }) => (
  <div className={clsx('space-y-4', className)} {...props}>
    {children}
  </div>
)

export const PremiumCardFooter = ({ children, className = '', ...props }) => (
  <div className={clsx('mt-6 pt-4 border-t border-gray-200/60 flex items-center justify-between', className)} {...props}>
    {children}
  </div>
)

export default PremiumCard
