<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Models\UserModel;

/**
 * Authentication Filter for Work-Boy Booking API
 * 
 * This filter validates JWT tokens and ensures only authenticated users
 * can access protected routes.
 */
class AuthFilter implements FilterInterface
{
    /**
     * JWT secret key
     */
    private $jwtSecret;

    public function __construct()
    {
        $this->jwtSecret = env('JWT_SECRET', 'your-default-secret-key');
    }

    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Get JWT token from Authorization header
        $token = $this->getJWTFromRequest($request);
        
        if (!$token) {
            return $this->unauthorizedResponse('Authorization token required');
        }

        // Verify JWT token
        $userData = $this->verifyJWT($token);
        
        if (!$userData) {
            return $this->unauthorizedResponse('Invalid or expired token');
        }

        // Check if user exists and is active
        $userModel = new UserModel();
        $user = $userModel->find($userData['id']);
        
        if (!$user || $user['status'] !== 'active') {
            return $this->unauthorizedResponse('User account is inactive or not found');
        }

        // Store user data in request for use in controllers
        $request->user = $user;
        $request->userData = $userData;
        
        return $request;
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do after request
    }

    /**
     * Get JWT token from request header
     * 
     * @param RequestInterface $request
     * @return string|null
     */
    private function getJWTFromRequest(RequestInterface $request)
    {
        $header = $request->getHeader('Authorization');
        
        if ($header && !empty($header->getValue())) {
            $authHeader = $header->getValue();
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }

    /**
     * Verify JWT token and get user data
     * 
     * @param string $token JWT token
     * @return array|null User data or null if invalid
     */
    private function verifyJWT(string $token)
    {
        try {
            $decoded = JWT::decode($token, new Key($this->jwtSecret, 'HS256'));
            return (array) $decoded;
        } catch (\Exception $e) {
            log_message('error', 'JWT verification failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Return unauthorized response
     * 
     * @param string $message Error message
     * @return ResponseInterface
     */
    private function unauthorizedResponse(string $message = 'Unauthorized')
    {
        $response = service('response');
        
        $data = [
            'success' => false,
            'message' => $message,
        ];
        
        return $response->setJSON($data)->setStatusCode(401);
    }
}
