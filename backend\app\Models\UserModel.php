<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table            = 'users';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'firebase_uid',
        'email',
        'phone',
        'first_name',
        'last_name',
        'profile_image',
        'user_type',
        'status',
        'email_verified',
        'phone_verified'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'firebase_uid' => 'required|max_length[128]|is_unique[users.firebase_uid,id,{id}]',
        'email'        => 'required|valid_email|max_length[255]|is_unique[users.email,id,{id}]',
        'phone'        => 'permit_empty|max_length[20]|is_unique[users.phone,id,{id}]',
        'first_name'   => 'required|max_length[100]',
        'last_name'    => 'required|max_length[100]',
        'profile_image'=> 'permit_empty|max_length[500]',
        'user_type'    => 'required|in_list[customer,workboy,admin]',
        'status'       => 'permit_empty|in_list[active,inactive,suspended,pending_verification]',
        'email_verified' => 'permit_empty|in_list[0,1]',
        'phone_verified' => 'permit_empty|in_list[0,1]',
    ];

    protected $validationMessages = [
        'firebase_uid' => [
            'required'  => 'Firebase UID is required',
            'is_unique' => 'This Firebase UID is already registered',
        ],
        'email' => [
            'required'    => 'Email is required',
            'valid_email' => 'Please provide a valid email address',
            'is_unique'   => 'This email is already registered',
        ],
        'phone' => [
            'is_unique' => 'This phone number is already registered',
        ],
        'first_name' => [
            'required' => 'First name is required',
        ],
        'last_name' => [
            'required' => 'Last name is required',
        ],
        'user_type' => [
            'required' => 'User type is required',
            'in_list'  => 'User type must be customer, workboy, or admin',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = ['createUserProfile'];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Create user profile after user creation
     */
    protected function createUserProfile(array $data)
    {
        if (!isset($data['data']) || !isset($data['id'])) {
            return $data;
        }

        $userData = $data['data'];
        $userId = $data['id'];

        // Create appropriate profile based on user type
        if (isset($userData['user_type'])) {
            switch ($userData['user_type']) {
                case 'customer':
                    $customerProfileModel = new CustomerProfileModel();
                    $customerProfileModel->insert([
                        'user_id' => $userId,
                        'preferred_payment_method' => 'card',
                        'notification_preferences' => json_encode([
                            'email_notifications' => true,
                            'push_notifications' => true,
                            'sms_notifications' => false,
                            'task_updates' => true,
                            'promotional' => true,
                        ]),
                    ]);
                    break;

                case 'workboy':
                    $workboyProfileModel = new WorkboyProfileModel();
                    $workboyProfileModel->insert([
                        'user_id' => $userId,
                        'kyc_status' => 'pending',
                        'availability_status' => 'offline',
                        'rating' => 0.00,
                        'total_tasks_completed' => 0,
                        'total_earnings' => 0.00,
                    ]);
                    break;
            }
        }

        return $data;
    }

    /**
     * Find user by Firebase UID
     */
    public function findByFirebaseUID(string $firebaseUID)
    {
        return $this->where('firebase_uid', $firebaseUID)->first();
    }

    /**
     * Find user by email
     */
    public function findByEmail(string $email)
    {
        return $this->where('email', $email)->first();
    }

    /**
     * Get user with profile data
     */
    public function getUserWithProfile(int $userId)
    {
        $user = $this->find($userId);
        
        if (!$user) {
            return null;
        }

        // Load appropriate profile
        switch ($user['user_type']) {
            case 'customer':
                $customerProfileModel = new CustomerProfileModel();
                $user['profile'] = $customerProfileModel->where('user_id', $userId)->first();
                break;

            case 'workboy':
                $workboyProfileModel = new WorkboyProfileModel();
                $user['profile'] = $workboyProfileModel->where('user_id', $userId)->first();
                break;
        }

        return $user;
    }

    /**
     * Get users by type with pagination
     */
    public function getUsersByType(string $userType, int $limit = 20, int $offset = 0)
    {
        return $this->where('user_type', $userType)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Search users
     */
    public function searchUsers(string $query, string $userType = null, int $limit = 20, int $offset = 0)
    {
        $builder = $this->builder();
        
        $builder->groupStart()
                ->like('first_name', $query)
                ->orLike('last_name', $query)
                ->orLike('email', $query)
                ->groupEnd();

        if ($userType) {
            $builder->where('user_type', $userType);
        }

        return $builder->orderBy('created_at', 'DESC')
                      ->get($limit, $offset)
                      ->getResultArray();
    }

    /**
     * Update user status
     */
    public function updateUserStatus(int $userId, string $status)
    {
        return $this->update($userId, ['status' => $status]);
    }

    /**
     * Get active users count by type
     */
    public function getActiveUsersCount(string $userType = null)
    {
        $builder = $this->builder();
        $builder->where('status', 'active');
        
        if ($userType) {
            $builder->where('user_type', $userType);
        }
        
        return $builder->countAllResults();
    }
}
