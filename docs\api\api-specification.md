# Work-Boy Booking API Specification

## Base URL
```
Development: http://localhost:8080/api/v1
Production: https://api.workboybooking.com/v1
```

## Authentication
All API endpoints (except public ones) require Firebase JWT token in the Authorization header:
```
Authorization: Bearer <firebase_jwt_token>
```

## Response Format
All API responses follow this structure:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Response data
  "errors": [], // Validation errors (if any)
  "meta": {} // Pagination, etc.
}
```

## HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Endpoints

### Authentication

#### POST /auth/register
Register a new user
```json
{
  "firebase_uid": "string",
  "email": "string",
  "phone": "string",
  "first_name": "string",
  "last_name": "string",
  "user_type": "customer|workboy"
}
```

#### POST /auth/login
Login user (verify Firebase token)
```json
{
  "firebase_uid": "string"
}
```

#### GET /auth/profile
Get current user profile

#### PUT /auth/profile
Update user profile
```json
{
  "first_name": "string",
  "last_name": "string",
  "phone": "string",
  "profile_image": "string"
}
```

### Customer Endpoints

#### GET /customer/dashboard
Get customer dashboard data
- Recent tasks
- Quick stats
- Recommended Work-Boys

#### POST /customer/addresses
Add new address
```json
{
  "label": "string",
  "address_line_1": "string",
  "address_line_2": "string",
  "city": "string",
  "state": "string",
  "postal_code": "string",
  "latitude": "number",
  "longitude": "number",
  "is_default": "boolean"
}
```

#### GET /customer/addresses
Get user addresses

#### PUT /customer/addresses/{id}
Update address

#### DELETE /customer/addresses/{id}
Delete address

### Task Management

#### GET /tasks/categories
Get all task categories

#### POST /tasks
Create new task
```json
{
  "category_id": "number",
  "title": "string",
  "description": "string",
  "task_images": ["string"],
  "address_id": "number",
  "scheduled_date": "date",
  "scheduled_time": "time",
  "estimated_duration": "number"
}
```

#### GET /tasks
Get tasks (with filters)
Query parameters:
- `status`: pending|assigned|in_progress|completed|cancelled
- `customer_id`: number
- `workboy_id`: number
- `date_from`: date
- `date_to`: date
- `page`: number
- `limit`: number

#### GET /tasks/{id}
Get task details

#### PUT /tasks/{id}
Update task

#### DELETE /tasks/{id}
Cancel task

#### POST /tasks/{id}/assign
Assign task to Work-Boy
```json
{
  "workboy_id": "number"
}
```

#### POST /tasks/{id}/status
Update task status
```json
{
  "status": "assigned|in_progress|completed|cancelled",
  "notes": "string",
  "location_lat": "number",
  "location_lng": "number"
}
```

### Work-Boy Endpoints

#### GET /workboy/dashboard
Get Work-Boy dashboard
- Available tasks
- Earnings summary
- Performance stats

#### PUT /workboy/profile
Update Work-Boy profile
```json
{
  "skills": ["string"],
  "hourly_rate": "number",
  "availability_status": "available|busy|offline",
  "bank_details": {
    "account_number": "string",
    "ifsc_code": "string",
    "account_holder_name": "string"
  }
}
```

#### POST /workboy/kyc
Submit KYC documents
```json
{
  "documents": {
    "id_proof": "string",
    "address_proof": "string",
    "photo": "string"
  }
}
```

#### GET /workboy/available-tasks
Get available tasks for Work-Boy
Query parameters:
- `category_id`: number
- `location_lat`: number
- `location_lng`: number
- `radius`: number (in km)

#### POST /workboy/tasks/{id}/accept
Accept a task

#### POST /workboy/tasks/{id}/reject
Reject a task

#### GET /workboy/earnings
Get earnings summary
Query parameters:
- `period`: daily|weekly|monthly
- `date_from`: date
- `date_to`: date

### Payment Endpoints

#### POST /payments/create
Create payment intent
```json
{
  "task_id": "number",
  "amount": "number",
  "tip_amount": "number"
}
```

#### POST /payments/confirm
Confirm payment
```json
{
  "payment_id": "string",
  "payment_gateway_id": "string"
}
```

#### GET /payments/history
Get payment history

### Reviews and Ratings

#### POST /reviews
Submit review
```json
{
  "task_id": "number",
  "workboy_id": "number",
  "rating": "number",
  "review_text": "string"
}
```

#### GET /reviews/workboy/{id}
Get Work-Boy reviews

### Notifications

#### GET /notifications
Get user notifications
Query parameters:
- `is_read`: boolean
- `type`: task_update|payment|system|promotion
- `page`: number
- `limit`: number

#### PUT /notifications/{id}/read
Mark notification as read

#### PUT /notifications/read-all
Mark all notifications as read

### Real-time Endpoints (WebSocket)

#### /ws/task-tracking/{task_id}
Real-time task status updates

#### /ws/location-tracking/{task_id}
Real-time location updates

### Admin Endpoints

#### GET /admin/dashboard
Get admin dashboard stats

#### GET /admin/users
Get all users with filters
Query parameters:
- `user_type`: customer|workboy|admin
- `status`: active|inactive|suspended|pending_verification
- `search`: string
- `page`: number
- `limit`: number

#### PUT /admin/users/{id}/status
Update user status
```json
{
  "status": "active|inactive|suspended"
}
```

#### GET /admin/tasks
Get all tasks with advanced filters

#### GET /admin/payments
Get all payments

#### GET /admin/analytics
Get platform analytics
Query parameters:
- `period`: daily|weekly|monthly|yearly
- `metric`: users|tasks|revenue|ratings

#### POST /admin/workboy/{id}/approve-kyc
Approve Work-Boy KYC

#### POST /admin/workboy/{id}/reject-kyc
Reject Work-Boy KYC
```json
{
  "reason": "string"
}
```

## Error Handling

### Validation Errors (422)
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "field_name": ["Error message 1", "Error message 2"]
  }
}
```

### Authentication Errors (401)
```json
{
  "success": false,
  "message": "Unauthorized",
  "errors": ["Invalid or expired token"]
}
```

## Rate Limiting
- 100 requests per minute per user
- 1000 requests per minute per IP

## Pagination
```json
{
  "data": [],
  "meta": {
    "current_page": 1,
    "per_page": 20,
    "total": 100,
    "total_pages": 5,
    "has_next": true,
    "has_prev": false
  }
}
```
