import { useState, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import { Link } from 'react-router-dom'
import {
  UserGroupIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  TruckIcon,
  WrenchScrewdriverIcon,
  MapPinIcon,
  CalendarDaysIcon,
  PhoneIcon,
  EnvelopeIcon,
} from '@heroicons/react/24/outline'
import PremiumCard, { PremiumCardHeader, PremiumCardTitle, PremiumCardContent } from '../../components/ui/PremiumCard'
import PremiumButton from '../../components/ui/PremiumButton'
import PremiumInput from '../../components/ui/PremiumInput'

const WorkBoysPage = () => {
  const [workBoys, setWorkBoys] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterCategory, setFilterCategory] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const workBoysPerPage = 10

  // Mock data
  useEffect(() => {
    const mockWorkBoys = [
      {
        id: 1,
        name: 'Rajesh Kumar',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        location: 'Mumbai, Maharashtra',
        joinDate: '2024-01-15',
        status: 'active',
        category: 'Plumbing',
        totalJobs: 156,
        completedJobs: 148,
        rating: 4.8,
        earnings: 45000,
        availability: 'available',
        verified: true
      },
      {
        id: 2,
        name: 'Amit Sharma',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        location: 'Delhi, Delhi',
        joinDate: '2024-02-20',
        status: 'active',
        category: 'Electrical',
        totalJobs: 89,
        completedJobs: 85,
        rating: 4.6,
        earnings: 32000,
        availability: 'busy',
        verified: true
      },
      {
        id: 3,
        name: 'Suresh Patel',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        location: 'Bangalore, Karnataka',
        joinDate: '2024-01-10',
        status: 'inactive',
        category: 'Cleaning',
        totalJobs: 67,
        completedJobs: 62,
        rating: 4.4,
        earnings: 28000,
        availability: 'unavailable',
        verified: false
      },
      {
        id: 4,
        name: 'Vikram Singh',
        email: '<EMAIL>',
        phone: '+91 9876543213',
        location: 'Chennai, Tamil Nadu',
        joinDate: '2024-03-05',
        status: 'active',
        category: 'Carpentry',
        totalJobs: 43,
        completedJobs: 40,
        rating: 4.9,
        earnings: 38000,
        availability: 'available',
        verified: true
      },
      {
        id: 5,
        name: 'Manoj Gupta',
        email: '<EMAIL>',
        phone: '+91 9876543214',
        location: 'Pune, Maharashtra',
        joinDate: '2024-02-28',
        status: 'suspended',
        category: 'Painting',
        totalJobs: 25,
        completedJobs: 20,
        rating: 3.8,
        earnings: 15000,
        availability: 'unavailable',
        verified: false
      }
    ]

    setTimeout(() => {
      setWorkBoys(mockWorkBoys)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredWorkBoys = workBoys.filter(workBoy => {
    const matchesSearch = workBoy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         workBoy.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         workBoy.category.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || workBoy.status === filterStatus
    const matchesCategory = filterCategory === 'all' || workBoy.category === filterCategory
    return matchesSearch && matchesStatus && matchesCategory
  })

  const totalPages = Math.ceil(filteredWorkBoys.length / workBoysPerPage)
  const startIndex = (currentPage - 1) * workBoysPerPage
  const paginatedWorkBoys = filteredWorkBoys.slice(startIndex, startIndex + workBoysPerPage)

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { bg: 'bg-success-100', text: 'text-success-800', icon: CheckCircleIcon },
      inactive: { bg: 'bg-gray-100', text: 'text-gray-800', icon: XCircleIcon },
      suspended: { bg: 'bg-danger-100', text: 'text-danger-800', icon: XCircleIcon }
    }

    const config = statusConfig[status] || statusConfig.inactive
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-bold ${config.bg} ${config.text}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    )
  }

  const getAvailabilityBadge = (availability) => {
    const availabilityConfig = {
      available: { bg: 'bg-success-100', text: 'text-success-800' },
      busy: { bg: 'bg-warning-100', text: 'text-warning-800' },
      unavailable: { bg: 'bg-gray-100', text: 'text-gray-800' }
    }

    const config = availabilityConfig[availability] || availabilityConfig.unavailable

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-bold ${config.bg} ${config.text}`}>
        {availability.charAt(0).toUpperCase() + availability.slice(1)}
      </span>
    )
  }

  const getCategoryIcon = (category) => {
    const categoryIcons = {
      'Plumbing': WrenchScrewdriverIcon,
      'Electrical': WrenchScrewdriverIcon,
      'Cleaning': WrenchScrewdriverIcon,
      'Carpentry': WrenchScrewdriverIcon,
      'Painting': WrenchScrewdriverIcon
    }

    return categoryIcons[category] || WrenchScrewdriverIcon
  }

  const getRatingStars = (rating) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(<span key={i} className="text-warning-400">★</span>)
    }

    if (hasHalfStar) {
      stars.push(<span key="half" className="text-warning-400">☆</span>)
    }

    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<span key={`empty-${i}`} className="text-gray-300">☆</span>)
    }

    return <div className="flex items-center">{stars}</div>
  }

  if (loading) {
    return (
      <div className="space-y-6 animate-fade-in-up">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 animate-fade-in-up">
      <Helmet>
        <title>Work-Boys Management - WorkBoy Admin</title>
      </Helmet>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-black bg-gradient-to-r from-gray-900 via-primary-800 to-brand-700 bg-clip-text text-transparent">
            Work-Boys Management
          </h1>
          <p className="text-gray-600 mt-2 font-medium">Manage service providers and work-boy profiles</p>
        </div>
        <div className="flex space-x-3">
          <PremiumButton variant="outline" icon={ArrowDownTrayIcon}>
            Export
          </PremiumButton>
          <PremiumButton variant="primary" icon={PlusIcon}>
            Add Work-Boy
          </PremiumButton>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 shadow-lg shadow-primary-500/25">
              <UserGroupIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Total Work-Boys</p>
              <p className="text-2xl font-black text-gray-900">{workBoys.length}</p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-success-500 to-success-600 shadow-lg shadow-success-500/25">
              <CheckCircleIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Active</p>
              <p className="text-2xl font-black text-gray-900">
                {workBoys.filter(w => w.status === 'active').length}
              </p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-warning-500 to-warning-600 shadow-lg shadow-warning-500/25">
              <ClockIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Available</p>
              <p className="text-2xl font-black text-gray-900">
                {workBoys.filter(w => w.availability === 'available').length}
              </p>
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-gradient-to-br from-accent-500 to-accent-600 shadow-lg shadow-accent-500/25">
              <StarIcon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Avg Rating</p>
              <p className="text-2xl font-black text-gray-900">
                {(workBoys.reduce((sum, w) => sum + w.rating, 0) / workBoys.length).toFixed(1)}
              </p>
            </div>
          </div>
        </PremiumCard>
      </div>

      {/* Filters and Search */}
      <PremiumCard gradient>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1 max-w-md">
            <PremiumInput
              placeholder="Search work-boys by name, email, or category..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={MagnifyingGlassIcon}
              variant="premium"
            />
          </div>

          <div className="flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-xl text-sm font-medium bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-300"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>

            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-xl text-sm font-medium bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-300"
            >
              <option value="all">All Categories</option>
              <option value="Plumbing">Plumbing</option>
              <option value="Electrical">Electrical</option>
              <option value="Cleaning">Cleaning</option>
              <option value="Carpentry">Carpentry</option>
              <option value="Painting">Painting</option>
            </select>

            <PremiumButton variant="outline" icon={FunnelIcon} size="sm">
              More Filters
            </PremiumButton>
          </div>
        </div>
      </PremiumCard>

      {/* Work-Boys Table */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={UserGroupIcon}>
            Work-Boys List ({filteredWorkBoys.length} work-boys)
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Work-Boy
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Jobs
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Rating
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Earnings
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedWorkBoys.map((workBoy) => {
                  const CategoryIcon = getCategoryIcon(workBoy.category)
                  return (
                    <tr key={workBoy.id} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 flex items-center justify-center shadow-lg shadow-primary-500/25">
                            <span className="text-white font-bold text-sm">
                              {workBoy.name.charAt(0)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="flex items-center space-x-2">
                              <div className="text-sm font-bold text-gray-900">{workBoy.name}</div>
                              {workBoy.verified && (
                                <CheckCircleIcon className="h-4 w-4 text-success-500" title="Verified" />
                              )}
                            </div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <CalendarDaysIcon className="h-3 w-3 mr-1" />
                              Joined {new Date(workBoy.joinDate).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <div className="text-sm text-gray-900 flex items-center">
                            <EnvelopeIcon className="h-3 w-3 mr-2 text-gray-400" />
                            {workBoy.email}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <PhoneIcon className="h-3 w-3 mr-2 text-gray-400" />
                            {workBoy.phone}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <MapPinIcon className="h-3 w-3 mr-2 text-gray-400" />
                            {workBoy.location}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="p-2 rounded-lg bg-gray-100 mr-3">
                            <CategoryIcon className="h-4 w-4 text-gray-600" />
                          </div>
                          <div>
                            <div className="text-sm font-bold text-gray-900">{workBoy.category}</div>
                            <div className="text-xs text-gray-500">
                              {getAvailabilityBadge(workBoy.availability)}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          <div className="font-bold">{workBoy.completedJobs}/{workBoy.totalJobs}</div>
                          <div className="text-xs text-gray-500">
                            {Math.round((workBoy.completedJobs / workBoy.totalJobs) * 100)}% completion
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          {getRatingStars(workBoy.rating)}
                          <span className="text-sm font-bold text-gray-900">{workBoy.rating}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-bold text-gray-900">
                          ₹{workBoy.earnings.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-500">This month</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(workBoy.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <Link
                            to={`/workboys/${workBoy.id}`}
                            className="text-primary-600 hover:text-primary-900 p-1 rounded-lg hover:bg-primary-50 transition-colors duration-200"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </Link>
                          <button className="text-warning-600 hover:text-warning-900 p-1 rounded-lg hover:bg-warning-50 transition-colors duration-200">
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button className="text-danger-600 hover:text-danger-900 p-1 rounded-lg hover:bg-danger-50 transition-colors duration-200">
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </PremiumCardContent>
      </PremiumCard>
    </div>
  )
}

export default WorkBoysPage
