<?php

namespace App\Models;

use CodeIgniter\Model;

class AddressModel extends Model
{
    protected $table            = 'addresses';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'user_id',
        'label',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'latitude',
        'longitude',
        'is_default'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id'        => 'required|integer',
        'label'          => 'permit_empty|max_length[50]',
        'address_line_1' => 'required|max_length[255]',
        'address_line_2' => 'permit_empty|max_length[255]',
        'city'           => 'required|max_length[100]',
        'state'          => 'required|max_length[100]',
        'postal_code'    => 'required|max_length[20]',
        'country'        => 'permit_empty|max_length[100]',
        'latitude'       => 'permit_empty|decimal',
        'longitude'      => 'permit_empty|decimal',
        'is_default'     => 'permit_empty|in_list[0,1]',
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
        ],
        'address_line_1' => [
            'required' => 'Address line 1 is required',
        ],
        'city' => [
            'required' => 'City is required',
        ],
        'state' => [
            'required' => 'State is required',
        ],
        'postal_code' => [
            'required' => 'Postal code is required',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['handleDefaultAddress'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['handleDefaultAddress'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Handle default address logic
     */
    protected function handleDefaultAddress(array $data)
    {
        if (isset($data['data']['is_default']) && $data['data']['is_default'] == 1) {
            // Remove default flag from other addresses of the same user
            $userId = $data['data']['user_id'];
            $this->where('user_id', $userId)
                 ->where('is_default', 1)
                 ->set('is_default', 0)
                 ->update();
        }

        return $data;
    }

    /**
     * Get addresses by user ID
     */
    public function getByUserId(int $userId)
    {
        return $this->where('user_id', $userId)
                   ->orderBy('is_default', 'DESC')
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get default address for user
     */
    public function getDefaultAddress(int $userId)
    {
        return $this->where('user_id', $userId)
                   ->where('is_default', 1)
                   ->first();
    }

    /**
     * Set address as default
     */
    public function setAsDefault(int $addressId, int $userId)
    {
        // First, remove default flag from all user addresses
        $this->where('user_id', $userId)
             ->set('is_default', 0)
             ->update();

        // Then set the specified address as default
        return $this->update($addressId, ['is_default' => 1]);
    }

    /**
     * Get user addresses with usage count
     */
    public function getAddressesWithUsage(int $userId)
    {
        $builder = $this->db->table('addresses a');
        
        return $builder->select('a.*, COUNT(t.id) as usage_count')
                      ->join('tasks t', 't.address_id = a.id', 'left')
                      ->where('a.user_id', $userId)
                      ->groupBy('a.id')
                      ->orderBy('a.is_default', 'DESC')
                      ->orderBy('usage_count', 'DESC')
                      ->orderBy('a.created_at', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Search addresses by location
     */
    public function searchByLocation(float $latitude, float $longitude, float $radius = 10, int $limit = 20)
    {
        // Calculate distance using Haversine formula
        $sql = "SELECT *, 
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * 
                cos(radians(longitude) - radians(?)) + sin(radians(?)) * 
                sin(radians(latitude)))) AS distance 
                FROM addresses 
                WHERE latitude IS NOT NULL AND longitude IS NOT NULL
                HAVING distance < ? 
                ORDER BY distance 
                LIMIT ?";

        return $this->db->query($sql, [$latitude, $longitude, $latitude, $radius, $limit])
                       ->getResultArray();
    }

    /**
     * Get full address string
     */
    public function getFullAddress(array $address): string
    {
        $parts = [];
        
        if (!empty($address['address_line_1'])) {
            $parts[] = $address['address_line_1'];
        }
        
        if (!empty($address['address_line_2'])) {
            $parts[] = $address['address_line_2'];
        }
        
        if (!empty($address['city'])) {
            $parts[] = $address['city'];
        }
        
        if (!empty($address['state'])) {
            $parts[] = $address['state'];
        }
        
        if (!empty($address['postal_code'])) {
            $parts[] = $address['postal_code'];
        }
        
        if (!empty($address['country']) && $address['country'] !== 'India') {
            $parts[] = $address['country'];
        }
        
        return implode(', ', $parts);
    }

    /**
     * Validate postal code format (Indian postal codes)
     */
    public function validatePostalCode(string $postalCode): bool
    {
        // Indian postal code format: 6 digits
        return preg_match('/^[1-9][0-9]{5}$/', $postalCode);
    }

    /**
     * Get addresses by city
     */
    public function getByCity(string $city, int $limit = 20, int $offset = 0)
    {
        return $this->like('city', $city)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get addresses by state
     */
    public function getByState(string $state, int $limit = 20, int $offset = 0)
    {
        return $this->like('state', $state)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get popular cities
     */
    public function getPopularCities(int $limit = 10)
    {
        $builder = $this->db->table('addresses');
        
        return $builder->select('city, COUNT(*) as address_count')
                      ->groupBy('city')
                      ->orderBy('address_count', 'DESC')
                      ->limit($limit)
                      ->get()
                      ->getResultArray();
    }

    /**
     * Update coordinates
     */
    public function updateCoordinates(int $addressId, float $latitude, float $longitude)
    {
        return $this->update($addressId, [
            'latitude' => $latitude,
            'longitude' => $longitude
        ]);
    }

    /**
     * Delete address if not used in any tasks
     */
    public function safeDelete(int $addressId, int $userId)
    {
        // Check if address is used in any tasks
        $taskModel = new TaskModel();
        $tasksCount = $taskModel->where('address_id', $addressId)->countAllResults();
        
        if ($tasksCount > 0) {
            return [
                'success' => false,
                'message' => 'Cannot delete address as it is used in existing tasks'
            ];
        }

        // Verify ownership
        $address = $this->where('id', $addressId)
                       ->where('user_id', $userId)
                       ->first();
        
        if (!$address) {
            return [
                'success' => false,
                'message' => 'Address not found or access denied'
            ];
        }

        $deleted = $this->delete($addressId);
        
        return [
            'success' => $deleted,
            'message' => $deleted ? 'Address deleted successfully' : 'Failed to delete address'
        ];
    }
}
