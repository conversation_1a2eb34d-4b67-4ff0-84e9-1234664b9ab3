import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Card, Button, Chip, Divider } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '@constants/theme';

const TaskDetailScreen = ({ route, navigation }) => {
  const { taskId } = route.params || {};

  // Mock task data - in real app, this would be fetched from API
  const task = {
    id: taskId || '1',
    title: 'House Cleaning Service',
    description: 'Deep cleaning of 3-bedroom apartment including kitchen, bathrooms, and living areas.',
    category: 'Cleaning',
    status: 'in_progress',
    price: 150,
    workboy: {
      name: '<PERSON>',
      rating: 4.8,
      phone: '+1234567890',
    },
    scheduledDate: '2024-01-20',
    scheduledTime: '10:00 AM',
    address: '123 Main St, City, State 12345',
    estimatedDuration: '3 hours',
    createdAt: '2024-01-15',
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return colors.warning[500];
      case 'in_progress':
        return colors.primary[500];
      case 'completed':
        return colors.success[500];
      case 'cancelled':
        return colors.error[500];
      default:
        return colors.gray[500];
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Task Header */}
        <Card style={styles.headerCard}>
          <Card.Content>
            <View style={styles.headerRow}>
              <Text style={styles.taskTitle}>{task.title}</Text>
              <Chip
                style={[styles.statusChip, { backgroundColor: getStatusColor(task.status) }]}
                textStyle={styles.statusText}
              >
                {getStatusText(task.status)}
              </Chip>
            </View>
            <Text style={styles.taskCategory}>{task.category}</Text>
            <Text style={styles.taskPrice}>${task.price}</Text>
          </Card.Content>
        </Card>

        {/* Task Description */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{task.description}</Text>
          </Card.Content>
        </Card>

        {/* Schedule Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Schedule</Text>
            <View style={styles.infoRow}>
              <Ionicons name="calendar-outline" size={20} color={colors.gray[600]} />
              <Text style={styles.infoText}>{task.scheduledDate}</Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="time-outline" size={20} color={colors.gray[600]} />
              <Text style={styles.infoText}>{task.scheduledTime}</Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="hourglass-outline" size={20} color={colors.gray[600]} />
              <Text style={styles.infoText}>{task.estimatedDuration}</Text>
            </View>
          </Card.Content>
        </Card>

        {/* Location */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Location</Text>
            <View style={styles.infoRow}>
              <Ionicons name="location-outline" size={20} color={colors.gray[600]} />
              <Text style={styles.infoText}>{task.address}</Text>
            </View>
          </Card.Content>
        </Card>

        {/* WorkBoy Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>WorkBoy</Text>
            <View style={styles.workboyInfo}>
              <View style={styles.workboyDetails}>
                <Text style={styles.workboyName}>{task.workboy.name}</Text>
                <View style={styles.ratingRow}>
                  <Ionicons name="star" size={16} color={colors.warning[500]} />
                  <Text style={styles.rating}>{task.workboy.rating}</Text>
                </View>
              </View>
              <Button
                mode="outlined"
                onPress={() => {
                  // Handle call workboy
                }}
                icon={({ size, color }) => (
                  <Ionicons name="call-outline" size={size} color={color} />
                )}
                compact
              >
                Call
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {task.status === 'pending' && (
            <>
              <Button
                mode="contained"
                onPress={() => {
                  // Handle cancel task
                }}
                style={[styles.actionButton, { backgroundColor: colors.error[500] }]}
              >
                Cancel Task
              </Button>
              <Button
                mode="outlined"
                onPress={() => {
                  // Handle edit task
                }}
                style={styles.actionButton}
              >
                Edit Task
              </Button>
            </>
          )}
          
          {task.status === 'completed' && (
            <Button
              mode="contained"
              onPress={() => {
                // Handle rate workboy
              }}
              style={styles.actionButton}
            >
              Rate WorkBoy
            </Button>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  headerCard: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  taskTitle: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    flex: 1,
    marginRight: spacing.md,
  },
  statusChip: {
    height: 28,
  },
  statusText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
  },
  taskCategory: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    marginBottom: spacing.sm,
  },
  taskPrice: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.primary[600],
  },
  card: {
    marginBottom: spacing.md,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.md,
  },
  description: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    lineHeight: 24,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  infoText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    marginLeft: spacing.sm,
    flex: 1,
  },
  workboyInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  workboyDetails: {
    flex: 1,
  },
  workboyName: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    marginLeft: spacing.xs,
  },
  actionButtons: {
    marginTop: spacing.md,
    gap: spacing.md,
  },
  actionButton: {
    marginBottom: spacing.sm,
  },
});

export default TaskDetailScreen;
