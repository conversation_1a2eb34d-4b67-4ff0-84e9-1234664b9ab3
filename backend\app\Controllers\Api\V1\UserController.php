<?php

namespace App\Controllers\Api\V1;

use App\Controllers\Api\BaseApiController;
use App\Models\UserModel;
use App\Models\CustomerProfileModel;
use App\Models\WorkboyProfileModel;

/**
 * User Controller for Work-Boy Booking API
 * 
 * Handles user-related operations and profile management
 */
class UserController extends BaseApiController
{
    protected $userModel;
    protected $customerProfileModel;
    protected $workboyProfileModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new UserModel();
        $this->customerProfileModel = new CustomerProfileModel();
        $this->workboyProfileModel = new WorkboyProfileModel();
    }

    /**
     * Get user by ID
     * GET /api/v1/users/{id}
     */
    public function show($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            // Users can only view their own profile unless they're admin
            if ($currentUser['user_type'] !== 'admin' && $currentUser['id'] != $id) {
                return $this->forbiddenResponse('Access denied');
            }

            $user = $this->userModel->getUserWithProfile($id);

            if (!$user) {
                return $this->notFoundResponse('User not found');
            }

            // Remove sensitive data
            unset($user['firebase_uid']);

            return $this->apiResponse($user, 'User retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get user error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve user');
        }
    }

    /**
     * Update user
     * PUT /api/v1/users/{id}
     */
    public function update($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser) {
                return $this->unauthorizedResponse('User not authenticated');
            }

            // Users can only update their own profile unless they're admin
            if ($currentUser['user_type'] !== 'admin' && $currentUser['id'] != $id) {
                return $this->forbiddenResponse('Access denied');
            }

            $user = $this->userModel->find($id);

            if (!$user) {
                return $this->notFoundResponse('User not found');
            }

            $rules = [
                'first_name'    => 'permit_empty|max_length[100]',
                'last_name'     => 'permit_empty|max_length[100]',
                'phone'         => 'permit_empty|max_length[20]',
                'profile_image' => 'permit_empty|max_length[500]',
            ];

            // Admin can update additional fields
            if ($currentUser['user_type'] === 'admin') {
                $rules['status'] = 'permit_empty|in_list[active,inactive,suspended,pending_verification]';
            }

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            // Update user data
            $updateData = [];
            $allowedFields = ['first_name', 'last_name', 'phone', 'profile_image'];

            if ($currentUser['user_type'] === 'admin') {
                $allowedFields[] = 'status';
            }

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return $this->errorResponse('No valid fields to update', 400);
            }

            $updated = $this->userModel->update($id, $updateData);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to update user');
            }

            // Get updated user with profile
            $updatedUser = $this->userModel->getUserWithProfile($id);
            unset($updatedUser['firebase_uid']);

            $this->logActivity('user_updated', [
                'target_user_id' => $id,
                'updated_fields' => array_keys($updateData)
            ]);

            return $this->apiResponse($updatedUser, 'User updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Update user error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to update user');
        }
    }

    /**
     * Get users list (Admin only)
     * GET /api/v1/users
     */
    public function index()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $pagination = $this->getPaginationParams();
            $userType = $this->request->getGet('user_type');
            $status = $this->request->getGet('status');
            $search = $this->request->getGet('search');

            $builder = $this->userModel->builder();

            // Apply filters
            if ($userType) {
                $builder->where('user_type', $userType);
            }

            if ($status) {
                $builder->where('status', $status);
            }

            if ($search) {
                $builder->groupStart()
                        ->like('first_name', $search)
                        ->orLike('last_name', $search)
                        ->orLike('email', $search)
                        ->groupEnd();
            }

            // Get total count for pagination
            $total = $builder->countAllResults(false);

            // Get users
            $users = $builder->orderBy('created_at', 'DESC')
                           ->limit($pagination['limit'], $pagination['offset'])
                           ->get()
                           ->getResultArray();

            // Remove sensitive data
            foreach ($users as &$user) {
                unset($user['firebase_uid']);
            }

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($users, 'Users retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Get users error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve users');
        }
    }

    /**
     * Delete user (Admin only)
     * DELETE /api/v1/users/{id}
     */
    public function delete($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $user = $this->userModel->find($id);

            if (!$user) {
                return $this->notFoundResponse('User not found');
            }

            // Prevent admin from deleting themselves
            if ($currentUser['id'] == $id) {
                return $this->errorResponse('Cannot delete your own account', 400);
            }

            // Check if user has active tasks
            $taskModel = new \App\Models\TaskModel();
            $activeTasks = $taskModel->where('customer_id', $id)
                                   ->orWhere('workboy_id', $id)
                                   ->whereIn('status', ['pending', 'assigned', 'in_progress'])
                                   ->countAllResults();

            if ($activeTasks > 0) {
                return $this->errorResponse('Cannot delete user with active tasks', 400);
            }

            $deleted = $this->userModel->delete($id);

            if (!$deleted) {
                return $this->serverErrorResponse('Failed to delete user');
            }

            $this->logActivity('user_deleted', ['deleted_user_id' => $id]);

            return $this->apiResponse(null, 'User deleted successfully');

        } catch (\Exception $e) {
            log_message('error', 'Delete user error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to delete user');
        }
    }

    /**
     * Get user statistics (Admin only)
     * GET /api/v1/users/stats
     */
    public function getStats()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $stats = [
                'total_users' => $this->userModel->countAllResults(),
                'active_users' => $this->userModel->where('status', 'active')->countAllResults(),
                'customers' => $this->userModel->where('user_type', 'customer')->countAllResults(),
                'workboys' => $this->userModel->where('user_type', 'workboy')->countAllResults(),
                'pending_verification' => $this->userModel->where('status', 'pending_verification')->countAllResults(),
                'suspended_users' => $this->userModel->where('status', 'suspended')->countAllResults(),
            ];

            // Get registration trends (last 30 days)
            $registrationTrends = $this->userModel->select('DATE(created_at) as date, COUNT(*) as count')
                                                 ->where('created_at >=', date('Y-m-d', strtotime('-30 days')))
                                                 ->groupBy('DATE(created_at)')
                                                 ->orderBy('date', 'ASC')
                                                 ->findAll();

            $stats['registration_trends'] = $registrationTrends;

            return $this->apiResponse($stats, 'User statistics retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Get user stats error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve user statistics');
        }
    }
}
