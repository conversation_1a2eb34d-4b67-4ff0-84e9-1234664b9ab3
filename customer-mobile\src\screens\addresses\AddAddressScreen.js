import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { TextInput, Button, HelperText, SegmentedButtons } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors, spacing, typography } from '@constants/theme';

const AddAddressScreen = ({ navigation, route }) => {
  const { address: editAddress } = route.params || {};
  const isEditing = !!editAddress;

  const [formData, setFormData] = useState({
    type: editAddress?.type || 'home',
    street: editAddress?.street || '',
    city: editAddress?.city || '',
    state: editAddress?.state || '',
    zipCode: editAddress?.zipCode || '',
    country: editAddress?.country || 'United States',
    isDefault: editAddress?.isDefault || false,
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const addressTypes = [
    { value: 'home', label: 'Home' },
    { value: 'work', label: 'Work' },
    { value: 'other', label: 'Other' },
  ];

  const validateForm = () => {
    const newErrors = {};

    if (!formData.street.trim()) {
      newErrors.street = 'Street address is required';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }

    if (!formData.state.trim()) {
      newErrors.state = 'State is required';
    }

    if (!formData.zipCode.trim()) {
      newErrors.zipCode = 'ZIP code is required';
    } else if (!/^\d{5}(-\d{4})?$/.test(formData.zipCode)) {
      newErrors.zipCode = 'Please enter a valid ZIP code';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      Alert.alert(
        'Success',
        `Address ${isEditing ? 'updated' : 'added'} successfully!`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert(
        'Error',
        `Failed to ${isEditing ? 'update' : 'add'} address. Please try again.`,
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <Text style={styles.title}>
            {isEditing ? 'Edit Address' : 'Add New Address'}
          </Text>

          {/* Address Type */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Address Type</Text>
            <SegmentedButtons
              value={formData.type}
              onValueChange={(value) => handleInputChange('type', value)}
              buttons={addressTypes}
              style={styles.segmentedButtons}
            />
          </View>

          {/* Address Form */}
          <View style={styles.form}>
            {/* Street Address */}
            <TextInput
              label="Street Address"
              value={formData.street}
              onChangeText={(value) => handleInputChange('street', value)}
              mode="outlined"
              style={styles.input}
              error={!!errors.street}
              autoCapitalize="words"
              placeholder="123 Main Street"
            />
            <HelperText type="error" visible={!!errors.street}>
              {errors.street}
            </HelperText>

            {/* City */}
            <TextInput
              label="City"
              value={formData.city}
              onChangeText={(value) => handleInputChange('city', value)}
              mode="outlined"
              style={styles.input}
              error={!!errors.city}
              autoCapitalize="words"
              placeholder="New York"
            />
            <HelperText type="error" visible={!!errors.city}>
              {errors.city}
            </HelperText>

            {/* State and ZIP Code Row */}
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <TextInput
                  label="State"
                  value={formData.state}
                  onChangeText={(value) => handleInputChange('state', value)}
                  mode="outlined"
                  style={styles.input}
                  error={!!errors.state}
                  autoCapitalize="characters"
                  placeholder="NY"
                  maxLength={2}
                />
                <HelperText type="error" visible={!!errors.state}>
                  {errors.state}
                </HelperText>
              </View>

              <View style={styles.halfWidth}>
                <TextInput
                  label="ZIP Code"
                  value={formData.zipCode}
                  onChangeText={(value) => handleInputChange('zipCode', value)}
                  mode="outlined"
                  style={styles.input}
                  error={!!errors.zipCode}
                  keyboardType="numeric"
                  placeholder="10001"
                  maxLength={10}
                />
                <HelperText type="error" visible={!!errors.zipCode}>
                  {errors.zipCode}
                </HelperText>
              </View>
            </View>

            {/* Country */}
            <TextInput
              label="Country"
              value={formData.country}
              onChangeText={(value) => handleInputChange('country', value)}
              mode="outlined"
              style={styles.input}
              autoCapitalize="words"
              editable={false}
            />

            {/* Save Button */}
            <Button
              mode="contained"
              onPress={handleSave}
              loading={loading}
              disabled={loading}
              style={styles.saveButton}
              contentStyle={styles.buttonContent}
            >
              {isEditing ? 'Update Address' : 'Save Address'}
            </Button>

            {/* Cancel Button */}
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              disabled={loading}
              style={styles.cancelButton}
              contentStyle={styles.buttonContent}
            >
              Cancel
            </Button>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: spacing.md,
  },
  title: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
    marginBottom: spacing.lg,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.medium,
    color: colors.gray[700],
    marginBottom: spacing.sm,
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },
  form: {
    flex: 1,
  },
  input: {
    marginBottom: spacing.xs,
  },
  row: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  halfWidth: {
    flex: 1,
  },
  saveButton: {
    marginTop: spacing.lg,
    marginBottom: spacing.md,
  },
  cancelButton: {
    marginBottom: spacing.lg,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
});

export default AddAddressScreen;
