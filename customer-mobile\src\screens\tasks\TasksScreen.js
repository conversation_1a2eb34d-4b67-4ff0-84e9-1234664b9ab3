import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  StatusBar,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  IconButton,
  Searchbar,
  FAB,
  Menu,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { apiService, endpoints } from '@services/apiService';
import { colors, spacing, typography, borderRadius } from '@constants/theme';
import { showToast } from '@utils/toast';
import { format } from 'date-fns';

const TasksScreen = ({ navigation }) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [menuVisible, setMenuVisible] = useState(false);

  const filterOptions = [
    { value: 'all', label: 'All Tasks' },
    { value: 'pending', label: 'Pending' },
    { value: 'assigned', label: 'Assigned' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  useEffect(() => {
    fetchTasks();
  }, [selectedFilter]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (selectedFilter !== 'all') {
        params.append('status', selectedFilter);
      }
      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim());
      }

      const response = await apiService.get(`${endpoints.tasks.list}?${params.toString()}`);
      
      if (response.success) {
        setTasks(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error);
      showToast('Failed to load tasks', 'error');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchTasks();
    setRefreshing(false);
  }, [selectedFilter, searchQuery]);

  const handleSearch = (query) => {
    setSearchQuery(query);
    // Debounce search
    const timeoutId = setTimeout(() => {
      fetchTasks();
    }, 500);
    
    return () => clearTimeout(timeoutId);
  };

  const handleFilterSelect = (filter) => {
    setSelectedFilter(filter);
    setMenuVisible(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return colors.warning[500];
      case 'assigned':
        return colors.primary[500];
      case 'in_progress':
        return colors.blue[500];
      case 'completed':
        return colors.success[500];
      case 'cancelled':
        return colors.error[500];
      default:
        return colors.gray[500];
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return 'time-outline';
      case 'assigned':
        return 'person-outline';
      case 'in_progress':
        return 'play-circle-outline';
      case 'completed':
        return 'checkmark-circle-outline';
      case 'cancelled':
        return 'close-circle-outline';
      default:
        return 'time-outline';
    }
  };

  const renderTaskItem = ({ item }) => (
    <Card 
      style={styles.taskCard}
      onPress={() => navigation.navigate('TaskDetail', { taskId: item.id })}
    >
      <Card.Content style={styles.taskCardContent}>
        <View style={styles.taskHeader}>
          <View style={styles.taskInfo}>
            <Text style={styles.taskTitle} numberOfLines={2}>
              {item.title}
            </Text>
            <Text style={styles.taskDescription} numberOfLines={2}>
              {item.description}
            </Text>
          </View>
          
          <Chip
            mode="outlined"
            style={[
              styles.statusChip,
              { borderColor: getStatusColor(item.status) }
            ]}
            textStyle={[
              styles.statusChipText,
              { color: getStatusColor(item.status) }
            ]}
            icon={() => (
              <Ionicons
                name={getStatusIcon(item.status)}
                size={14}
                color={getStatusColor(item.status)}
              />
            )}
          >
            {item.status.replace('_', ' ')}
          </Chip>
        </View>

        <View style={styles.taskMeta}>
          <View style={styles.taskMetaItem}>
            <Ionicons name="pricetag-outline" size={16} color={colors.gray[500]} />
            <Text style={styles.taskMetaText}>{item.category_name}</Text>
          </View>
          
          <View style={styles.taskMetaItem}>
            <Ionicons name="calendar-outline" size={16} color={colors.gray[500]} />
            <Text style={styles.taskMetaText}>
              {format(new Date(item.scheduled_date), 'MMM dd, yyyy')}
            </Text>
          </View>
          
          <View style={styles.taskMetaItem}>
            <Ionicons name="time-outline" size={16} color={colors.gray[500]} />
            <Text style={styles.taskMetaText}>{item.scheduled_time}</Text>
          </View>
        </View>

        {item.city && (
          <View style={styles.taskLocation}>
            <Ionicons name="location-outline" size={16} color={colors.gray[500]} />
            <Text style={styles.taskLocationText}>{item.city}</Text>
          </View>
        )}

        {item.amount && (
          <View style={styles.taskAmount}>
            <Text style={styles.taskAmountText}>₹{item.amount}</Text>
          </View>
        )}
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="clipboard-outline" size={64} color={colors.gray[400]} />
      <Text style={styles.emptyTitle}>No tasks found</Text>
      <Text style={styles.emptySubtitle}>
        {searchQuery || selectedFilter !== 'all'
          ? 'Try adjusting your search or filters'
          : 'Create your first task to get started'}
      </Text>
    </View>
  );

  const currentFilterLabel = filterOptions.find(f => f.value === selectedFilter)?.label || 'All Tasks';

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Tasks</Text>
        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <IconButton
              icon="filter-outline"
              size={24}
              iconColor={colors.gray[600]}
              onPress={() => setMenuVisible(true)}
            />
          }
        >
          {filterOptions.map((option) => (
            <Menu.Item
              key={option.value}
              onPress={() => handleFilterSelect(option.value)}
              title={option.label}
              leadingIcon={selectedFilter === option.value ? 'check' : undefined}
            />
          ))}
        </Menu>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search tasks..."
          onChangeText={handleSearch}
          value={searchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={colors.gray[500]}
        />
      </View>

      {/* Filter Indicator */}
      {selectedFilter !== 'all' && (
        <View style={styles.filterIndicator}>
          <Chip
            mode="outlined"
            onClose={() => setSelectedFilter('all')}
            style={styles.filterChip}
            textStyle={styles.filterChipText}
          >
            {currentFilterLabel}
          </Chip>
        </View>
      )}

      {/* Tasks List */}
      <FlatList
        data={tasks}
        renderItem={renderTaskItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
      />

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('CreateTask')}
        label="New Task"
        extended
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.fontSize['2xl'],
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
  },
  searchBar: {
    backgroundColor: colors.gray[100],
    elevation: 0,
  },
  searchInput: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
  },
  filterIndicator: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
    backgroundColor: colors.white,
  },
  filterChip: {
    alignSelf: 'flex-start',
    backgroundColor: colors.primary[50],
    borderColor: colors.primary[200],
  },
  filterChipText: {
    color: colors.primary[700],
    fontSize: typography.fontSize.sm,
  },
  listContainer: {
    padding: spacing.lg,
    paddingBottom: 100, // Space for FAB
  },
  taskCard: {
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
    elevation: 2,
  },
  taskCardContent: {
    paddingVertical: spacing.md,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  taskInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  taskTitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  taskDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    lineHeight: typography.lineHeight.sm,
  },
  statusChip: {
    backgroundColor: colors.transparent,
    height: 32,
  },
  statusChipText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.medium,
    textTransform: 'capitalize',
  },
  taskMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.sm,
  },
  taskMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.lg,
    marginBottom: spacing.xs,
  },
  taskMetaText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    marginLeft: spacing.xs,
  },
  taskLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  taskLocationText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    marginLeft: spacing.xs,
  },
  taskAmount: {
    alignSelf: 'flex-end',
  },
  taskAmountText: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.primary[600],
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing['4xl'],
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
    paddingHorizontal: spacing.xl,
  },
  fab: {
    position: 'absolute',
    margin: spacing.lg,
    right: 0,
    bottom: 0,
    backgroundColor: colors.primary[600],
  },
});

export default TasksScreen;
