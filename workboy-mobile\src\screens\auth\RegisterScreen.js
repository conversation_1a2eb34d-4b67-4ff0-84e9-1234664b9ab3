import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, TextInput, Card, Title, Paragraph, Checkbox } from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import { useAuth } from '@context/AuthContext';
import Toast from 'react-native-toast-message';

const RegisterScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const { register } = useAuth();

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    },
  });

  const password = watch('password');

  const onSubmit = async (data) => {
    if (!acceptTerms) {
      Toast.show({
        type: 'error',
        text1: 'Terms Required',
        text2: 'Please accept the terms and conditions to continue.',
      });
      return;
    }

    try {
      setLoading(true);
      await register({
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        password: data.password,
      });
      Toast.show({
        type: 'success',
        text1: 'Registration Successful!',
        text2: 'Please complete your profile to start working.',
      });
    } catch (error) {
      console.error('Registration error:', error);
      Toast.show({
        type: 'error',
        text1: 'Registration Failed',
        text2: error.message || 'Please try again later.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSignIn = () => {
    navigation.navigate('Login');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Title style={styles.title}>Join WorkBoy</Title>
            <Paragraph style={styles.subtitle}>
              Create your partner account and start earning
            </Paragraph>
          </View>

          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.form}>
                <View style={styles.nameRow}>
                  <Controller
                    control={control}
                    rules={{ required: 'First name is required' }}
                    render={({ field: { onChange, onBlur, value } }) => (
                      <TextInput
                        label="First Name"
                        mode="outlined"
                        value={value}
                        onBlur={onBlur}
                        onChangeText={onChange}
                        error={!!errors.firstName}
                        style={[styles.input, styles.nameInput]}
                        left={<TextInput.Icon icon="account" />}
                      />
                    )}
                    name="firstName"
                  />

                  <Controller
                    control={control}
                    rules={{ required: 'Last name is required' }}
                    render={({ field: { onChange, onBlur, value } }) => (
                      <TextInput
                        label="Last Name"
                        mode="outlined"
                        value={value}
                        onBlur={onBlur}
                        onChangeText={onChange}
                        error={!!errors.lastName}
                        style={[styles.input, styles.nameInput]}
                      />
                    )}
                    name="lastName"
                  />
                </View>

                <Controller
                  control={control}
                  rules={{
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Please enter a valid email address',
                    },
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Email Address"
                      mode="outlined"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.email}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      style={styles.input}
                      left={<TextInput.Icon icon="email" />}
                    />
                  )}
                  name="email"
                />

                <Controller
                  control={control}
                  rules={{
                    required: 'Phone number is required',
                    pattern: {
                      value: /^[0-9+\-\s()]+$/,
                      message: 'Please enter a valid phone number',
                    },
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Phone Number"
                      mode="outlined"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.phone}
                      keyboardType="phone-pad"
                      style={styles.input}
                      left={<TextInput.Icon icon="phone" />}
                    />
                  )}
                  name="phone"
                />

                <Controller
                  control={control}
                  rules={{
                    required: 'Password is required',
                    minLength: {
                      value: 8,
                      message: 'Password must be at least 8 characters',
                    },
                    pattern: {
                      value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                      message: 'Password must contain uppercase, lowercase, and number',
                    },
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Password"
                      mode="outlined"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.password}
                      secureTextEntry={!showPassword}
                      style={styles.input}
                      left={<TextInput.Icon icon="lock" />}
                      right={
                        <TextInput.Icon
                          icon={showPassword ? 'eye-off' : 'eye'}
                          onPress={() => setShowPassword(!showPassword)}
                        />
                      }
                    />
                  )}
                  name="password"
                />

                <Controller
                  control={control}
                  rules={{
                    required: 'Please confirm your password',
                    validate: (value) =>
                      value === password || 'Passwords do not match',
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Confirm Password"
                      mode="outlined"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.confirmPassword}
                      secureTextEntry={!showConfirmPassword}
                      style={styles.input}
                      left={<TextInput.Icon icon="lock-check" />}
                      right={
                        <TextInput.Icon
                          icon={showConfirmPassword ? 'eye-off' : 'eye'}
                          onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                        />
                      }
                    />
                  )}
                  name="confirmPassword"
                />

                {(errors.firstName || errors.lastName || errors.email || 
                  errors.phone || errors.password || errors.confirmPassword) && (
                  <View style={styles.errorContainer}>
                    {Object.values(errors).map((error, index) => (
                      <Text key={index} style={styles.errorText}>
                        • {error.message}
                      </Text>
                    ))}
                  </View>
                )}

                <View style={styles.checkboxContainer}>
                  <Checkbox
                    status={acceptTerms ? 'checked' : 'unchecked'}
                    onPress={() => setAcceptTerms(!acceptTerms)}
                    color="#10b981"
                  />
                  <Text style={styles.checkboxText}>
                    I agree to the{' '}
                    <Text style={styles.linkText}>Terms of Service</Text> and{' '}
                    <Text style={styles.linkText}>Privacy Policy</Text>
                  </Text>
                </View>

                <Button
                  mode="contained"
                  onPress={handleSubmit(onSubmit)}
                  loading={loading}
                  disabled={loading}
                  style={styles.registerButton}
                  labelStyle={styles.registerButtonText}
                >
                  {loading ? 'Creating Account...' : 'Create Account'}
                </Button>
              </View>
            </Card.Content>
          </Card>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Already have an account? </Text>
            <Button
              mode="text"
              onPress={handleSignIn}
              labelStyle={styles.signInText}
              compact
            >
              Sign In
            </Button>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#10b981',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  card: {
    elevation: 4,
    borderRadius: 12,
    marginBottom: 20,
  },
  form: {
    paddingVertical: 10,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nameInput: {
    flex: 1,
    marginHorizontal: 4,
  },
  input: {
    marginBottom: 16,
  },
  errorContainer: {
    backgroundColor: '#fef2f2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginBottom: 2,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  checkboxText: {
    flex: 1,
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
  },
  linkText: {
    color: '#10b981',
    fontWeight: '600',
  },
  registerButton: {
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#10b981',
  },
  registerButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  footerText: {
    fontSize: 16,
    color: '#6b7280',
  },
  signInText: {
    color: '#10b981',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default RegisterScreen;
