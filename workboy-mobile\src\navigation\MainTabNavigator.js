import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@constants/theme';

// Tab Screens
import DashboardScreen from '@screens/dashboard/DashboardScreen';
import TasksScreen from '@screens/tasks/TasksScreen';
import EarningsScreen from '@screens/earnings/EarningsScreen';
import ProfileScreen from '@screens/profile/ProfileScreen';

// Stack Screens
import TaskDetailScreen from '@screens/tasks/TaskDetailScreen';
import TaskNavigationScreen from '@screens/tasks/TaskNavigationScreen';
import TaskCompletionScreen from '@screens/tasks/TaskCompletionScreen';
import EarningsDetailScreen from '@screens/earnings/EarningsDetailScreen';
import WithdrawalScreen from '@screens/earnings/WithdrawalScreen';
import NotificationsScreen from '@screens/notifications/NotificationsScreen';
import SettingsScreen from '@screens/settings/SettingsScreen';
import HelpScreen from '@screens/help/HelpScreen';
import EditProfileScreen from '@screens/profile/EditProfileScreen';
import DocumentsScreen from '@screens/profile/DocumentsScreen';
import AvailabilityScreen from '@screens/profile/AvailabilityScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Dashboard Stack
const DashboardStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen name="DashboardMain" component={DashboardScreen} />
    <Stack.Screen name="Notifications" component={NotificationsScreen} />
    <Stack.Screen name="TaskDetail" component={TaskDetailScreen} />
    <Stack.Screen name="TaskNavigation" component={TaskNavigationScreen} />
  </Stack.Navigator>
);

// Tasks Stack
const TasksStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen name="TasksMain" component={TasksScreen} />
    <Stack.Screen name="TaskDetail" component={TaskDetailScreen} />
    <Stack.Screen name="TaskNavigation" component={TaskNavigationScreen} />
    <Stack.Screen name="TaskCompletion" component={TaskCompletionScreen} />
  </Stack.Navigator>
);

// Earnings Stack
const EarningsStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen name="EarningsMain" component={EarningsScreen} />
    <Stack.Screen name="EarningsDetail" component={EarningsDetailScreen} />
    <Stack.Screen name="Withdrawal" component={WithdrawalScreen} />
  </Stack.Navigator>
);

// Profile Stack
const ProfileStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen name="ProfileMain" component={ProfileScreen} />
    <Stack.Screen name="EditProfile" component={EditProfileScreen} />
    <Stack.Screen name="Documents" component={DocumentsScreen} />
    <Stack.Screen name="Availability" component={AvailabilityScreen} />
    <Stack.Screen name="Settings" component={SettingsScreen} />
    <Stack.Screen name="Help" component={HelpScreen} />
  </Stack.Navigator>
);

const MainTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Dashboard':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Tasks':
              iconName = focused ? 'list' : 'list-outline';
              break;
            case 'Earnings':
              iconName = focused ? 'wallet' : 'wallet-outline';
              break;
            case 'Profile':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'home-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary[600],
        tabBarInactiveTintColor: colors.gray[400],
        tabBarStyle: {
          backgroundColor: colors.white,
          borderTopWidth: 1,
          borderTopColor: colors.gray[200],
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontFamily: 'Inter-Medium',
          marginTop: -5,
        },
        tabBarIconStyle: {
          marginTop: 5,
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardStack}
        options={{
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen
        name="Tasks"
        component={TasksStack}
        options={{
          tabBarLabel: 'My Tasks',
        }}
      />
      <Tab.Screen
        name="Earnings"
        component={EarningsStack}
        options={{
          tabBarLabel: 'Earnings',
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileStack}
        options={{
          tabBarLabel: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
};

export default MainTabNavigator;
