import React from 'react'
import { useParams } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { ClipboardDocumentListIcon } from '@heroicons/react/24/outline'
import { Card, CardHeader, CardTitle, CardContent } from '@components/ui/Card'

const TaskDetailPage = () => {
  const { id } = useParams()

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Helmet>
        <title>Task Details - WorkBoy Admin</title>
      </Helmet>
      
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
            <ClipboardDocumentListIcon className="mr-3 h-8 w-8 text-primary-600" />
            Task Details
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            Viewing details for task ID: {id}
          </p>
        </div>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Task Information</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500">Task details will be implemented here.</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default TaskDetailPage
