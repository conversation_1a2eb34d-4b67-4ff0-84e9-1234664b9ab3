<?php

namespace App\Models;

use CodeIgniter\Model;

class TaskModel extends Model
{
    protected $table            = 'tasks';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'customer_id',
        'workboy_id',
        'category_id',
        'title',
        'description',
        'task_images',
        'address_id',
        'scheduled_date',
        'scheduled_time',
        'estimated_duration',
        'status',
        'priority',
        'total_amount',
        'tip_amount'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'customer_id'        => 'required|integer',
        'workboy_id'         => 'permit_empty|integer',
        'category_id'        => 'permit_empty|integer',
        'title'              => 'required|max_length[255]',
        'description'        => 'required',
        'task_images'        => 'permit_empty|valid_json',
        'address_id'         => 'required|integer',
        'scheduled_date'     => 'required|valid_date',
        'scheduled_time'     => 'required',
        'estimated_duration' => 'permit_empty|integer|greater_than[0]',
        'status'             => 'permit_empty|in_list[pending,assigned,in_progress,completed,cancelled]',
        'priority'           => 'permit_empty|in_list[low,medium,high]',
        'total_amount'       => 'permit_empty|decimal|greater_than[0]',
        'tip_amount'         => 'permit_empty|decimal|greater_than_equal_to[0]',
    ];

    protected $validationMessages = [
        'customer_id' => [
            'required' => 'Customer ID is required',
        ],
        'title' => [
            'required' => 'Task title is required',
        ],
        'description' => [
            'required' => 'Task description is required',
        ],
        'address_id' => [
            'required' => 'Address is required',
        ],
        'scheduled_date' => [
            'required' => 'Scheduled date is required',
        ],
        'scheduled_time' => [
            'required' => 'Scheduled time is required',
        ],
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['encodeJsonFields'];
    protected $afterInsert    = ['createStatusHistory'];
    protected $beforeUpdate   = ['encodeJsonFields'];
    protected $afterUpdate    = ['updateStatusHistory'];
    protected $beforeFind     = [];
    protected $afterFind      = ['decodeJsonFields'];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Encode JSON fields before saving
     */
    protected function encodeJsonFields(array $data)
    {
        if (isset($data['data']['task_images']) && is_array($data['data']['task_images'])) {
            $data['data']['task_images'] = json_encode($data['data']['task_images']);
        }

        return $data;
    }

    /**
     * Decode JSON fields after retrieval
     */
    protected function decodeJsonFields(array $data)
    {
        if (isset($data['data'])) {
            // Handle single record
            if (isset($data['data']['task_images'])) {
                $data['data']['task_images'] = json_decode($data['data']['task_images'], true) ?? [];
            }
        } else {
            // Handle multiple records
            foreach ($data as &$record) {
                if (isset($record['task_images'])) {
                    $record['task_images'] = json_decode($record['task_images'], true) ?? [];
                }
            }
        }

        return $data;
    }

    /**
     * Create status history after task creation
     */
    protected function createStatusHistory(array $data)
    {
        if (isset($data['id'])) {
            $statusHistoryModel = new TaskStatusHistoryModel();
            $statusHistoryModel->insert([
                'task_id' => $data['id'],
                'status' => $data['data']['status'] ?? 'pending',
                'notes' => 'Task created',
                'created_by' => $data['data']['customer_id'],
            ]);
        }

        return $data;
    }

    /**
     * Update status history after task update
     */
    protected function updateStatusHistory(array $data)
    {
        if (isset($data['id']) && isset($data['data']['status'])) {
            $statusHistoryModel = new TaskStatusHistoryModel();
            $statusHistoryModel->insert([
                'task_id' => $data['id'][0], // ID is in array format during update
                'status' => $data['data']['status'],
                'notes' => 'Status updated',
            ]);
        }

        return $data;
    }

    /**
     * Get tasks by customer ID
     */
    public function getByCustomerId(int $customerId, int $limit = 20, int $offset = 0)
    {
        return $this->where('customer_id', $customerId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get tasks by Work-Boy ID
     */
    public function getByWorkBoyId(int $workboyId, int $limit = 20, int $offset = 0)
    {
        return $this->where('workboy_id', $workboyId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get tasks by status
     */
    public function getByStatus(string $status, int $limit = 20, int $offset = 0)
    {
        return $this->where('status', $status)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get available tasks for Work-Boys
     */
    public function getAvailableTasks(int $limit = 20, int $offset = 0)
    {
        return $this->select('tasks.*, task_categories.name as category_name, task_categories.icon as category_icon,
                             addresses.city, addresses.state, addresses.latitude, addresses.longitude,
                             users.first_name as customer_first_name, users.last_name as customer_last_name')
                   ->join('task_categories', 'task_categories.id = tasks.category_id', 'left')
                   ->join('addresses', 'addresses.id = tasks.address_id')
                   ->join('users', 'users.id = tasks.customer_id')
                   ->where('tasks.status', 'pending')
                   ->where('tasks.scheduled_date >=', date('Y-m-d'))
                   ->orderBy('tasks.priority', 'DESC')
                   ->orderBy('tasks.created_at', 'ASC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get task with full details
     */
    public function getTaskWithDetails(int $taskId)
    {
        return $this->select('tasks.*,
                             task_categories.name as category_name, task_categories.icon as category_icon,
                             addresses.*, 
                             customer.first_name as customer_first_name, customer.last_name as customer_last_name,
                             customer.email as customer_email, customer.phone as customer_phone,
                             workboy.first_name as workboy_first_name, workboy.last_name as workboy_last_name,
                             workboy.email as workboy_email, workboy.phone as workboy_phone')
                   ->join('task_categories', 'task_categories.id = tasks.category_id', 'left')
                   ->join('addresses', 'addresses.id = tasks.address_id')
                   ->join('users customer', 'customer.id = tasks.customer_id')
                   ->join('users workboy', 'workboy.id = tasks.workboy_id', 'left')
                   ->where('tasks.id', $taskId)
                   ->first();
    }

    /**
     * Assign task to Work-Boy
     */
    public function assignTask(int $taskId, int $workboyId)
    {
        return $this->update($taskId, [
            'workboy_id' => $workboyId,
            'status' => 'assigned'
        ]);
    }

    /**
     * Update task status
     */
    public function updateTaskStatus(int $taskId, string $status, array $additionalData = [])
    {
        $updateData = array_merge(['status' => $status], $additionalData);
        return $this->update($taskId, $updateData);
    }

    /**
     * Get tasks by date range
     */
    public function getTasksByDateRange(string $startDate, string $endDate, int $limit = 20, int $offset = 0)
    {
        return $this->where('scheduled_date >=', $startDate)
                   ->where('scheduled_date <=', $endDate)
                   ->orderBy('scheduled_date', 'ASC')
                   ->orderBy('scheduled_time', 'ASC')
                   ->findAll($limit, $offset);
    }

    /**
     * Search tasks
     */
    public function searchTasks(string $query, array $filters = [], int $limit = 20, int $offset = 0)
    {
        $builder = $this->builder();
        
        // Search in title and description
        $builder->groupStart()
                ->like('title', $query)
                ->orLike('description', $query)
                ->groupEnd();

        // Apply filters
        if (isset($filters['status'])) {
            $builder->where('status', $filters['status']);
        }
        
        if (isset($filters['category_id'])) {
            $builder->where('category_id', $filters['category_id']);
        }
        
        if (isset($filters['customer_id'])) {
            $builder->where('customer_id', $filters['customer_id']);
        }
        
        if (isset($filters['workboy_id'])) {
            $builder->where('workboy_id', $filters['workboy_id']);
        }
        
        if (isset($filters['date_from'])) {
            $builder->where('scheduled_date >=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $builder->where('scheduled_date <=', $filters['date_to']);
        }

        return $builder->orderBy('created_at', 'DESC')
                      ->get($limit, $offset)
                      ->getResultArray();
    }

    /**
     * Get task statistics
     */
    public function getTaskStats(array $filters = [])
    {
        $builder = $this->builder();
        
        // Apply filters if provided
        if (isset($filters['customer_id'])) {
            $builder->where('customer_id', $filters['customer_id']);
        }
        
        if (isset($filters['workboy_id'])) {
            $builder->where('workboy_id', $filters['workboy_id']);
        }
        
        if (isset($filters['date_from'])) {
            $builder->where('created_at >=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $builder->where('created_at <=', $filters['date_to']);
        }

        return $builder->select('
                            COUNT(*) as total_tasks,
                            SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_tasks,
                            SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned_tasks,
                            SUM(CASE WHEN status = "in_progress" THEN 1 ELSE 0 END) as in_progress_tasks,
                            SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_tasks,
                            SUM(CASE WHEN status = "cancelled" THEN 1 ELSE 0 END) as cancelled_tasks,
                            AVG(total_amount) as avg_task_amount,
                            SUM(total_amount) as total_revenue
                        ')
                      ->get()
                      ->getRowArray();
    }

    /**
     * Get upcoming tasks
     */
    public function getUpcomingTasks(int $userId = null, string $userType = null, int $limit = 10)
    {
        $builder = $this->select('tasks.*, task_categories.name as category_name, addresses.city')
                       ->join('task_categories', 'task_categories.id = tasks.category_id', 'left')
                       ->join('addresses', 'addresses.id = tasks.address_id')
                       ->where('tasks.scheduled_date >=', date('Y-m-d'))
                       ->whereIn('tasks.status', ['pending', 'assigned', 'in_progress']);

        if ($userId && $userType) {
            if ($userType === 'customer') {
                $builder->where('tasks.customer_id', $userId);
            } elseif ($userType === 'workboy') {
                $builder->where('tasks.workboy_id', $userId);
            }
        }

        return $builder->orderBy('tasks.scheduled_date', 'ASC')
                      ->orderBy('tasks.scheduled_time', 'ASC')
                      ->limit($limit)
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get overdue tasks
     */
    public function getOverdueTasks(int $limit = 20, int $offset = 0)
    {
        $currentDateTime = date('Y-m-d H:i:s');
        
        return $this->select('tasks.*, task_categories.name as category_name, addresses.city,
                             users.first_name as customer_first_name, users.last_name as customer_last_name')
                   ->join('task_categories', 'task_categories.id = tasks.category_id', 'left')
                   ->join('addresses', 'addresses.id = tasks.address_id')
                   ->join('users', 'users.id = tasks.customer_id')
                   ->where("CONCAT(tasks.scheduled_date, ' ', tasks.scheduled_time) <", $currentDateTime)
                   ->whereIn('tasks.status', ['pending', 'assigned', 'in_progress'])
                   ->orderBy('tasks.scheduled_date', 'ASC')
                   ->orderBy('tasks.scheduled_time', 'ASC')
                   ->findAll($limit, $offset);
    }
}
