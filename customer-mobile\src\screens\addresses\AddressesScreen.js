import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  StatusBar,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  IconButton,
  FAB,
  Menu,
  Chip,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { apiService, endpoints } from '@services/apiService';
import { colors, spacing, typography, borderRadius } from '@constants/theme';
import { showToast } from '@utils/toast';

const AddressesScreen = ({ navigation }) => {
  const [addresses, setAddresses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [menuVisible, setMenuVisible] = useState({});

  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(endpoints.customer.addresses);
      
      if (response.success) {
        setAddresses(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch addresses:', error);
      showToast('Failed to load addresses', 'error');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchAddresses();
    setRefreshing(false);
  }, []);

  const handleDeleteAddress = async (addressId) => {
    Alert.alert(
      'Delete Address',
      'Are you sure you want to delete this address?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await apiService.delete(endpoints.customer.deleteAddress(addressId));
              setAddresses(addresses.filter(addr => addr.id !== addressId));
              showToast('Address deleted successfully', 'success');
            } catch (error) {
              console.error('Failed to delete address:', error);
              showToast('Failed to delete address', 'error');
            }
          },
        },
      ]
    );
  };

  const handleSetDefault = async (addressId) => {
    try {
      await apiService.put(endpoints.customer.updateAddress(addressId), {
        is_default: true,
      });
      
      // Update local state
      setAddresses(addresses.map(addr => ({
        ...addr,
        is_default: addr.id === addressId,
      })));
      
      showToast('Default address updated', 'success');
    } catch (error) {
      console.error('Failed to set default address:', error);
      showToast('Failed to update default address', 'error');
    }
  };

  const toggleMenu = (addressId) => {
    setMenuVisible(prev => ({
      ...prev,
      [addressId]: !prev[addressId],
    }));
  };

  const getAddressIcon = (label) => {
    switch (label?.toLowerCase()) {
      case 'home':
        return 'home-outline';
      case 'office':
      case 'work':
        return 'business-outline';
      default:
        return 'location-outline';
    }
  };

  const getAddressIconColor = (label) => {
    switch (label?.toLowerCase()) {
      case 'home':
        return colors.primary[500];
      case 'office':
      case 'work':
        return colors.success[500];
      default:
        return colors.gray[500];
    }
  };

  const formatAddress = (address) => {
    const parts = [
      address.address_line_1,
      address.address_line_2,
      address.city,
      address.state,
      address.postal_code,
    ].filter(Boolean);
    
    return parts.join(', ');
  };

  const renderAddressItem = ({ item }) => (
    <Card style={styles.addressCard}>
      <Card.Content style={styles.addressCardContent}>
        <View style={styles.addressHeader}>
          <View style={styles.addressInfo}>
            <View style={styles.addressTitleRow}>
              <View style={[
                styles.addressIcon,
                { backgroundColor: `${getAddressIconColor(item.label)}20` }
              ]}>
                <Ionicons
                  name={getAddressIcon(item.label)}
                  size={20}
                  color={getAddressIconColor(item.label)}
                />
              </View>
              
              <View style={styles.addressTitleContainer}>
                <Text style={styles.addressLabel}>
                  {item.label || 'Address'}
                </Text>
                {item.is_default && (
                  <Chip
                    mode="outlined"
                    style={styles.defaultChip}
                    textStyle={styles.defaultChipText}
                    icon="star-outline"
                  >
                    Default
                  </Chip>
                )}
              </View>
            </View>
            
            <Text style={styles.addressText} numberOfLines={2}>
              {formatAddress(item)}
            </Text>
            
            {item.latitude && item.longitude && (
              <View style={styles.locationIndicator}>
                <Ionicons name="location" size={14} color={colors.success[500]} />
                <Text style={styles.locationText}>Location saved</Text>
              </View>
            )}
          </View>
          
          <Menu
            visible={menuVisible[item.id] || false}
            onDismiss={() => toggleMenu(item.id)}
            anchor={
              <IconButton
                icon="dots-vertical"
                size={20}
                iconColor={colors.gray[500]}
                onPress={() => toggleMenu(item.id)}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                toggleMenu(item.id);
                navigation.navigate('AddAddress', { address: item });
              }}
              title="Edit"
              leadingIcon="pencil-outline"
            />
            {!item.is_default && (
              <Menu.Item
                onPress={() => {
                  toggleMenu(item.id);
                  handleSetDefault(item.id);
                }}
                title="Set as Default"
                leadingIcon="star-outline"
              />
            )}
            <Menu.Item
              onPress={() => {
                toggleMenu(item.id);
                handleDeleteAddress(item.id);
              }}
              title="Delete"
              leadingIcon="delete-outline"
              titleStyle={{ color: colors.error[600] }}
            />
          </Menu>
        </View>
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="location-outline" size={64} color={colors.gray[400]} />
      <Text style={styles.emptyTitle}>No addresses saved</Text>
      <Text style={styles.emptySubtitle}>
        Add your first address to make booking services easier and faster
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          iconColor={colors.gray[600]}
          onPress={() => navigation.goBack()}
        />
        <Text style={styles.headerTitle}>My Addresses</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Addresses List */}
      <FlatList
        data={addresses}
        renderItem={renderAddressItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
      />

      {/* Tips Card */}
      {addresses.length > 0 && (
        <View style={styles.tipsContainer}>
          <Card style={styles.tipsCard}>
            <Card.Content style={styles.tipsContent}>
              <View style={styles.tipsHeader}>
                <Ionicons name="bulb-outline" size={20} color={colors.primary[600]} />
                <Text style={styles.tipsTitle}>Pro Tips</Text>
              </View>
              <Text style={styles.tipsText}>
                • Add detailed landmarks to help Work-Boys find your location easily{'\n'}
                • Set a default address for quick booking{'\n'}
                • Include apartment/floor numbers for accurate delivery
              </Text>
            </Card.Content>
          </Card>
        </View>
      )}

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('AddAddress')}
        label="Add Address"
        extended
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.gray[900],
  },
  listContainer: {
    padding: spacing.lg,
    paddingBottom: 100, // Space for FAB
  },
  addressCard: {
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
    elevation: 2,
  },
  addressCardContent: {
    paddingVertical: spacing.md,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  addressInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  addressTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  addressIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  addressTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  addressLabel: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
  },
  defaultChip: {
    backgroundColor: colors.primary[50],
    borderColor: colors.primary[200],
    height: 28,
  },
  defaultChipText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.medium,
    color: colors.primary[700],
  },
  addressText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    lineHeight: typography.lineHeight.sm,
    marginBottom: spacing.sm,
  },
  locationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: typography.fontSize.xs,
    fontFamily: typography.fontFamily.medium,
    color: colors.success[600],
    marginLeft: spacing.xs,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing['4xl'],
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.gray[900],
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptySubtitle: {
    fontSize: typography.fontSize.base,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[600],
    textAlign: 'center',
    paddingHorizontal: spacing.xl,
  },
  tipsContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
  },
  tipsCard: {
    backgroundColor: colors.blue[50],
    borderColor: colors.blue[200],
    borderWidth: 1,
    borderRadius: borderRadius.lg,
  },
  tipsContent: {
    paddingVertical: spacing.md,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  tipsTitle: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.primary[700],
    marginLeft: spacing.sm,
  },
  tipsText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.blue[700],
    lineHeight: typography.lineHeight.sm,
  },
  fab: {
    position: 'absolute',
    margin: spacing.lg,
    right: 0,
    bottom: 0,
    backgroundColor: colors.primary[600],
  },
});

export default AddressesScreen;
