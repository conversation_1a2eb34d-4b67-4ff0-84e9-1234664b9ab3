<?php

namespace App\Controllers\Api\V1;

use App\Controllers\Api\BaseApiController;
use App\Models\UserModel;
use App\Models\TaskModel;
use App\Models\PaymentModel;
use App\Models\ReviewModel;
use App\Models\WorkboyProfileModel;
use App\Models\CustomerProfileModel;

/**
 * Admin Controller for Work-Boy Booking API
 * 
 * Handles admin dashboard, analytics, user management, and platform oversight
 */
class AdminController extends BaseApiController
{
    protected $userModel;
    protected $taskModel;
    protected $paymentModel;
    protected $reviewModel;
    protected $workboyProfileModel;
    protected $customerProfileModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new UserModel();
        $this->taskModel = new TaskModel();
        $this->paymentModel = new PaymentModel();
        $this->reviewModel = new ReviewModel();
        $this->workboyProfileModel = new WorkboyProfileModel();
        $this->customerProfileModel = new CustomerProfileModel();
    }

    /**
     * Get admin dashboard data
     * GET /api/v1/admin/dashboard
     */
    public function dashboard()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            // Get overview statistics
            $userStats = [
                'total_users' => $this->userModel->countAllResults(),
                'active_customers' => $this->userModel->getActiveUsersCount('customer'),
                'active_workboys' => $this->userModel->getActiveUsersCount('workboy'),
                'pending_kyc' => $this->workboyProfileModel->where('kyc_status', 'pending')->countAllResults(),
            ];

            $taskStats = $this->taskModel->getTaskStats();
            $paymentStats = $this->paymentModel->getPaymentStats();

            // Get recent activities
            $recentTasks = $this->taskModel->select('tasks.*, customers.first_name as customer_name, workboys.first_name as workboy_name')
                                         ->join('users customers', 'customers.id = tasks.customer_id')
                                         ->join('users workboys', 'workboys.id = tasks.workboy_id', 'left')
                                         ->orderBy('tasks.created_at', 'DESC')
                                         ->limit(5)
                                         ->findAll();

            $recentPayments = $this->paymentModel->select('payments.*, tasks.title as task_title, customers.first_name as customer_name')
                                                ->join('tasks', 'tasks.id = payments.task_id')
                                                ->join('users customers', 'customers.id = payments.customer_id')
                                                ->orderBy('payments.created_at', 'DESC')
                                                ->limit(5)
                                                ->findAll();

            $recentReviews = $this->reviewModel->getRecentReviews(5);

            // Get daily revenue for the last 7 days
            $dailyRevenue = $this->paymentModel->getDailyRevenue(7);

            $dashboardData = [
                'overview' => [
                    'users' => $userStats,
                    'tasks' => $taskStats,
                    'payments' => $paymentStats,
                ],
                'recent_activities' => [
                    'tasks' => $recentTasks,
                    'payments' => $recentPayments,
                    'reviews' => $recentReviews,
                ],
                'revenue_trend' => $dailyRevenue,
            ];

            return $this->apiResponse($dashboardData, 'Admin dashboard data retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Admin dashboard error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve dashboard data');
        }
    }

    /**
     * Get users list with filters
     * GET /api/v1/admin/users
     */
    public function getUsers()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $pagination = $this->getPaginationParams();
            $userType = $this->request->getGet('user_type');
            $status = $this->request->getGet('status');
            $search = $this->request->getGet('search');

            $builder = $this->userModel->builder();

            // Apply filters
            if ($userType) {
                $builder->where('user_type', $userType);
            }

            if ($status) {
                $builder->where('status', $status);
            }

            if ($search) {
                $builder->groupStart()
                        ->like('first_name', $search)
                        ->orLike('last_name', $search)
                        ->orLike('email', $search)
                        ->groupEnd();
            }

            // Get total count for pagination
            $total = $builder->countAllResults(false);

            // Get users with additional stats
            $users = $builder->select('users.*, 
                                     COUNT(DISTINCT t.id) as total_tasks,
                                     SUM(CASE WHEN p.payment_status = "completed" THEN p.amount + p.tip_amount ELSE 0 END) as total_spent')
                           ->join('tasks t', 't.customer_id = users.id OR t.workboy_id = users.id', 'left')
                           ->join('payments p', 'p.customer_id = users.id OR p.workboy_id = users.id', 'left')
                           ->groupBy('users.id')
                           ->orderBy('users.created_at', 'DESC')
                           ->limit($pagination['limit'], $pagination['offset'])
                           ->get()
                           ->getResultArray();

            // Remove sensitive data
            foreach ($users as &$user) {
                unset($user['firebase_uid']);
            }

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($users, 'Users retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Admin get users error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve users');
        }
    }

    /**
     * Update user status
     * PUT /api/v1/admin/users/{id}/status
     */
    public function updateUserStatus($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $user = $this->userModel->find($id);

            if (!$user) {
                return $this->notFoundResponse('User not found');
            }

            $rules = [
                'status' => 'required|in_list[active,inactive,suspended,pending_verification]',
                'reason' => 'permit_empty|max_length[500]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            $updated = $this->userModel->updateUserStatus($id, $data['status']);

            if (!$updated) {
                return $this->serverErrorResponse('Failed to update user status');
            }

            $this->logActivity('user_status_updated', [
                'target_user_id' => $id,
                'old_status' => $user['status'],
                'new_status' => $data['status'],
                'reason' => $data['reason'] ?? null
            ]);

            return $this->apiResponse(null, 'User status updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Admin update user status error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to update user status');
        }
    }

    /**
     * Get all tasks with filters
     * GET /api/v1/admin/tasks
     */
    public function getTasks()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $pagination = $this->getPaginationParams();
            $status = $this->request->getGet('status');
            $categoryId = $this->request->getGet('category_id');
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');

            $filters = [];
            if ($status) $filters['status'] = $status;
            if ($categoryId) $filters['category_id'] = $categoryId;
            if ($dateFrom) $filters['date_from'] = $dateFrom;
            if ($dateTo) $filters['date_to'] = $dateTo;

            $tasks = $this->taskModel->searchTasks('', $filters, $pagination['limit'], $pagination['offset']);

            // Get total count for pagination
            $total = $this->taskModel->searchTasks('', $filters, 999999, 0);
            $totalCount = count($total);

            $meta = $this->createPaginationMeta($totalCount, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($tasks, 'Tasks retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Admin get tasks error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve tasks');
        }
    }

    /**
     * Get all payments with filters
     * GET /api/v1/admin/payments
     */
    public function getPayments()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $pagination = $this->getPaginationParams();
            $status = $this->request->getGet('status');
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');

            $builder = $this->paymentModel->select('payments.*, tasks.title as task_title,
                                                  customers.first_name as customer_name, customers.last_name as customer_last_name,
                                                  workboys.first_name as workboy_name, workboys.last_name as workboy_last_name')
                                         ->join('tasks', 'tasks.id = payments.task_id')
                                         ->join('users customers', 'customers.id = payments.customer_id')
                                         ->join('users workboys', 'workboys.id = payments.workboy_id', 'left');

            // Apply filters
            if ($status) {
                $builder->where('payments.payment_status', $status);
            }

            if ($dateFrom) {
                $builder->where('payments.created_at >=', $dateFrom);
            }

            if ($dateTo) {
                $builder->where('payments.created_at <=', $dateTo);
            }

            // Get total count for pagination
            $total = $builder->countAllResults(false);

            // Get payments
            $payments = $builder->orderBy('payments.created_at', 'DESC')
                              ->limit($pagination['limit'], $pagination['offset'])
                              ->get()
                              ->getResultArray();

            $meta = $this->createPaginationMeta($total, $pagination['page'], $pagination['limit']);

            return $this->apiResponse($payments, 'Payments retrieved successfully', 200, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Admin get payments error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve payments');
        }
    }

    /**
     * Get analytics data
     * GET /api/v1/admin/analytics
     */
    public function getAnalytics()
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $period = $this->request->getGet('period') ?? '30'; // days
            $dateFrom = date('Y-m-d', strtotime("-{$period} days"));
            $dateTo = date('Y-m-d');

            // User analytics
            $userRegistrations = $this->userModel->select('DATE(created_at) as date, user_type, COUNT(*) as count')
                                                ->where('created_at >=', $dateFrom)
                                                ->groupBy('DATE(created_at), user_type')
                                                ->orderBy('date', 'ASC')
                                                ->findAll();

            // Task analytics
            $taskTrends = $this->taskModel->select('DATE(created_at) as date, status, COUNT(*) as count')
                                        ->where('created_at >=', $dateFrom)
                                        ->groupBy('DATE(created_at), status')
                                        ->orderBy('date', 'ASC')
                                        ->findAll();

            // Revenue analytics
            $revenueData = $this->paymentModel->getDailyRevenue((int) $period);

            // Category analytics
            $categoryStats = $this->taskModel->select('tc.name as category_name, COUNT(tasks.id) as task_count, 
                                                     AVG(tasks.total_amount) as avg_amount')
                                            ->join('task_categories tc', 'tc.id = tasks.category_id', 'left')
                                            ->where('tasks.created_at >=', $dateFrom)
                                            ->groupBy('tasks.category_id')
                                            ->orderBy('task_count', 'DESC')
                                            ->findAll();

            // Work-Boy performance
            $topWorkBoys = $this->reviewModel->getTopRatedWorkBoys(10);

            // Platform metrics
            $platformMetrics = [
                'total_revenue' => $this->paymentModel->where('payment_status', 'completed')
                                                    ->where('created_at >=', $dateFrom)
                                                    ->selectSum('amount + tip_amount', 'total')
                                                    ->get()
                                                    ->getRowArray()['total'] ?? 0,
                'avg_task_value' => $this->taskModel->where('status', 'completed')
                                                  ->where('created_at >=', $dateFrom)
                                                  ->selectAvg('total_amount')
                                                  ->get()
                                                  ->getRowArray()['total_amount'] ?? 0,
                'completion_rate' => $this->calculateCompletionRate($dateFrom, $dateTo),
                'customer_retention' => $this->calculateCustomerRetention($dateFrom, $dateTo),
            ];

            $analyticsData = [
                'user_registrations' => $userRegistrations,
                'task_trends' => $taskTrends,
                'revenue_data' => $revenueData,
                'category_stats' => $categoryStats,
                'top_workboys' => $topWorkBoys,
                'platform_metrics' => $platformMetrics,
            ];

            return $this->apiResponse($analyticsData, 'Analytics data retrieved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Admin get analytics error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to retrieve analytics data');
        }
    }

    /**
     * Approve Work-Boy KYC
     * POST /api/v1/admin/workboy/{id}/approve-kyc
     */
    public function approveKYC($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $workboy = $this->workboyProfileModel->getByUserId($id);

            if (!$workboy) {
                return $this->notFoundResponse('Work-Boy not found');
            }

            if ($workboy['kyc_status'] !== 'pending') {
                return $this->errorResponse('KYC is not pending approval', 400);
            }

            $updated = $this->workboyProfileModel->updateKYCStatus($id, 'approved');

            if (!$updated) {
                return $this->serverErrorResponse('Failed to approve KYC');
            }

            $this->logActivity('kyc_approved', [
                'workboy_id' => $id,
                'approved_by' => $currentUser['id']
            ]);

            return $this->apiResponse(null, 'KYC approved successfully');

        } catch (\Exception $e) {
            log_message('error', 'Admin approve KYC error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to approve KYC');
        }
    }

    /**
     * Reject Work-Boy KYC
     * POST /api/v1/admin/workboy/{id}/reject-kyc
     */
    public function rejectKYC($id = null)
    {
        try {
            $currentUser = $this->request->user;

            if (!$currentUser || $currentUser['user_type'] !== 'admin') {
                return $this->forbiddenResponse('Admin access required');
            }

            $workboy = $this->workboyProfileModel->getByUserId($id);

            if (!$workboy) {
                return $this->notFoundResponse('Work-Boy not found');
            }

            if ($workboy['kyc_status'] !== 'pending') {
                return $this->errorResponse('KYC is not pending approval', 400);
            }

            $rules = [
                'reason' => 'required|max_length[500]',
            ];

            $validation = $this->validateRequest($rules);
            if ($validation !== true) {
                return $this->validationErrorResponse($validation);
            }

            $data = $this->request->getJSON(true);

            $updated = $this->workboyProfileModel->updateKYCStatus($id, 'rejected');

            if (!$updated) {
                return $this->serverErrorResponse('Failed to reject KYC');
            }

            $this->logActivity('kyc_rejected', [
                'workboy_id' => $id,
                'rejected_by' => $currentUser['id'],
                'reason' => $data['reason']
            ]);

            return $this->apiResponse(null, 'KYC rejected successfully');

        } catch (\Exception $e) {
            log_message('error', 'Admin reject KYC error: ' . $e->getMessage());
            return $this->serverErrorResponse('Failed to reject KYC');
        }
    }

    /**
     * Calculate completion rate
     */
    private function calculateCompletionRate(string $dateFrom, string $dateTo): float
    {
        $totalTasks = $this->taskModel->where('created_at >=', $dateFrom)
                                    ->where('created_at <=', $dateTo)
                                    ->countAllResults();

        $completedTasks = $this->taskModel->where('created_at >=', $dateFrom)
                                        ->where('created_at <=', $dateTo)
                                        ->where('status', 'completed')
                                        ->countAllResults();

        return $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;
    }

    /**
     * Calculate customer retention rate
     */
    private function calculateCustomerRetention(string $dateFrom, string $dateTo): float
    {
        // Simplified retention calculation
        $totalCustomers = $this->userModel->where('user_type', 'customer')
                                        ->where('created_at <', $dateFrom)
                                        ->countAllResults();

        $activeCustomers = $this->taskModel->select('DISTINCT customer_id')
                                         ->where('created_at >=', $dateFrom)
                                         ->where('created_at <=', $dateTo)
                                         ->countAllResults();

        return $totalCustomers > 0 ? round(($activeCustomers / $totalCustomers) * 100, 2) : 0;
    }
}
